server:
  port: 8080
  servlet:
    context-path: /ps2-file-processing

spring:
  application:
    name: ps2-file-processing
  jpa:
    hibernate.ddl-auto: none
    properties.hibernate.dialect: org.hibernate.dialect.Oracle12cDialect
  datasource:
    driver-class-name: oracle.jdbc.driver.OracleDriver
    url: ${JDBC_URL}
    username: ${J<PERSON>BC_USERNAME}
    password: ${JDBC_PASSWORD}

sftp-server:
  protocol: sftp
  host: ${SFTP_HOST:localhost}
  port: ${SFTP_PORT:2022}
  username: ${SFTP_USERNAME:}
  password: ${SFTP_PASSWORD:}
  base-folder: ${SFTP_BASE_FOLDER:/reconcile/LoyaltyRewardtcb-lake-streamingdata/dev}
  known-hosts-path: ${KNOWN_HOSTS_PATH:./conf/known_hosts}
  strict-host-key-checking: no
  private-key-passphrase: ${SFTP_KEY_PASSPHRASE:}
  private-key-path: ${SFTP_PRIVATE_KEY_PATH:}
  pgp-enable: ${SFTP_PGP_ENABLE:false}
  pgp-private-key-path: ${SFTP_PGP_PRIVATE_KEY_PATH:keys/pgp_private-key.asc}
  pgp-public-key-path: ${SFTP_PGP_PUBLIC_KEY_PATH:keys/pgp_public-key.asc}
  pgp-private-key-passphrase: ${SFTP_PGP_PRIVATE_KEY_PASSPHRASE:123456}
  pgp-private-key-out-path: ${SFTP_PGP_PRIVATE_KEY_OUT_PATH:./conf/pgp_private-key.asc}
  pgp-public-key-out-path: ${SFTP_PGP_PUBLIC_KEY_OUT_PATH:./conf/pgp_public-key.asc}
  pgp-private-key-out-passphrase: ${SFTP_PGP_PRIVATE_KEY_OUT_PASSPHRASE:123456}
  output-folder: ${SFTP_PGP_OUTPUT_FOLDER:/reconcile/LoyaltyRewardtcb-lake-streamingdata/datalake/output}
  pgp-control-out-enable: ${SFTP_PGP_OUT_CONTROL_ENABLE:false}
  pgp-data-out-enable: ${SFTP_PGP_OUT_DATA_ENABLE:false}
  pgp-delay-time: ${SFTP_DELAY_TIME:3000}

internal-sftp-server:
  protocol: ${INTERNAL_SFTP_PROTOCOL:sftp}
  host: ${INTERNAL_SFTP_HOST:localhost}
  port: ${INTERNAL_SFTP_PORT:2022}
  username: ${INTERNAL_SFTP_USERNAME:}
  password: ${INTERNAL_SFTP_PASSWORD:}
  base-folder: ${INTERNAL_SFTP_BASE_FOLDER:/reconcile/LoyaltyRewardtcb-lake-streamingdata/dev/internal}
  known-hosts-path: ${INTERNAL_SFTP_KNOWN_HOSTS_PATH:./conf/known_hosts}
  strict-host-key-checking: no
  private-key-passphrase: ${INTERNAL_SFTP_KEY_PASSPHRASE:}
  private-key-path: ${INTERNAL_SFTP_PRIVATE_KEY_PATH:}
  pgp-enable: ${INTERNAL_SFTP_PGP_ENABLE:false}
  pgp-delay-time: ${INTERNAL_SFTP_DELAY_TIME:3000}

app:
  performance:
    threads:
      max: ${APP_PERFORMANCE_THREADS:8}
      onboard: ${APP_PERFORMANCE_ONBOARD_THREADS:2}
      customer: ${APP_PERFORMANCE_CUSTOMER_THREADS:2}
      transaction: ${APP_PERFORMANCE_TRANSACTION_THREADS:4}
      event: ${APP_PERFORMANCE_EVENT_THREADS:8}
    queues: ${APP_PERFORMANCE_QUEUES:1024}

  enable-earning-versioning: ${APP_ENABLE_EARNING_VERSIONING:true}
  business-code: ${APP_BUSINESS_CODE:TCB}
  program-code: ${APP_PROGRAM_CODE:TCBC}
  corporation-code: ${APP_CORPORATION_CODE:TCBLOYALTY}
  store-code:
    earn-txn: ${APP_STORE_CODE_EARN_TXN:TCB_TXN}
    earn-event: ${APP_STORE_CODE_EARN_EVENT:TCB_EVENT}
  pos-code:
    earn-txn: ${APP_POS_CODE_EARN_TXN:TCB_TXN}
    earn-event: ${APP_POS_CODE_EARN_EVENT:TCB_EVENT}
  currency-code: ${APP_CURRENCY_CODE:VND}
  service-code:
    earn-txn: ${APP_SERVICE_CODE_EARN_TXN:TXN_EARNING}
    revoke-txn: ${APP_SERVICE_CODE_REVOKE_TXN:TXN_REVERT}
    earn-event: ${APP_SERVICE_CODE_EARN_EVENT:EVENT_EARNING}
    refund: ${APP_SERVICE_CODE_REFUND:REFUND}
  channel-code: ${APP_CHANEL_CODE:STREAMING_WORKER}
  reason-code: ${APP_REASON_CODE:RFD}
  end-of-month-dates: ${END_OF_MONTH_DATES:31/01/2024, 29/02/2024, 31/03/2024, 30/04/2024, 31/05/2024, 30/06/2024, 31/07/2024, 31/08/2024, 30/09/2024, 31/10/2024, 30/11/2024, 31/12/2024}
  casa-event-code: ${CASA_EVENT_CODE:120520}
  eop-event-code: ${EOP_EVENT_CODE:120533}
  product-casa-code: ${PRODUCT_CASA_CODE:RETAIL_CASA}
  casa-booster-invoice-no-format: ${CASA_BOOSTER_INVOICE_NO_FORMAT:%s-2}
  retry-transaction-page-size: ${RETRY_TRANSACTION_PAGE_SIZE:100}
  oneloyalty:
    service:
      base-url: ${OL_SERVICE_BASE_URL:https://api-qc.int.vinid.dev/oneloyalty-service}
    escrow-point:
      base-url: ${OL_ESCROW_POINT_BASE_URL:http://oneloyalty-escrow-point-service/oneloyalty-escrow-point-service}
    check-fraud:
      base-url: ${CHECK_FRAUD_BASE_URL:https://api-payment-qc.int.vinid.dev/ps2/risk-fraud/internal}
      basic-auth: ${CHECK_FRAUD_BASIC_AUTH:}
    scale:
      base-url: ${SCALE_BASE_URL:http://oneloyalty-scale/api/v1}
  enable-event-gami: ${APP_ENABLE_EVENT_GAMI:false}
  enable-earning-processing: ${APP_ENABLE_EARNING_PROCESSING:true}
  scale:
    enable: ${APP_ENABLE_SCALE_POD:false}
    service-scale-up: ${APP_UP_SERVICE_NAME:earn}
    service-scale-down: ${APP_DOWN_SERVICE_NAME:done}
    poll-interval-ms: ${APP_POLL_INTERVAL_MS:30000}
    wait-duration-ms: ${APP_WAIT_DURATION_MS:1800000}
    time-out: ${APP_SCALE_TIME_OUT_MS:2400000}

# Kafka
kafka:
  group.id: ${GROUP_ID:ps2-loyalty-event-worker-dev}
  listener.group.id: ${LISTENER_GROUP_ID:ps2-event-transaction-listener-status-consume-group-dev}-${POD_NAME:ps2-transaction-worker-0}
  topic:
    trans-earn-consume: ${TOPIC_NAME_TRANS_EARN_CONSUME:ps2-event-transaction-earn-point-consume-dev}
    trans-listener-status: ${TOPIC_NAME_TRANSACTION_LISTENER_STATUS:ps2-event-transaction-listener-status-dev}
    ps2-sync-audit-trail-transaction: ${TOPIC_NAME_PS2_SYNC_AUDIT_TRAIL_TRANSACTION:ps2-sync-audit-trail-transaction-qc}
    trans-gami-produce: ${TOPIC_NAME_TRANS_GAMI_PRODUCE:ps2-oneloyalty-event-transactions-raw-qc}
    earn-event-gami-produce: ${TOPIC_NAME_EARN_EVENT_GAMI_PRODUCE:ps2-oneloyalty-earn-event-raw-qc}
    cus-info-produce: ${TOPIC_NAME_CUS_INFO_PRODUCE:ps2-event-customer-info-produce-dev}
    customer-profile-produce: ${TOPIC_NAME_CUSTOMER_PROFILE_PRODUCE:ps2-customer-profile-update-qc}
  type: ${KAFKA_TYPE:EXTERNAL}

  confluent:
    enabled: ${KAFKA_CONFLUENT_ENABLED:true}
    bootstrap-servers: ${KAFKA_CONFLUENT_BOOTSTRAP_ADDRESS:confluent-kafka-tw-1.int.onemount.dev:9093,confluent-kafka-tw-2.int.onemount.dev:9093,confluent-kafka-tw-3.int.onemount.dev:9093,confluent-kafka-sg-4.int.onemount.dev:9093,confluent-kafka-sg-5.int.onemount.dev:9093,confluent-kafka-sg-6.int.onemount.dev:9093}
    truststore:
      location: ${KAFKA_CONFLUENT_TRUSTSTORE_LOCATION:cert/confluent-truststore.jks}
      password: ${KAFKA_CONFLUENT_TRUSTSTORE_PASSWORD:}
    keystore:
      location: ${KAFKA_CONFLUENT_KEYSTORE_LOCATION:cert/oneloyalty-common-clients.int.onemount.dev.jks}
      password: ${KAFKA_CONFLUENT_KEYSTORE_PASSWORD:}
    key:
      password: ${KAFKA_CONFLUENT_KEY_PASSWORD:}
    ssl-enable: ${KAFKA_CONFLUENT_SSL_ENABLE:true}
    produce-enable: ${KAFKA_CONFLUENT_PRODUCER_ENABLE:true}

  ol-service:
    enabled: ${KAFKA_OL_SERVICE_ENABLED:false}
    group:id: ${KAFKA_OL_SERVICE_CONSUMER_GROUP:oneloyalty-ps2-event-notification-worker-dev}
    bootstrap-servers: ${KAFKA_OL_SERVICE_BOOTSTRAP_ADDRESS:confluent-kafka-tw-1.int.onemount.dev:9093,confluent-kafka-tw-2.int.onemount.dev:9093,confluent-kafka-tw-3.int.onemount.dev:9093,confluent-kafka-sg-4.int.onemount.dev:9093,confluent-kafka-sg-5.int.onemount.dev:9093,confluent-kafka-sg-6.int.onemount.dev:9093}
    topic:
      name.common-events: ${KAFKA_OL_SERVICE_CONSUMER_TOPIC:oneloyalty-common-events-dev}
    truststore:
      location: ${KAFKA_OL_SERVICE_TRUSTSTORE_LOCATION:}
      password: ${KAFKA_OL_SERVICE_TRUSTSTORE_PASSWORD:}
    keystore:
      location: ${KAFKA_OL_SERVICE_KEYSTORE_LOCATION:}
      password: ${KAFKA_OL_SERVICE_KEYSTORE_PASSWORD:}
    key.password: ${KAFKA_OL_SERVICE_KEY_PASSWORD:}
    ssl-enable: ${KAFKA_SSL_ENABLE:false}

# Kafka TCB
kafka-external:
  enable: ${KAFKA_EXTERNAL_ENABLED:true}
  max-concurrency: ${KAFKA_EXTERNAL_MAX_CONCURRENCY:16}
  max-pool-records: ${KAFKA_EXTERNAL_MAX_POOL_RECORDS:48}
  group:
    id: ${EXTERNAL_GROUP_ID:cloud-ps2-event-sit-loyalty-dev-01}
  topic:
    cus-info-consume: ${EXTERNAL_TOPIC_NAME_CUS_INFO_CONSUME:cloud-ps2-event-sit-loyalty-member-01}
    cus-info-produce: ${EXTERNAL_TOPIC_NAME_CUS_INFO_PRODUCE:cloud-ps2-event-sit-loyalty-member-resp-01}
    update-customer-consume: ${EXTERNAL_TOPIC_NAME_UPDATE_CUSTOMER_CONSUME:cloud-ps2-event-sit-customer-info-01}
    update-customer-produce: ${EXTERNAL_TOPIC_NAME_UPDATE_CUSTOMER_PRODUCE:cloud-ps2-event-sit-customer-info-resp-01}

  bootstrap-servers: ${KAFKA_EXTERNAL_BOOTSTRAP_ADDRESS:confluent-kafka-tw-1.int.onemount.dev:9093}
  properties:
    sasl:
      jaas:
        template: ${KAFKA_EXTERNAL_SASL_JAAS_TEMPLATE:org.apache.kafka.common.security.scram.ScramLoginModule required username="%s" password="%s";}
        username: ${KAFKA_EXTERNAL_SASL_JAAS_USERNAME:srv_omc_msk}
        password: ${KAFKA_EXTERNAL_SASL_JAAS_PASSWORD:}
        mechanism: ${KAFKA_EXTERNAL_SASL_MECHANISM:SCRAM-SHA-512}
    security:
      protocol: ${KAFKA_EXTERNAL_SECURITY_PROTOCOL:SASL_SSL}
      enable: ${KAFKA_EXTERNAL_SECURITY_ENABLE:true}
  ssl:
    enable: ${KAFKA_EXTERNAL_SSL_ENABLE:true}
    client:
      auth: ${KAFKA_EXTERNAL_SSL_CLIENT_AUTH:none}
    protocol: ${KAFKA_EXTERNAL_SSL_PROTOCOL:TLSv1.2}
    truststore:
      location: ${KAFKA_EXTERNAL_TRUSTSTORE_LOCATION:cert/confluent-truststore.jks}
      password: ${KAFKA_EXTERNAL_TRUSTSTORE_PASSWORD:}
    keystore:
      location: ${KAFKA_EXTERNAL_KEYSTORE_LOCATION:cert/oneloyalty-common-clients.int.onemount.dev.jks}
      password: ${KAFKA_EXTERNAL_KEYSTORE_PASSWORD:}
    key:
      password: ${KAFKA_EXTERNAL_KEY_PASSWORD:}

http:
  client:
    max-retries-execution: ${HTTP_CLIENT_MAX_RETRIES:3}
    initial-retry-interval: ${HTTP_CLIENT_INITIAL_INTERVAL:5000}
    max-retry-interval: ${HTTP_CLIENT_MAX_INTERVAL:10000}
    retry-multiplier: ${HTTP_CLIENT_RETRY_MULTIPLIER:2.0}
