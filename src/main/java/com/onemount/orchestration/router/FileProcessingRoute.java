package com.onemount.orchestration.router;

import com.oneid.oneloyalty.common.constant.EFileType;
import com.onemount.orchestration.constant.Constant;
import com.onemount.orchestration.constant.EErrorCode;
import com.onemount.orchestration.constant.EProcessServiceId;
import com.onemount.orchestration.exception.DataFileProcessException;
import com.onemount.orchestration.exception.DataFileValidationException;
import com.onemount.orchestration.exception.DecryptControlFileException;
import com.onemount.orchestration.exception.DecryptDataFileException;
import com.onemount.orchestration.exception.DeserializeControlFileException;
import com.onemount.orchestration.processor.ContentProcessor;
import com.onemount.orchestration.processor.ControlFileHistoryProcessor;
import com.onemount.orchestration.processor.ControlFileProcessor;
import com.onemount.orchestration.processor.DataFileHistoryProcessor;
import com.onemount.orchestration.processor.DeserializeProcessor;
import com.onemount.orchestration.processor.GCProcessor;
import com.onemount.orchestration.processor.PreProcessControlFileProcessor;
import com.onemount.orchestration.processor.PreProcessDataFileProcessor;
import com.onemount.orchestration.processor.ReadFileContentProcessor;
import com.onemount.orchestration.processor.ScaleProcessor;
import com.onemount.orchestration.processor.ThreadOrderProcessor;
import com.onemount.orchestration.processor.UploadControlFileProcessor;
import com.onemount.orchestration.processor.UploadDataFileProcessor;
import com.onemount.orchestration.strategy.DataFileAggregationStrategy;
import com.onemount.orchestration.support.remote.SFTPConfig;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.LoggingLevel;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.dataformat.PGPDataFormat;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;

// @formatter:off
@Component
@RequiredArgsConstructor
public class FileProcessingRoute extends RouteBuilder {

    private final SFTPConfig sftpConfig;
    private final SFTPConfig internalSftpConfig;
    private final ControlFileProcessor controlFileProcessor;
    private final ReadFileContentProcessor readFileContentProcessor;
    private final ContentProcessor contentProcessor;
    private final ControlFileHistoryProcessor controlFileHistoryProcessor;
    private final DataFileHistoryProcessor dataFileHistoryProcessor;
    private final PreProcessControlFileProcessor preProcessControlFileProcessor;
    private final PreProcessDataFileProcessor preProcessDataFileProcessor;
    private final DeserializeProcessor deserializeProcessor;
    private final ThreadOrderProcessor threadOrderProcessor;
    private final ScaleProcessor scaleProcessor;
    private final GCProcessor gcProcessor;
    private final UploadControlFileProcessor uploadControlFileProcessor;
    private final UploadDataFileProcessor uploadDataFileProcessor;
    private final DataFileAggregationStrategy aggregationStrategy;

    @Value("${app.performance.threads:8}")
    private Integer threadsNum;
    @Value("${app.performance.threads.onboard:2}")
    private Integer onboardThreadsNum;
    @Value("${app.performance.threads.customer:2}")
    private Integer customerThreadsNum;
    @Value("${app.performance.threads.transaction:4}")
    private Integer transactionThreadsNum;
    @Value("${app.performance.threads.event:8}")
    private Integer eventThreadsNum;

    @Value("${app.performance.queues:1000}")
    private Integer queueSize;

    // Used to wait for the data file processing to complete
    private final Map<String, CountDownLatch> countDownLatchMap = new ConcurrentHashMap<>();

    private URI sftpURI() throws URISyntaxException {
        return buildSftpUri(sftpConfig);
    }

    private URI sftpInternalURI() throws URISyntaxException {
        return buildSftpUri(internalSftpConfig);
    }

    @Override
    public void configure() throws Exception {

        PGPDataFormat pgpFormat = pgpEncrypt();

        /*
            Process flow

            1. Listen for a new control file
                - Read the control file and extract the list of data files into the body.
                - Sort the data files in the following order: onboard → customer → transaction → event.

            2.Move to file processing
                - Iterate through the sorted list of data files and process them sequentially.

            3. Decrypt each data file using PGP and read its content.
            4. Split the file content into lines using tokenization.
            5. Process each line in parallel for optimized performance.
         */

        /*
         *  Exception decrypt control file
         */
        onException(DecryptControlFileException.class)
                .handled(true)
                .setBody(constant(EErrorCode.FILE_DECRYPT_FAILED))
                .process(controlFileHistoryProcessor::processFormatError)
                .stop();

        /*
         *  Exception deserialize control file
         */
        onException(DeserializeControlFileException.class)
                .handled(true)
                .setBody(constant(EErrorCode.FILE_FORMAT_INVALID))
                .process(controlFileHistoryProcessor::processFormatError)
                .stop();

        /*
         *  Exception verify list data file
         */
        onException(DataFileValidationException.class)
                .handled(true)
                .setBody(constant(EErrorCode.DATA_FILE_ERROR))
                .process(controlFileHistoryProcessor::processError)
                .stop();

        /*
         *  Exception decrypt data file
         */
        onException(DecryptDataFileException.class)
                .handled(true)
                .setBody(constant(EErrorCode.FILE_DECRYPT_FAILED))
                .process(dataFileHistoryProcessor::processError)
                .setBody(constant(EErrorCode.PROCESS_DATA_FILE_FAILED))
                .process(controlFileHistoryProcessor::processError)
                .stop();

        /*
         *  Exception process data file
         */
        onException(DataFileProcessException.class)
                .handled(true)
                .process(exchange -> {
                    DataFileProcessException exception
                            = exchange.getProperty(Exchange.EXCEPTION_CAUGHT, DataFileProcessException.class);
                    if (exception != null && exception.getErrorCode() != null){
                        exchange.getIn().setBody(exception.getErrorCode());
                    } else {
                        exchange.getIn().setBody(EErrorCode.PROCESS_DATA_FILE_FAILED);
                    }
                })
                .process(dataFileHistoryProcessor::processError)
                .setBody(constant(EErrorCode.PROCESS_DATA_FILE_FAILED))
                .process(controlFileHistoryProcessor::processError)
                .stop();

        /*
         *  General exception
         */
        onException(Exception.class)
                .handled(true)
                .log(LoggingLevel.ERROR, "Exception while processing file: ${header.CamelFileName}")
                .log(LoggingLevel.ERROR, "StackTrace:\n ${exception.stacktrace}")
                .stop();

        /*
            Route to process the Control File
            Listens for new files from the server and reprocesses pending or failed control files
         */
        from(sftpURI().toString())
            .to("direct:streamFile");

        from(sftpInternalURI().toString())
            .to("direct:streamFile");

        from("direct:streamFile")
            .routeId("process-read-file")
            .process(exchange -> {
                exchange.getIn().setHeader(Constant.Header.CONTROL_FILE_TYPE, constant(EFileType.EARNING_FLOW));
                exchange.setProperty(Constant.Properties.PROCESSED_FILE, new HashSet<>());
            })
            .choice()
            .when(simple("${header.CamelFileNameOnly} regex '^control-\\d{8}-\\d{13}\\..*$'"))
            .process(preProcessControlFileProcessor)
            .choice()
            .when(exchange -> exchange.getProperty(Constant.Properties.CONTROL_FILE_SKIP) == null)
            .log("Start processing control file: ${header.CamelFileNameOnly}")
            .choice()
            .when(simple("${properties:sftp-server.pgp-enable} == true"))
            .log("Decrypting control file: ${header.CamelFileNameOnly}")
            .doTry()
            .unmarshal(pgpFormat)
            .doCatch(Exception.class)
            .throwException(DecryptControlFileException.class,"")
            .end()
            .end()
            .process(e -> countDownLatchMap.clear())                                                        // Reset count down map
            .process(controlFileProcessor)                                                                  // Process control file content
            .process(controlFileHistoryProcessor::processStart)                                             // Update control file processing
            .process(scaleProcessor::processStart)
            .split(body())                                                                                  // Split list data files
            .setHeader(Constant.Header.DATA_FILE_NAME, body())                                              // Set data file name to Header
            .to("direct:processFile")                                                                   // Move to route process data file
            .end()
            .to("direct:generateResultControl")                                                             // Generate output control file
            .process(controlFileHistoryProcessor::processEnd)                                                   // Update control file status
            .process(this::cleanupBody)
            .stop();

        /*
             Route to process the Data file
             Check and forward data to the corresponding route for processing
         */
        from("direct:processFile")
                .routeId("processFileRoute")
                .process(preProcessDataFileProcessor)                      // Check whether the data should proceed to processing or be skipped
                .choice()
                    .when(exchange -> exchange.getProperty(Constant.Properties.DATA_FILE_SKIP) == null)
                        .log("Start processing data file: ${header." + Constant.Header.DATA_FILE_NAME + "}")
                        .process(gcProcessor)
                        .process(dataFileHistoryProcessor::processStart)
                        .choice()
                            .when(body().regex("^onboard-\\d{8}-\\d{0,19}-\\d{0,19}-\\d{13}\\..*$"))
                                .to("direct:onboard")
                            .when(body().regex("^customer-\\d{8}-\\d{0,19}-\\d{0,19}-\\d{13}\\..*$"))
                                .to("direct:customerProfile")
                            .when(body().regex("^transaction-\\d{8}-\\d{0,19}-\\d{0,19}-\\d{13}\\..*$"))
                                .to("direct:transaction")
                            .when(body().regex("^event-\\d{8}-\\d{0,19}-\\d{0,19}-\\d{13}\\..*$"))
                                .to("direct:event")
                        .end()
                        .to("direct:waitForFileProcessing")
                        .process(dataFileHistoryProcessor::processEnd)
                        .log("End process data file: ${header." + Constant.Header.DATA_FILE_NAME + "}")
                        .process(this::cleanupAll)
                    .end()
                .end();

        /*
            Route to handle each data type individually
         */
        from("direct:onboard")
                .setHeader(Constant.Header.PROCESS_ID, constant(EProcessServiceId.ONBOARD.getName()))
                .setHeader(Constant.Header.THREAD_NUMS, constant(onboardThreadsNum))
                .setProperty(Constant.Properties.DATA_FILE_TYPE, constant("onboard"))
                .to("direct:decryptAndProcess");
        from("direct:customerProfile")
                .setHeader(Constant.Header.PROCESS_ID, constant(EProcessServiceId.CUSTOMER.getName()))
                .setHeader(Constant.Header.THREAD_NUMS, constant(customerThreadsNum))
                .setProperty(Constant.Properties.DATA_FILE_TYPE, constant("customer"))
                .to("direct:decryptAndProcess");
        from("direct:transaction")
                .setHeader(Constant.Header.PROCESS_ID, constant(EProcessServiceId.TRANSACTION.getName()))
                .setHeader(Constant.Header.THREAD_NUMS, constant(transactionThreadsNum))
                .setProperty(Constant.Properties.DATA_FILE_TYPE, constant("transaction"))
                .to("direct:decryptAndProcess");
        from("direct:event")
                .setHeader(Constant.Header.PROCESS_ID, constant(EProcessServiceId.EVENT.getName()))
                .setHeader(Constant.Header.THREAD_NUMS, constant(eventThreadsNum))
                .setProperty(Constant.Properties.DATA_FILE_TYPE, constant("event"))
                .to("direct:decryptAndProcess");

        /*
             Route to process the Data file
             - Decrypt file
             - Read content & process logic
         */
        from("direct:decryptAndProcess")
                .process(readFileContentProcessor)
                .choice()
                    .when(simple("${properties:sftp-server.pgp-enable} == true"))
                        .log("Decrypting data file: ${header." + Constant.Header.DATA_FILE_NAME + "}")
                        .doTry()
                            .unmarshal(pgpFormat)
                        .doCatch(Exception.class)
                            .throwException(DecryptDataFileException.class,"")
                        .end()
                        .log("End decrypt data file: ${header." + Constant.Header.DATA_FILE_NAME + "}")
                .end()
                .process(exchange -> {
                    String content = exchange.getIn().getBody(String.class);
                    String[] lines = content.split("\n");
                    String dataFileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);
                    CountDownLatch latch = new CountDownLatch(lines.length);
                    countDownLatchMap.put(dataFileName, latch);
                    List<Map<String, Object>> indexedLines = new ArrayList<>();
                    for (int i = 0; i < lines.length; i++) {
                        Map<String, Object> indexedLine = new HashMap<>();
                        indexedLine.put("index", i);
                        indexedLine.put("content", lines[i]);
                        indexedLines.add(indexedLine);
                    }
//                    exchange.getIn().setBody(lines);
                    exchange.getIn().setBody(indexedLines);
                    exchange.setProperty(Constant.Properties.DATA_FILE_TOTAL_LINES, lines.length);
                })
                .split(body()).streaming()
                .process(deserializeProcessor)
                .process(threadOrderProcessor)
                .toD("seda:processLine-${exchangeProperty." + Constant.Properties.ORDER_THREAD_INDEX + "}" + "?blockWhenFull=true&size=" + queueSize);

        /*
            Worker pool to receive and process each line of the data file
         */
        for (int i = 0; i < threadsNum; i++) {
            from("seda:processLine-" + i + "?blockWhenFull=true&size=" + queueSize)
                    .process(contentProcessor)
                    .to("direct:processOutput")
                    .process(exchange -> {
                        String fileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);
                        CountDownLatch latch = countDownLatchMap.get(fileName);
                        if (latch != null) {
                            latch.countDown();
                        }
                    });
        }

        /*
            Route to handle waiting data file completed
         */
        from("direct:waitForFileProcessing")
                .process(exchange -> {
                    String fileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);
                    CountDownLatch latch = countDownLatchMap.get(fileName);
                    if (latch != null) {
                        latch.await();
                        countDownLatchMap.remove(fileName);
                    }
                });

        from("direct:processOutput")
                .aggregate(simple("${header." + Constant.Header.DATA_FILE_NAME + "}"), new DataFileAggregationStrategy())
                .completionSize(simple("${exchangeProperty." + Constant.Properties.DATA_FILE_TOTAL_LINES + "}"))
//                .parallelProcessing(true)
                .process(aggregationStrategy::completeAggregation)
                .process(uploadDataFileProcessor)
                .process(this::cleanupBody);

        // Generate and upload control files
        from("direct:generateResultControl")
                .process(uploadControlFileProcessor);
    }

    private URI buildSftpUri(SFTPConfig config) throws URISyntaxException {
        URIBuilder uriBuilder = new URIBuilder()
                .setScheme(config.getProtocol())
                .setHost(config.getHost())
                .setPort(Integer.parseInt(config.getPort()))
                .setPath(config.getBaseFolder())
                .addParameter("username", config.getUsername())
                .addParameter("password", "RAW(" + config.getPassword() + ")")
                .addParameter("delay", config.getDelay())
                .addParameter("maxDepth", config.getMaxDepth())
                .addParameter("recursive", config.getRecursive())
                .addParameter("idempotent", config.getIdempotent())
                .addParameter("streamDownload", config.getStreamDownload())
                .addParameter("preSort", config.getPreSort())
                .addParameter("knownHostsFile", config.getKnownHostsPath())
                .addParameter("noop", config.getNoop())
                .addParameter("stepwise", config.getStepwise());

        if (config.getPrivateKeyPath() != null && !config.getPrivateKeyPath().isEmpty()) {
            uriBuilder.addParameter("privateKeyFile", config.getPrivateKeyPath());
        }

        if (config.getPrivateKeyPassphrase() != null && !config.getPrivateKeyPassphrase().isEmpty()) {
            uriBuilder.addParameter("privateKeyPassphrase", config.getPrivateKeyPassphrase());
        }

        return uriBuilder.build();
    }


    public PGPDataFormat pgpEncrypt() {
        PGPDataFormat pgp = new PGPDataFormat();
        pgp.setKeyFileName("file:" + sftpConfig.getPgpPrivateKeyPath());
        pgp.setSignatureKeyFileName("file:" + sftpConfig.getPgpPublicKeyPath());
        pgp.setPassword(sftpConfig.getPgpPrivateKeyPassphrase());
        pgp.setArmored("true");
        pgp.setIntegrity("true");

        return pgp;
    }

    private void cleanupBody(Exchange exchange) {
        exchange.getIn().setBody(null);
    }

    private void cleanupAll(Exchange exchange) {
        exchange.getIn().setBody(null);
        exchange.getIn().getHeaders().clear();
        exchange.getProperties().clear();
    }

}
