package com.onemount.orchestration.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ServiceError {
    public static final String CODE_SUCCESS = "200";

    private String code;
    private String message;

    public boolean isSuccess() {
        return CODE_SUCCESS.equals(this.code);
    }
}
