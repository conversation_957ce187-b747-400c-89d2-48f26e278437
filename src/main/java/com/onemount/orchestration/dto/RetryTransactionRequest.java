package com.onemount.orchestration.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;

import javax.validation.constraints.NotBlank;

@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class RetryTransactionRequest {

    @NotBlank(message = "service_error_code not null")
    private String serviceErrorCode;

    @NotBlank(message = "created_date not null")
    private String createdDate;

    @NotBlank(message = "ds_partition_date not null")
    private String dsPartitionDate;

    private Long minId;

    private Long maxId;
}
