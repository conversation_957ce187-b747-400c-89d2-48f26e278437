package com.onemount.orchestration.mapper;

import com.onemount.orchestration.constant.EErrorGroup;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ErrorGroupMapper {
    private static final Map<String, List<String>> ERROR_CODE_MAPPING = new HashMap<>();

    static {
        ERROR_CODE_MAPPING.put("E001", List.of("00", "200"));
        ERROR_CODE_MAPPING.put("E002", List.of("E400101", "E400108", "4040115"));
        ERROR_CODE_MAPPING.put("E003", List.of("E400103"));
        ERROR_CODE_MAPPING.put("E004", List.of("E400110"));
        ERROR_CODE_MAPPING.put("E005", List.of("E400003", "E400202", "E400203", "E400204", "E400216",
                "E400217", "E400218", "E400219", "E400220", "E400221", "E400222", "E400223", "E400224", "E400225",
                "E400226", "E400227", "E400325", "E400201", "E400305", "E400327", "E400304", "E400355", "E400403",
                "E5000", "E400303", "E400601", "E400602", "E400603", "E400604", "E400605", "E400606",
                "E400009", "E400003", "E400325", "E400302", "E400326", "E400361", "E400362", "E400313", "E400314",
                "E400306", "E400328", "E400330", "E400307", "E400329", "E400308", "E400337", "E400315", "E400344",
                "E400317", "E400345", "E400320", "E400010", "E400309", "E400310", "E400359", "E400360", "E400354",
                "E400353", "E400352", "E400351", "E400350", "E400349", "E400348", "E400347", "E400346", "E400343",
                "E400342", "E400341", "E400340", "E400338", "E400336", "E400335", "E400334", "E400333", "E400332",
                "E400331", "E400301", "E400312", "E400311", "E400401"));
        ERROR_CODE_MAPPING.put("E006", List.of("500"));
    }

    public static EErrorGroup mapErrorCodeToGroup(String errorCode) {
        // Find the EErrorGroup that contains the error code
        for (EErrorGroup group : EErrorGroup.values()) {
            List<String> mappedCodes = ERROR_CODE_MAPPING.get(group.getValue());
            if (mappedCodes != null && mappedCodes.contains(errorCode)) {
                return group;
            }
        }
        // Default to UNKNOWN if no mapping is found
        return EErrorGroup.UNKNOWN;
    }
}
