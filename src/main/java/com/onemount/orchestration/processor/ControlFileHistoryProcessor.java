package com.onemount.orchestration.processor;

import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.entity.ControlFileHistory;
import com.oneid.oneloyalty.common.repository.ControlFileHistoryRepository;
import com.oneid.oneloyalty.common.repository.DataFileHistoryRepository;
import com.onemount.orchestration.constant.Constant;
import com.onemount.orchestration.constant.EErrorCode;
import com.onemount.orchestration.support.utils.Log;
import com.onemount.orchestration.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ControlFileHistoryProcessor implements Processor {
    private final ControlFileHistoryRepository controlFileHistoryRepo;
    private final DataFileHistoryRepository dataFileHistoryRepo;

    @Override
    public void process(Exchange exchange) {
        // Do nothing
    }

    public void processStart(Exchange exchange) {
        String controlFileName = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_NAME, String.class);
        updateStatus(controlFileName, EProcessingStatus.PROCESSING);
    }

    public void processEnd(Exchange exchange) {
        String controlFileName = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_NAME, String.class);
        Long notSuccess = dataFileHistoryRepo.countFileNotSuccess(controlFileName);
        if (notSuccess == null || notSuccess <= 0) {
            updateStatus(controlFileName, EProcessingStatus.SUCCESS);
        }
    }

    public void processFormatError(Exchange exchange) {
        String controlFileName = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_NAME, String.class);
        EErrorCode errorCode = exchange.getIn().getBody(EErrorCode.class);
        if (errorCode == null) {
            errorCode = EErrorCode.FILE_FORMAT_INVALID;
        }

        ControlFileHistory ctrFileHistory = controlFileHistoryRepo.findByFileName(controlFileName, EFileType.EARNING_FLOW);
        if (ctrFileHistory == null) {
            ctrFileHistory = ControlFileHistory.builder()
                    .status(EProcessingStatus.FAILED)
                    .fileName(controlFileName)
                    .totalFiles(0)
                    .isIgnored(EBoolean.NO)
                    .fileType(EFileType.EARNING_FLOW)
                    .build();
        }
        ctrFileHistory.setErrorCode(errorCode.getCode());
        ctrFileHistory.setErrorMessage(errorCode.getMessage());
        controlFileHistoryRepo.save(ctrFileHistory);
        Log.error(LogData.createLogData()
                .append("mes", "ControlFileProcessor - Stop process due to exception while deserializing file")
                .append("control_file", controlFileName)
                .append("error_msg", errorCode.getMessage())
        );
    }

    public void processError(Exchange exchange) {
        String controlFileName = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_NAME, String.class);
        EErrorCode errorCode = exchange.getIn().getBody(EErrorCode.class);
        updateStatus(controlFileName, EProcessingStatus.FAILED, errorCode);
    }

    private void updateStatus(String controlFileName, EProcessingStatus status) {
        updateStatus(controlFileName, status, null);
    }

    private void updateStatus(String controlFileName, EProcessingStatus status, EErrorCode errorCode) {

        ControlFileHistory history = controlFileHistoryRepo.findByFileName(controlFileName, EFileType.EARNING_FLOW);
        history.setStatus(status);

        if (errorCode != null) {
            history.setErrorCode(errorCode.getCode());
            history.setErrorMessage(errorCode.getMessage());

        } else if (EProcessingStatus.SUCCESS.equals(status)) {
            history.setErrorCode(null);
            history.setErrorMessage(null);
        }

        long onboard = countDataFiles(history.getFileName(), "onboard");
        long customer = countDataFiles(history.getFileName(), "customer");
        long transaction = countDataFiles(history.getFileName(), "transaction");
        long event = countDataFiles(history.getFileName(), "event");

        Log.info(LogData.createLogData()
                .append("msg", "ControlFileHistoryProcessor - Start update control file history status")
                .append("control_file", controlFileName)
                .append("status", status.getValue())
                .append("onboard", onboard)
                .append("customer", customer)
                .append("transaction", transaction)
                .append("event", event)
                .append("error", errorCode)
        );

        controlFileHistoryRepo.save(history);
    }

    private long countDataFiles(String controlFile, String fileType) {
        fileType = fileType + "%";
        return dataFileHistoryRepo.countLike(controlFile, fileType) != null
                ? dataFileHistoryRepo.countLike(controlFile, fileType) : 0;
    }
}
