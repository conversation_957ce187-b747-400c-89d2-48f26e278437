package com.onemount.orchestration.processor;

import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.entity.ControlFileHistory;
import com.oneid.oneloyalty.common.repository.ControlFileHistoryRepository;
import com.onemount.orchestration.constant.Constant;
import com.onemount.orchestration.support.utils.FileNameGenerator;
import com.onemount.orchestration.support.utils.Log;
import com.onemount.orchestration.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PreProcessControlFileProcessor implements Processor {

    private final ControlFileHistoryRepository controlFileRepo;

    /*
        Check whether the control file should be moved to processing or skipped. Reasons:
        - The file has already been processed and should be ignored.
        - There is a file in the history that has not been processed successfully.
    */
    @Override
    public void process(Exchange exchange) throws Exception {

        String controlFileName = exchange.getIn().getHeader(Exchange.FILE_NAME, String.class);

        boolean skipFlag = false;

        // Check if control file was processed
        ControlFileHistory controlFileEntity = controlFileRepo.findByFileName(controlFileName, EFileType.EARNING_FLOW);
        if (controlFileEntity != null) {
            skipFlag = isProcessedFile(controlFileEntity);
        }

        // Check if there is a file in the history that has not been processed successfully.
        if (!skipFlag) {
            Long pendingFileNum = controlFileRepo.countPendingFiles(controlFileName, EFileType.EARNING_FLOW);
            if (pendingFileNum != null && pendingFileNum > 0) {
                Log.info(LogData.createLogData()
                        .append("msg", "There is a file in the history that has not been processed successfully.")
                        .append("control_file", controlFileName)
                );
                skipFlag = true;
            }
        }

        if (skipFlag) {
            exchange.setProperty(Constant.Properties.CONTROL_FILE_SKIP, true);
        }

        exchange.getIn().setHeader(Constant.Header.CONTROL_FILE_NAME, controlFileName);
        exchange.setProperty(Constant.Properties.COB_DATE, FileNameGenerator.extractCobDate(controlFileName));
    }

    /*
        Control file was processed when status = SUCCESS or ignore flag = YES
     */
    private boolean isProcessedFile(ControlFileHistory controlFile) {
        return EProcessingStatus.SUCCESS.equals(controlFile.getStatus()) ||
                EBoolean.YES.equals(controlFile.getIsIgnored());
    }
}
