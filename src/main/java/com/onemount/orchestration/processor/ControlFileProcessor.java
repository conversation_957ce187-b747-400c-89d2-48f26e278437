package com.onemount.orchestration.processor;

import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EDataFileType;
import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.entity.ControlFileHistory;
import com.oneid.oneloyalty.common.entity.DataFileHistory;
import com.oneid.oneloyalty.common.repository.ControlFileHistoryRepository;
import com.oneid.oneloyalty.common.repository.DataFileHistoryRepository;
import com.oneid.oneloyalty.common.util.StringUtil;
import com.onemount.orchestration.constant.Constant;
import com.onemount.orchestration.constant.EErrorCode;
import com.onemount.orchestration.exception.DataFileValidationException;
import com.onemount.orchestration.exception.DeserializeControlFileException;
import com.onemount.orchestration.exception.DeserializeException;
import com.onemount.orchestration.model.ControlFileData;
import com.onemount.orchestration.service.DeserializeService;
import com.onemount.orchestration.support.remote.SFTPFileSupport;
import com.onemount.orchestration.support.utils.Log;
import com.onemount.orchestration.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ControlFileProcessor implements Processor {

    private static final String CHANNEL = "TCB";
    private static final Map<String, Integer> categoryOrder = Map.of(
            "onboard", 1,
            "customer", 2,
            "transaction", 3,
            "event", 4
    );
    private final DeserializeService deserializeService;
    private final SFTPFileSupport sftpFileSupport;
    private final DataFileHistoryRepository dataFileHistoryRepo;
    private final ControlFileHistoryRepository controlFileHistoryRepo;

    @Override
    public void process(Exchange exchange) {
        String controlFileName = exchange.getIn().getHeader(Exchange.FILE_NAME, String.class);
        String fileContent = exchange.getIn().getBody(String.class);

        List<String> dataFiles = new ArrayList<>();
        if (StringUtil.isNotEmpty(fileContent)) {
            try {
                ControlFileData controlFileContent = deserializeService.deserialize(fileContent, ControlFileData.class);
                if (CollectionUtils.isNotEmpty(controlFileContent.getFileList())) {
                    dataFiles = controlFileContent.getFileList();
                }
            } catch (DeserializeException e) {
                throw new DeserializeControlFileException("Exception while deserializing control file file");
            }
        }

        ControlFileHistory ctrFileHistory = controlFileHistoryRepo.findByFileName(controlFileName, EFileType.EARNING_FLOW);
        if (ctrFileHistory == null) {
            ctrFileHistory = createNewControlFileHistory(controlFileName, dataFiles.size());
        } else {
            ctrFileHistory.setTotalFiles(dataFiles.size());
        }

        /*
            Handle case dataFiles is empty
            Make process success and stop
         */
        if (dataFiles.isEmpty()) {
            Log.info(LogData.createLogData()
                    .append("msg", "ControlFileHandler -- Control file is empty")
                    .append("file_name", controlFileName)
            );
            exchange.getIn().setBody(dataFiles);
            return;
        }

        List<DataFileHistory> dataFileHistories = recordDataFileHistories(exchange, dataFiles, ctrFileHistory);

        boolean hasFailedFiles = dataFileHistories.stream()
                .anyMatch(e -> EProcessingStatus.FAILED.equals(e.getStatus()));
        if (hasFailedFiles) {
            ctrFileHistory.setErrorCode(EErrorCode.DATA_FILE_ERROR.getCode());
            ctrFileHistory.setErrorMessage(EErrorCode.DATA_FILE_ERROR.getMessage());
            ctrFileHistory.setStatus(EProcessingStatus.FAILED);
            Log.error(LogData.createLogData()
                    .append("mes", "ControlFileProcessor - Stop process when one or more data files have errors")
                    .append("control_file", controlFileName)
            );
            throw new DataFileValidationException("One or more data files have errors");
        }
        controlFileHistoryRepo.save(ctrFileHistory);

        // Filter out data file was processed
        List<String> processDataFiles = dataFileHistories.stream()
                .filter(e -> !EProcessingStatus.SUCCESS.equals(e.getStatus()))
                .map(DataFileHistory::getFileName)
                .collect(Collectors.toList());

        List<String> processDataFilesOrdered = sortFilesByRule(processDataFiles);

        exchange.getIn().setBody(processDataFilesOrdered);
    }

    private ControlFileHistory createNewControlFileHistory(String controlFileName,
                                                           Integer totalDataFile
    ) {
        ControlFileHistory controlFileHistory =
                ControlFileHistory.builder()
                        .status(EProcessingStatus.INIT)
                        .fileName(controlFileName)
                        .totalFiles(totalDataFile)
                        .isIgnored(EBoolean.NO)
                        .fileType(EFileType.EARNING_FLOW)
                        .channel(CHANNEL)
                        .build();

        return controlFileHistoryRepo.save(controlFileHistory);
    }

    /*
        Sort data files by sequence rule:
            1. Follow the sequence by file group in the order:
               Onboard → Customer Profile → Transaction → Event.
            2. Process files within each group in filename order, e.g., 0001 → 0002 → 0003.
    */
    private List<String> sortFilesByRule(List<String> dataFiles) {
        return dataFiles.stream()
                .sorted(Comparator
                        .comparing((String file) -> {
                            String prefix = file.split("-")[0];
                            return categoryOrder.getOrDefault(prefix, Integer.MAX_VALUE);
                        })
                        .thenComparing(file -> Integer.parseInt(file.split("-")[2]))
                )
                .collect(Collectors.toList());
    }

    private List<DataFileHistory> recordDataFileHistories(Exchange exchange,
                                                          List<String> orderedFiles,
                                                          ControlFileHistory controlFileEntity
    ) {
        String sourcePath = exchange.getIn().getHeader(Exchange.FILE_PARENT, String.class);
        Set<String> seenFiles = new HashSet<>();
        Set<String> duplicateFiles = orderedFiles.stream()
                .filter(file -> !seenFiles.add(file))
                .collect(Collectors.toSet());

        List<DataFileHistory> dataFileEntities = new ArrayList<>();

        for (String fileName : orderedFiles) {
            EDataFileType dataFileType = getDataFileType(fileName);
            exchange.getIn().setHeader(Constant.Properties.DATA_FILE_TYPE, dataFileType.getValue());

            DataFileHistory dataFileEntity =
                    dataFileHistoryRepo.findByFileNameAndControlFileName(fileName, controlFileEntity.getFileName())
                            .orElseGet(() ->
                                    DataFileHistory.builder()
                                            .controlFileId(controlFileEntity.getId())
                                            .controlFileName(controlFileEntity.getFileName())
                                            .fileName(fileName)
                                            .status(EProcessingStatus.INIT)
                                            .fileType(dataFileType)
                                            .build()
                            );

            EErrorCode validatedError = validateDataFile(duplicateFiles, sourcePath, fileName);

            if (!EProcessingStatus.SUCCESS.equals(dataFileEntity.getStatus())) {
                if (validatedError != null) {
                    dataFileEntity.setErrorCode(validatedError.getCode());
                    dataFileEntity.setErrorMessage(validatedError.getMessage());
                    dataFileEntity.setStatus(EProcessingStatus.FAILED);
                } else if (EProcessingStatus.FAILED.equals(dataFileEntity.getStatus())) {
                    dataFileEntity.setStatus(EProcessingStatus.INIT);
                }
                dataFileEntity = dataFileHistoryRepo.save(dataFileEntity);
            }

            dataFileEntities.add(dataFileEntity);
        }

        return dataFileEntities;
    }

    private EErrorCode validateDataFile(Set<String> duplicatesFiles,
                                        String sourcePath,
                                        String fileName
    ) {

        // Check if file duplicate
        if (duplicatesFiles.contains(fileName)) {
            Log.error(LogData.createLogData()
                    .append("mes", "ControlFileProcessor - validate data file error")
                    .append("err", EErrorCode.DATA_FILE_DUPLICATED.getMessage())
                    .append("file_name", fileName)
            );
            return EErrorCode.DATA_FILE_DUPLICATED;
        }

        // Check if file exists
        String filePath = String.format("%s/%s", sourcePath, fileName);
        boolean doesFileExist = sftpFileSupport.doesFileExist(filePath, false);
        if (!doesFileExist) {
            Log.error(LogData.createLogData()
                    .append("mes", "ControlFileProcessor - validate data file error")
                    .append("err", EErrorCode.DATA_FILE_NOT_FOUND.getMessage())
                    .append("file_name", fileName)
            );
            return EErrorCode.DATA_FILE_NOT_FOUND;
        }

        return null;
    }

    private EDataFileType getDataFileType(String fileName) {
        return EDataFileType.valueOf(fileName.split("-")[0].toUpperCase());
    }
}
