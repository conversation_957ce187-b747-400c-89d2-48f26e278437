package com.onemount.orchestration.processor;

import com.onemount.orchestration.support.utils.Log;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GCProcessor implements Processor {

    @Override
    public void process(Exchange exchange) throws Exception {
        Log.info("Start run GC");
        System.gc();
    }

}