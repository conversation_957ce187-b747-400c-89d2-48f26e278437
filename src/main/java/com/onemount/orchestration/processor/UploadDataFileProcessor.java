package com.onemount.orchestration.processor;

import com.oneid.oneloyalty.common.entity.DataFileHistory;
import com.oneid.oneloyalty.common.repository.DataFileHistoryRepository;
import com.onemount.orchestration.constant.Constant;
import com.onemount.orchestration.constant.EProcessServiceId;
import com.onemount.orchestration.model.ProcessData;
import com.onemount.orchestration.service.ContentProcessFactory;
import com.onemount.orchestration.service.ContentProcessService;
import com.onemount.orchestration.service.FileService;
import com.onemount.orchestration.service.RecordCountingService;
import com.onemount.orchestration.support.remote.SFTPConfig;
import com.onemount.orchestration.support.remote.SFTPFileSupport;
import com.onemount.orchestration.support.utils.CSVUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class UploadDataFileProcessor implements Processor {

    private final SFTPConfig sftpConfig;

    private final SFTPFileSupport sftpFileSupport;

    private final DataFileHistoryRepository dataFileHistoryRepo;

    private final FileService fileService;

    private final RecordCountingService recordCountingService;

    @Override
    public void process(Exchange exchange) {
        String fileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);
        String fileType = exchange.getProperty(Constant.Properties.DATA_FILE_TYPE, String.class);
        String processId = exchange.getIn().getHeader(Constant.Header.PROCESS_ID, String.class);
        String cobDate = exchange.getProperty(Constant.Properties.COB_DATE, String.class);
        ContentProcessService<Object> processService = ContentProcessFactory.getProcessor(EProcessServiceId.of(processId));
        List<?> body = exchange.getIn().getBody(List.class);
        updateTotalRecord(exchange, body);
        try {
            if (CollectionUtils.isNotEmpty(body)) {
                String outputFileName = fileService.createCsvResultFileName(fileName, cobDate, fileType, body.size());
                String outputFilePath = String.format("%s/%s", sftpConfig.getOutputFolder(), outputFileName);

                byte[] csvData;
                try {
                    String[] headers = processService.getHeader();

                    // Create array to hold all data rows
                    String[][] dataRows = new String[body.size()][];

                    // Convert each item in the body to a row
                    for (int i = 0; i < body.size(); i++) {
                        Object item = body.get(i);
                        // Process the item based on its type
                        if (item instanceof ProcessData) {
                            dataRows[i] = processService.generateOutput((ProcessData<Object>) item);
                        } else {
                            // Create a ProcessData wrapper if needed
                            ProcessData<Object> processData = ProcessData.builder()
                                    .fileName(fileName)
                                    .processId(processId)
                                    .dataObj(item)
                                    .build();
                            dataRows[i] = processService.generateOutput(processData);
                        }
                    }

                    csvData = CSVUtil.generateOutputCSV(headers, dataRows);
                } catch (IllegalArgumentException e) {
                    throw e;
                }
                outputFileName = sftpFileSupport.uploadFile(
                        csvData,
                        outputFilePath,
                        sftpConfig.getPgpPublicKeyOutPath(),
                        sftpConfig.getPgpDataOutEnable()
                );
                Set<String> processedFiles = exchange.getProperty(Constant.Properties.PROCESSED_FILE, Set.class);
                processedFiles.add(outputFileName);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void updateTotalRecord(Exchange exchange, List<?> body) {
        String dataFileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);
        String controlFileName = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_NAME, String.class);

        DataFileHistory dataFileHistory = dataFileHistoryRepo.find(dataFileName, controlFileName);

        Integer total = exchange.getProperty(Constant.Properties.DATA_FILE_TOTAL_LINES, Integer.class);

        // Extract actual data objects from ProcessData wrappers
        List<Object> dataObjects = body.stream()
                .map(item -> {
                    if (item instanceof ProcessData) {
                        return ((ProcessData<?>) item).getDataObj();
                    }
                    return item;
                })
                .collect(Collectors.toList());

        // Count failed records using the extracted data objects
        long totalFailedRecord = recordCountingService.countFailedRecords(dataObjects);

        dataFileHistory.setTotalRecord(total);
        dataFileHistory.setTotalFailedRecord(Math.toIntExact(totalFailedRecord));

        dataFileHistoryRepo.save(dataFileHistory);
    }


}
