package com.onemount.orchestration.processor;

import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.entity.DataFileHistory;
import com.oneid.oneloyalty.common.repository.DataFileHistoryRepository;
import com.onemount.orchestration.constant.Constant;
import com.onemount.orchestration.constant.EErrorCode;
import com.onemount.orchestration.support.utils.Log;
import com.onemount.orchestration.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DataFileHistoryProcessor implements Processor {
    private final DataFileHistoryRepository dataFileHistoryRepo;

    @Override
    public void process(Exchange exchange) {
        // Do nothing
    }

    public void processStart(Exchange exchange) {
        updateDataFileStatus(exchange, EProcessingStatus.PROCESSING);
    }

    public void processEnd(Exchange exchange) {
        updateDataFileStatus(exchange, EProcessingStatus.SUCCESS);
    }

    public void processError(Exchange exchange) {
        EErrorCode errorCode = exchange.getIn().getBody(EErrorCode.class);
        updateDataFileStatus(exchange, EProcessingStatus.FAILED, errorCode);
    }

    private void updateDataFileStatus(Exchange exchange, EProcessingStatus status) {
        updateDataFileStatus(exchange, status, null);
    }

    private void updateDataFileStatus(Exchange exchange, EProcessingStatus status, EErrorCode errorCode) {

        String dataFileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);
        String controlFileName = exchange.getIn().getHeader(Constant.Header.CONTROL_FILE_NAME, String.class);

        DataFileHistory dataFileHistory = dataFileHistoryRepo.find(dataFileName, controlFileName);
        dataFileHistory.setStatus(status);
        if (errorCode != null) {
            dataFileHistory.setErrorCode(errorCode.getCode());
            dataFileHistory.setErrorMessage(errorCode.getMessage());

            Log.info(LogData.createLogData()
                    .append("msg", "DataFileHistoryProcessor - Start update data file history status")
                    .append("data_file", dataFileName)
                    .append("control_file", controlFileName)
                    .append("status", status.getValue())
                    .append("error_code", errorCode.getMessage())
            );
        } else {
            dataFileHistory.setErrorCode(null);
            dataFileHistory.setErrorMessage(null);

            Log.info(LogData.createLogData()
                    .append("msg", "DataFileHistoryProcessor - Start update data file history status")
                    .append("data_file", dataFileName)
                    .append("control_file", controlFileName)
                    .append("status", status.getValue())
            );
        }
        dataFileHistoryRepo.save(dataFileHistory);
    }
}
