package com.onemount.orchestration.processor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.onemount.orchestration.constant.Constant;
import com.onemount.orchestration.model.ControlFileData;
import com.onemount.orchestration.service.FileService;
import com.onemount.orchestration.support.remote.SFTPConfig;
import com.onemount.orchestration.support.remote.SFTPFileSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class UploadControlFileProcessor implements Processor {

    private final SFTPConfig sftpConfig;

    private final SFTPFileSupport sftpFileSupport;

    private final FileService fileService;

    @Override
    public void process(Exchange exchange) {
        List<String> processedFiles = exchange.getProperty(Constant.Properties.PROCESSED_FILE, List.class);
        String cobDate = exchange.getProperty(Constant.Properties.COB_DATE, String.class);
        List<String> dataFileTypes = List.of("ONBOARD", "CUSTOMER", "TRANSACTION", "EVENT");

        Map<String, List<String>> processedFilesMap = getProcessedFilesByType(processedFiles);
        for (String dataFileType : dataFileTypes) {
            List<String> processedFilesByType = processedFilesMap.get(dataFileType);
            if (CollectionUtils.isNotEmpty(processedFilesByType))
                uploadFile(processedFilesByType, cobDate, dataFileType, sftpConfig.getOutputFolder(), sftpConfig.getPgpPublicKeyOutPath());
        }
    }

    private void uploadFile(List<String> processedFiles, String cobDate, String dataFileType, String outputFolder, String publicKeyPath) {
        if (CollectionUtils.isNotEmpty(processedFiles)) {
            ControlFileData resultControl = new ControlFileData();
            resultControl.setFileList(processedFiles);

            String result;
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                result = objectMapper.writeValueAsString(resultControl);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }

            String fileName = fileService.createJsonControlFileName(cobDate, dataFileType);

            String filePath = String.format("%s/%s", outputFolder, fileName);
            try {
                sftpFileSupport.uploadFile(result.getBytes(), filePath, publicKeyPath, sftpConfig.getPgpControlOutEnable());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private Map<String, List<String>> getProcessedFilesByType(List<String> processedFiles) {
        return processedFiles.stream()
                .collect(Collectors.groupingBy(
                        file -> file.split("[-_]")[1].toUpperCase(),
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    Collections.sort(list);
                                    return list;
                                }
                        )
                ));
    }
}
