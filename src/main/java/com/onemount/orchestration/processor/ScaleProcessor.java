package com.onemount.orchestration.processor;

import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import com.onemount.orchestration.service.ScaleProcessService;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ScaleProcessor implements Processor {

    private final ScaleProcessService scaleProcessService;

    @Value("${app.scale.enable:}")
    private Boolean enableScalePod;

    @Override
    public void process(Exchange exchange) throws Exception {
    }

    public void processStart(Exchange exchange) {
        if (!enableScalePod) {
            return;
        }
        scaleProcessService.scaleUp();
    }
}
