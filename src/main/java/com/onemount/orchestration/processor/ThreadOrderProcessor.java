package com.onemount.orchestration.processor;

import com.oneid.oneloyalty.common.util.StringUtil;
import com.onemount.orchestration.constant.Constant;
import com.onemount.orchestration.model.ProcessData;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadLocalRandom;

@Component
@RequiredArgsConstructor
public class ThreadOrderProcessor implements Processor {

    @Value("${app.performance.threads.max:8}")
    private Integer maxThreadsNum;

    @Override
    public void process(Exchange exchange) {
        Integer threadNums = exchange.getIn().getHeader(Constant.Header.THREAD_NUMS, Integer.class);
        ProcessData<?> processData = exchange.getIn().getBody(ProcessData.class);

        int threads = maxThreadsNum;
        if (threadNums != null && threadNums > 0) {
            threads = Math.min(maxThreadsNum, threadNums);
        }

        int threadIndex;
        if (processData != null && StringUtil.isNotEmpty(processData.getOrderKey())) {
            String orderKey = processData.getOrderKey();
            threadIndex = Math.abs(orderKey.hashCode() % threads);
        } else {
            threadIndex = ThreadLocalRandom.current().nextInt(threads);
        }

        exchange.setProperty(Constant.Properties.ORDER_THREAD_INDEX, threadIndex);
    }
}
