package com.onemount.orchestration.processor;

import com.onemount.orchestration.constant.EErrorCode;
import com.onemount.orchestration.exception.DataFileProcessException;
import com.onemount.orchestration.exception.FileNotFoundException;
import com.onemount.orchestration.exception.ReadFileException;
import com.onemount.orchestration.support.remote.SFTPFileSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class ReadFileContentProcessor implements Processor {

    private final SFTPFileSupport sftpFileSupport;

    @Override
    public void process(Exchange exchange) {
        String filePath = buildFilePath(exchange);

        try {
            byte[] content = readFileContent(filePath, false);
            exchange.getIn().setBody(content);
        } catch (DataFileProcessException e) {
            exchange.getIn().setBody(e);
            throw e;
        }
    }

    private String buildFilePath(Exchange exchange) {
        String sourcePath = exchange.getIn().getHeader(Exchange.FILE_PARENT, String.class);
        String fileName = exchange.getIn().getBody(String.class);
        return String.format("%s/%s", sourcePath, fileName);
    }

    private byte[] readFileContent(String filePath, boolean isRetry) {
        try {
            return sftpFileSupport.readFile(filePath, isRetry);
        } catch (ReadFileException e) {
            throw new DataFileProcessException("Exception while reading data file content", EErrorCode.READ_DATA_FILE_ERROR, e);
        } catch (FileNotFoundException e) {
            throw new DataFileProcessException("Data file not found", EErrorCode.DATA_FILE_NOT_FOUND, e);
        }
    }
}
