package com.onemount.orchestration.processor;

import com.onemount.orchestration.constant.Constant;
import com.onemount.orchestration.constant.EProcessServiceId;
import com.onemount.orchestration.exception.DeserializeException;
import com.onemount.orchestration.model.ProcessData;
import com.onemount.orchestration.service.ContentProcessFactory;
import com.onemount.orchestration.service.ContentProcessService;
import com.onemount.orchestration.support.utils.Log;
import com.onemount.orchestration.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@RequiredArgsConstructor
public class DeserializeProcessor implements Processor {

    @Override
    public void process(Exchange exchange) throws Exception {
        String processId = exchange.getIn().getHeader(Constant.Header.PROCESS_ID, String.class);
        String fileName = exchange.getIn().getHeader(Constant.Header.DATA_FILE_NAME, String.class);

        // Get the actual content from the map if it's a map, otherwise use the body directly
        Object body = exchange.getIn().getBody();
        String lineData;
        Integer lineIndex = null;

        if (body instanceof Map) {
            Map<String, Object> indexedLine = (Map<String, Object>) body;
            lineData = (String) indexedLine.get("content");
            lineIndex = (Integer) indexedLine.get("index");
        } else {
            lineData = exchange.getIn().getBody(String.class);
        }

        ContentProcessService<Object> processService =
                ContentProcessFactory.getProcessor(EProcessServiceId.of(processId));

        try {
            Object parsedData = processService.deserialize(lineData);
            String orderKey = processService.getOrderKey(parsedData);

            exchange.getIn().setHeader(Constant.Header.ORDER_KEY, orderKey);

            ProcessData<?> processData = ProcessData.builder()
                    .processId(processId)
                    .rawData(lineData)
                    .fileName(fileName)
                    .orderKey(orderKey)
                    .dataObj(parsedData)
                    .build();

            // Store the line index in the ProcessData if available
            if (lineIndex != null) {
                processData.setLineIndex(lineIndex);
            }

            exchange.getIn().setBody(processData);
        } catch (DeserializeException e) {
            Log.error(LogData.createLogData()
                    .append("mes", "DeserializeProcessor - Exception while deserializing file")
                    .append("data_file", fileName)
                    .append("line_data", lineData)
                    .append("error_msg", e.getMessage())
            );
            exchange.setProperty(Exchange.EXCEPTION_HANDLED, true);
            exchange.getIn().setBody(null);
        }
    }
}
