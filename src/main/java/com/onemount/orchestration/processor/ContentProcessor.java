package com.onemount.orchestration.processor;

import com.onemount.orchestration.constant.Constant;
import com.onemount.orchestration.constant.EProcessServiceId;
import com.onemount.orchestration.model.ProcessData;
import com.onemount.orchestration.service.ContentProcessFactory;
import com.onemount.orchestration.service.ContentProcessService;
import com.onemount.orchestration.support.utils.Log;
import com.onemount.orchestration.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ContentProcessor implements Processor {
    @Value("${app.enable-earning-processing:true}")
    private Boolean enableEarningProcessing;

    private Boolean enableLoggingProcessing = true;

    @Override
    @SuppressWarnings("unchecked")
    public void process(Exchange exchange) throws Exception {
        ProcessData<Object> processData = exchange.getIn().getBody(ProcessData.class);
        if (!enableEarningProcessing) {
            if (enableLoggingProcessing) {
                Log.info(LogData.createLogData()
                        .append("msg", "Earning Processing is disabled")
                        .append("processData", processData));
            } else {
                Log.info(LogData.createLogData()
                        .append("msg", "Earning Processing is disabled"));
            }

            return;
        }

        if (processData == null) {
            return;
        }
        try {
            String processId = exchange.getIn().getHeader(Constant.Header.PROCESS_ID, String.class);
            ContentProcessService<Object> processService = ContentProcessFactory.getProcessor(EProcessServiceId.of(processId));

            // Ignore process if content was processed
            if (processService.isProcessed(processData)) {
                Log.info(LogData.createLogData()
                        .append("msg", "[ContentProcessor] - Ignore content was processed")
                        .append("process_data", processData));
            } else {
                processService.process(processData);
            }

            exchange.getIn().setBody(processData);

        } catch (Exception ex) {
            handleException(processData, ex);
        }
    }

    /*
     * Common exception handling
     * Ensures that line processing does not throw exceptions
     */
    private void handleException(ProcessData<Object> processData, Exception ex) {
        Log.warn(LogData.createLogData()
                .append("msg", "Exception while processing a line of data file")
                .append("data_file", processData.getFileName())
                .append("raw_data", processData.getRawData())
                .append("error_msg", ex.getMessage())
        );
    }

    public void enableLogging(Boolean enable) {
        this.enableLoggingProcessing = enable;
    }
}
