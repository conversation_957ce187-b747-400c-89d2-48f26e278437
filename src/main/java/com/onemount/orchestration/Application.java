package com.onemount.orchestration;

import com.onemount.orchestration.support.remote.SFTPConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication
@EnableConfigurationProperties(SFTPConfig.class)
@ComponentScan({"com.onemount.orchestration.*"})
@EntityScan(basePackages = {"com.onemount.orchestration.entity"})
@EnableJpaRepositories(basePackages = {"com.onemount.orchestration.repository"})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

}
