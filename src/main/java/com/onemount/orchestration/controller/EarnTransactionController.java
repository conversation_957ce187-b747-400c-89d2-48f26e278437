package com.onemount.orchestration.controller;

import com.onemount.orchestration.service.impl.TransactionRetryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.Executor;

@RestController
@RequestMapping("/v1/earn-transaction")
public class EarnTransactionController {

    @Autowired
    private TransactionRetryService service;

    @Autowired
    @Qualifier("backgroundProcessThreadPool")
    private Executor executor;

    @GetMapping("/retry")
    public ResponseEntity<?> retryEarnTransaction() {

        executor.execute(() -> service.process());

        return new ResponseEntity<>(HttpStatus.OK);
    }
}
