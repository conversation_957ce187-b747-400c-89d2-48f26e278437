package com.onemount.orchestration.controller;

import com.onemount.orchestration.model.UpdateHistoryRequest;
import com.onemount.orchestration.processor.ContentProcessor;
import com.onemount.orchestration.service.HistoryService;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping(value = {"v1/histories"})
public class HistoryServiceController {

    private final HistoryService historyService;
    private final ContentProcessor contentProcessor;

    public HistoryServiceController(HistoryService historyService,
                                    ContentProcessor contentProcessor
    ) {
        this.historyService = historyService;
        this.contentProcessor = contentProcessor;
    }

    @GetMapping("")
    public ResponseEntity<?> list(@PageableDefault(size = 10) Pageable pageable) {
        return ResponseEntity.ok(historyService.getList(pageable));
    }

    @GetMapping("/{file}")
    public ResponseEntity<?> detail(@PathVariable("file") String fileName) {
        var detail = historyService.detail(fileName);
        if (detail == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(detail);
    }


    @GetMapping("/{file}/dataFile")
    public ResponseEntity<?> getDataFile(@PathVariable("file") String fileName,
                                         @PageableDefault(size = 10) Pageable pageable) {
        return ResponseEntity.ok(historyService.getListDataFiles(fileName, pageable));
    }


    @PutMapping("")
    public ResponseEntity<?> update(@RequestBody @Valid UpdateHistoryRequest request) {
        historyService.updateStatus(request);
        return new ResponseEntity<>(null, HttpStatus.OK);
    }

    @GetMapping("/logging/{enable}")
    public ResponseEntity<?> offLogging(@PathVariable(name = "enable") Boolean enable) {
        contentProcessor.enableLogging(enable);
        return ResponseEntity.ok(null);
    }
}