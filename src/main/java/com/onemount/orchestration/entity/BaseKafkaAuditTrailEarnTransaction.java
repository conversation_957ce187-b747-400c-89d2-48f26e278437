package com.onemount.orchestration.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@MappedSuperclass
public class BaseKafkaAuditTrailEarnTransaction {

    @Column(name = "kafka_type")
    private String kafkaType;

    @Column(name = "business_code")
    private String businessCode;

    @Column(name = "program_code")
    private String programCode;

    @Column(name = "currency_code")
    private String currencyCode;

    @Column(name = "corporation_code")
    private String corporationCode;

    @Column(name = "store_code")
    private String storeCode;

    @Column(name = "pos_code")
    private String posCode;

    @Column(name = "txn_ref_no")
    private String txnRefNo;

    @Column(name = "invoice_no")
    private String invoiceNo;

    @Column(name = "transaction_time")
    private String transactionTime;

    @Column(name = "award_point")
    private BigDecimal awardPoint;

    @Column(name = "error_code")
    private String errorCode;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "service_error_code")
    private String serviceErrorCode;

    @Column(name = "service_error_message")
    private String serviceErrorMessage;

    @Column(name = "tcb_error_code")
    private String tcbErrorCode;

    @Column(name = "tcb_error_message")
    private String tcbErrorMessage;

    @Column(name = "transaction_status")
    private String transactionStatus;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_at")
    private Date createdAt;

    @Column(name = "message_id")
    private String messageId;

    @Column(name = "timestamp")
    private Long timestamp;

    @Column(name = "message_type")
    private String messageType;

    @Column(name = "loyalty_cus_id")
    private String loyaltyCusId;

    @Column(name = "transaction_id")
    private String transactionId;

    @Column(name = "txn_code")
    private String txnCode;

    @Column(name = "txn_service_type")
    private String txnServiceType;

    @Column(name = "arrangement_id")
    private String arrangementId;

    @Column(name = "card_id")
    private String cardId;

    @Column(name = "txn_product_family")
    private String txnProductFamily;

    @Column(name = "txn_product_line")
    private String txnProductLine;

    @Column(name = "txn_in_out")
    private String txnInOut;

    @Column(name = "txn_channel_lv1")
    private String txnChannelLv1;

    @Column(name = "txn_card_indicator")
    private String txnCardIndicator;

    @Column(name = "txn_channel_lv2")
    private String txnChannelLv2;

    @Column(name = "txn_channel_lv3")
    private String txnChannelLv3;

    @Column(name = "txn_mother_category")
    private String txnMotherCategory;

    @Column(name = "txn_mcc")
    private String txnMcc;

    @Column(name = "txn_service_lv1")
    private String txnServiceLv1;

    @Column(name = "txn_service_lv2")
    private String txnServiceLv2;

    @Column(name = "txn_category_group")
    private String txnCategoryGroup;

    @Column(name = "txn_purpose")
    private String txnPurpose;

    @Column(name = "txn_merchant_purpose")
    private String txnMerchantPurpose;

    @Column(name = "txn_supplier")
    private String txnSupplier;

    @Column(name = "txn_ccy_code")
    private String txnCcyCode;

    @Column(name = "txn_adj_reason")
    private String txnAdjReason;

    @Column(name = "txn_slr_ind")
    private String txnSlrInd;

    @Column(name = "txn_date")
    private String txnDate;

    @Column(name = "txn_time")
    private String txnTime;

    @Column(name = "txn_gross_amt")
    private String txnGrossAmt;

    @Column(name = "txn_net_amt")
    private String txnNetAmt;

    @Column(name = "txn_terminalname")
    private String txnTerminalname;

    @Column(name = "txn_brandname")
    private String txnBrandname;

    @Column(name = "txn_tid")
    private String txnTid;

    @Column(name = "txn_mid")
    private String txnMid;

    @Column(name = "txn_termlocation")
    private String txnTermlocation;

    @Column(name = "txn_foreign_txn_amt")
    private String txnForeignTxnAmt;

    @Column(name = "txn_foreign_exc_rate")
    private String txnForeignExcRate;

    @Column(name = "txn_status")
    private String txnStatus;

    @Column(name = "txn_adj_sign")
    private String txnAdjSign;

    @Column(name = "txn_original_id")
    private String txnOriginalId;

    @Column(name = "txn_appcode")
    private String txnAppcode;

    @Column(name = "txn_token")
    private String txnToken;

    @Column(name = "txn_bin")
    private String txnBin;

    @Column(name = "txn_type")
    private String txnType;

    @Column(name = "txn_termowner")
    private String txnTermowner;

    @Column(name = "txn_settlement_date")
    private String txnSettlementDate;

    @Column(name = "txn_post_date")
    private String txnPostDate;

    @Column(name = "txn_qr_merchant_ind")
    private String txnQrMerchantInd;

    @Column(name = "txn_foreign_ccy_code")
    private String txnForeignCcyCode;

    @Column(name = "txn_boundary")
    private String txnBoundary;

    @Column(name = "txn_sub_type")
    private String txnSubType;

    @Column(name = "txn_channel_lv4")
    private String txnChannelLv4;

    @Column(name = "txn_recurring_ind")
    private String txnRecurringInd;

    @Column(name = "txn_card_combo")
    private String txnCardCombo;

    @Column(name = "ds_partition_date")
    private Date dsPartitionDate;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "retry_count")
    private Integer retryCount;

    @Column(name = "txn_rank_check")
    private String txnRankCheck;
}