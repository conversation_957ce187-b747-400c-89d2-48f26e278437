package com.onemount.orchestration.entity;

import com.onemount.orchestration.constant.EFileType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "ps2_earning_error_group")
@NoArgsConstructor
public class Ps2EarningErrorGroup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    @Column(name = "file_type")
    @Convert(converter = EFileType.Converter.class)
    private EFileType fileType;

    @Column(name = "code")
    private String code;

    @Column(name = "message")
    private String message;

    @Column(name = "message_detail")
    private String messageDetail;

    @Column(name = "error_code")
    private String errorCode;

    @Column(name = "service_error_code")
    private String serviceErrorCode;
}