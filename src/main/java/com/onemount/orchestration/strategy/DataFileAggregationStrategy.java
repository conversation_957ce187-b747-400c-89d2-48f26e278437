package com.onemount.orchestration.strategy;

import com.oneid.oneloyalty.common.util.LogData;
import com.onemount.orchestration.model.ProcessData;
import org.apache.camel.AggregationStrategy;
import org.apache.camel.Exchange;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.PriorityQueue;

@Component
public class DataFileAggregationStrategy implements AggregationStrategy {
    @Override
    public Exchange aggregate(Exchange oldExchange, Exchange newExchange) {
        ProcessData<?> processData = newExchange.getIn().getBody(ProcessData.class);

        // Skip processing if processData is null or represents an empty line
        if (processData == null || isEmptyProcessData(processData)) {
            com.oneid.oneloyalty.common.util.Log.error(LogData.createLogData()
                    .append("msg", "Skipping aggregation for null or empty ProcessData in file: {}")
            );
            return oldExchange != null ? oldExchange : newExchange;
        }

        if (oldExchange == null) {
            // Initialize a new PriorityQueue for the first exchange
            PriorityQueue<ProcessData<?>> records = new PriorityQueue<>(
                    Comparator.comparing(pd -> pd.getLineIndex() != null ? pd.getLineIndex() : Integer.MAX_VALUE)
            );
            records.add(processData);
            newExchange.getIn().setBody(records);
            return newExchange;
        }

        // Validate that oldExchange body is a PriorityQueue
        Object body = oldExchange.getIn().getBody();
        if (!(body instanceof PriorityQueue)) {
            PriorityQueue<ProcessData<?>> records = new PriorityQueue<>(
                    Comparator.comparing(pd -> pd.getLineIndex() != null ? pd.getLineIndex() : Integer.MAX_VALUE)
            );
            records.add(processData);
            oldExchange.getIn().setBody(records);
            return oldExchange;
        }

        // Add processData to existing PriorityQueue
        PriorityQueue<ProcessData<?>> records = oldExchange.getIn().getBody(PriorityQueue.class);
        records.add(processData);
        oldExchange.getIn().setBody(records);
        return oldExchange;
    }

    public void completeAggregation(Exchange exchange) {
        Object body = exchange.getIn().getBody();
        if (body instanceof PriorityQueue) {
            PriorityQueue<ProcessData<?>> records = (PriorityQueue<ProcessData<?>>) body;
            List<ProcessData<?>> sortedList = new ArrayList<>();

            // Extract items in order from the priority queue
            while (!records.isEmpty()) {
                sortedList.add(records.poll());
            }
            exchange.getIn().setBody(sortedList);
        } else {
            exchange.getIn().setBody(Collections.emptyList());
        }
    }

    private boolean isEmptyProcessData(ProcessData<?> processData) {
        return processData.getRawData() == null || processData.getRawData().trim().isEmpty();
    }
}
