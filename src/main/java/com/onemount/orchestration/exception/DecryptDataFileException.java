package com.onemount.orchestration.exception;

import lombok.Getter;

@Getter
public class DecryptDataFileException extends RuntimeException {

    private String jsonInput;

    public DecryptDataFileException(String message) {
        super(message);
    }

    public DecryptDataFileException(String message, String data, Throwable cause) {
        super(message, cause);
        this.jsonInput = data;
    }
}