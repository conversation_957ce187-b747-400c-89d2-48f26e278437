package com.onemount.orchestration.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.oneid.oneloyalty.common.constant.PersistentEnum;
import com.oneid.oneloyalty.common.converter.PersistentEnumConverter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum EFileType implements PersistentEnum<String> {
    ONBOARD("onboard", "onboard"),
    CUSTOMER("customer", "customer"),
    TRANSACTION("transaction", "transaction"),
    EVENT("event", "event");

    private static final Map<String, EFileType> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (EFileType e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    EFileType(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final String value;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.value;
    }

    public static EFileType of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<EFileType, String> {
        public Converter() {
            super(EFileType.class);
        }
    }
}
