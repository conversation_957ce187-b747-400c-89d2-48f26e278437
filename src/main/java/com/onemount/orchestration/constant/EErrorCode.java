package com.onemount.orchestration.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Getter
public enum EErrorCode {

    // Common Error
    SUCCESS("00", "Success"),
    UNKNOWN("E5000", "Undefined"),

    // Validation Error E4000xx
    PAYLOAD_CANNOT_NULL("E400001", "Payload Can Not Be Null"),
    TIMESTAMP_CANNOT_NULL("E400002", "Timestamp Can Not Be Null"),
    LOYALTY_CUS_ID_CANNOT_EMPTY("E400003", "Loyalty Cus Id Can Not Be Empty"),
    MESSAGE_ID_DUPLICATED("E400004", "Message Id Duplicated"),

    // Business Error E4001xx
    LOYALTY_CUSTOMER_NOT_FOUND("E400101", "Loyalty Customer Not Found"),
    LOYALTY_CUSTOMER_NOT_ACTIVE("E400102", "Loyalty Customer Not Active"),
    NOT_PASS_ANY_SCHEME("E400103", "Not Pass Any Scheme"),
    INVOICE_NO_EXISTED("E400104", "Invoice No Existed"),
    TRANSACTION_CODE_NOT_FOUND("E400105", "Transaction Code Not Found"),
    TRANSACTION_CODE_NOT_ACTIVE("E400106", "Transaction Code Not Active"),
    ORIGINAL_TXN_NOT_FOUND("E400107", "Original Transaction Not Found"),
    ORIGINAL_TXN_ALREADY_REFUND("E400108", "Original Transaction Already Refunded"),
    LOYALTY_ACCOUNT_IS_BLOCKED("E400109", "Loyalty Account Is Blocked"),
    LOYALTY_ACCOUNT_BALANCE_INSUFFICIENT("E400110", "Loyalty Account Balance Insufficient"),

    // Validation Earn Event E4002xx
    EVENT_LIST_CANNOT_EMPTY("E400201", "Event List Can Not Be Empty"),
    EVENT_ID_CANNOT_EMPTY("E400202", "Event Id Can Not Be Empty"),
    EVENT_ID_LENGTH_INVALID("E400230", "Event Id Length Invalid"),
    EVENT_ID_DUPLICATED("E400203", "Event Id Duplicated"),
    EVENT_CODE_CANNOT_EMPTY("E400204", "Event Code Can Not Be Empty"),
    EVENT_AMOUNT_CANNOT_EMPTY("E400224", "Event Amount Can Not Be Empty"),
    EVENT_AMOUNT_INVALID_NUMBER_FORMAT("E400225", "Event Amount Invalid Number Format"),
    EVENT_DATE_CANNOT_EMPTY("E400226", "Event Date Can Not Be Empty"),
    EVENT_DATE_INVALID_FORMAT("E400227", "Event Date Invalid Format"),
    EVENT_NAME_CANNOT_EMPTY("E400221", "Event Name Can Not Be Empty"),
    EVENT_TYPE_CANNOT_EMPTY("E400219", "Event Type Can Not Be Empty"),
    EVENT_GROUP_CANNOT_EMPTY("E400217", "Event Group Can Not Be Empty"),
    EVENT_PRODUCT_CANNOT_EMPTY("E400223", "Event Product Can Not Be Empty"),
    EVENT_PRODUCT_LENGTH_INVALID("E400231", "Event Product Length Invalid"),
    EVENT_CODE_LENGTH_INVALID("E400216", "Event Code Length Invalid"),
    EVENT_GROUP_LENGTH_INVALID("E400218", "Event Group Length Invalid"),
    EVENT_TYPE_LENGTH_INVALID("E400220", "Event Type Length Invalid"),
    EVENT_NAME_LENGTH_INVALID("E400222", "Event Name Length Invalid"),

    // Validation Earn Transaction E4003xx
    TXN_LIST_CANNOT_EMPTY("E400301", "Txn List Can Not Be Empty"),
    TRANSACTION_ID_CANNOT_EMPTY("E400302", "Transaction Id Can Not Be Empty"),
    TRANSACTION_ID_DUPLICATED("E400303", "Transaction Id Duplicated"),
    TRANSACTION_ID_LENGTH_INVALID("E4003021", "Transaction Id Length Invalid"),
    ARRANGEMENT_ID_CANNOT_EMPTY("E400304", "Arrangement Id Can Not Be Empty"),
    CARD_ID_CANNOT_EMPTY("E400305", "Card Id Can Not Be Empty"),
    TXN_CODE_CANNOT_EMPTY("E400306", "Txn Code Can Not Be Empty"),
    TXN_TYPE_CANNOT_EMPTY("E400307", "Txn Type Can Not Be Empty"),
    TXN_CCY_CODE_CANNOT_EMPTY("E400308", "Txn Ccy Code Can Not Be Empty"),
    TXN_DATE_CANNOT_EMPTY("E400309", "Txn Date Can Not Be Empty"),
    TXN_DATE_INVALID("E400310", "Txn Date Invalid"),
    TXN_SLR_IND_CANNOT_EMPTY("E400311", "Txn Slr Ind Can Not Be Empty"),
    TXN_SLR_IND_INVALID("E400312", "Txn Slr Ind Invalid"),
    TXN_SLR_IND_LENGTH_INVALID("E4003121", "Txn Slr Ind Length Invalid"),
    TXN_TIME_CANNOT_EMPTY("E400313", "Txn Time Can Not Be Empty"),
    TXN_TIME_INVALID("E400314", "Txn Time Invalid Format"),
    TXN_GROSS_AMT_CANNOT_EMPTY("E400315", "Txn Gross Amt Can Not Be Empty"),
    TXN_GROSS_AMT_INVALID("E400316", "Txn Gross Amt Invalid"),
    TXN_NET_AMT_CANNOT_EMPTY("E400317", "Txn Net Amt Can Not Be Empty"),
    TXN_NET_AMT_INVALID("E400318", "Txn Net Amt Invalid"),
    TXN_FOREIGN_TXN_AMT_INVALID("E400319", "Txn Foreign Txn Amt Invalid"),
    TXN_STATUS_CANNOT_EMPTY("E400320", "Txn Status Can Not Be Empty"),
    TXN_STATUS_INVALID("E400321", "Txn Status Invalid"),
    TXN_STATUS_LENGTH_INVALID("E4003211", "Txn Status Length Invalid"),
    TXN_APPCODE_CANNOT_EMPTY("E400322", "Txn Appcode Can Not Be Empty"),
    TXN_TOKEN_CANNOT_EMPTY("E400323", "Txn Token Can Not Be Empty"),
    TXN_BIN_CANNOT_EMPTY("E400324", "Txn Bin Can Not Be Empty"),
    TXN_IN_OUT_CANNOT_EMPTY("E4003251", "Txn In Out Can Not Be Empty"),
    TXN_SETTLEMENT_DATE_CANNOT_EMPTY("E400326", "Txn Settlement Date Can Not Be Empty"),
    TXN_POST_DATE_CANNOT_EMPTY("E400327", "Txn Post Date Can Not Be Empty"),

    LOYALTY_CUS_ID_LENGTH_INVALID("E400325", "Loyalty Cus Id Length Invalid"),

    CARD_ID_LENGTH_INVALID("E4003271", "Card Id Length Invalid"),
    TXN_CODE_LENGTH_INVALID("E400328", "Txn Code Length Invalid"),
    TXN_TYPE_LENGTH_INVALID("E400329", "Txn Type Length Invalid"),
    TXN_IN_OUT_LENGTH_INVALID("E400330", "Txn In Out Length Invalid"),
    TXN_CHANNEL_LV1_LENGTH_INVALID("E400331", "Txn Channel Lv1 Length Invalid"),
    TXN_CHANNEL_LV2_LENGTH_INVALID("E400332", "Txn Channel Lv2 Length Invalid"),
    TXN_CHANNEL_LV3_LENGTH_INVALID("E400333", "Txn Channel Lv3 Length Invalid"),
    TXN_CHANNEL_LV4_LENGTH_INVALID("E4003331", "Txn Channel Lv4 Length Invalid"),
    TXN_MOTHER_CATEGORY_LENGTH_INVALID("E400334", "Txn Mother Category Length Invalid"),
    TXN_MCC_LENGTH_INVALID("E400335", "Txn Mcc Length Invalid"),
    TXN_SERVICE_LV1_LENGTH_INVALID("E400336", "Txn Service Lv1 Length Invalid"),
    TXN_CCY_CODE_LENGTH_INVALID("E400337", "Txn Ccy Code Length Invalid"),
    TXN_ADJ_REASON_LENGTH_INVALID("E400338", "Txn Adj Reason Length Invalid"),
    TXN_DATE_LENGTH_INVALID("E400339", "Txn Date Length Invalid"),
    TXN_SERVICE_LV2_LENGTH_INVALID("E400340", "Txn Service Lv2 Length Invalid"),
    TXN_PURPOSE_LENGTH_INVALID("E400341", "Txn Purpose Length Invalid"),
    TXN_MERCHANT_PURPOSE_LENGTH_INVALID("E400342", "Txn Merchant Purpose Length Invalid"),
    TXN_SUPPLIER_LENGTH_INVALID("E400343", "Txn Supplier Length Invalid"),
    TXN_GROSS_AMT_LENGTH_INVALID("E400344", "Txn Gross Amt Length Invalid"),
    TXN_NET_AMT_LENGTH_INVALID("E400345", "Txn Net Amt Length Invalid"),
    TXN_TERMINALNAME_LENGTH_INVALID("E400346", "Txn Terminalname Length Invalid"),
    TXN_BRANDNAME_LENGTH_INVALID("E400347", "Txn Brandname Length Invalid"),
    TXN_TID_LENGTH_INVALID("E400348", "Txn Tid Length Invalid"),
    TXN_MID_LENGTH_INVALID("E400349", "Txn Mid Length Invalid"),
    TXN_TERMLOCATION_LENGTH_INVALID("E400350", "Txn Termlocation Length Invalid"),
    TXN_FOREIGN_AMT_LENGTH_INVALID("E400351", "Txn Foreign Amt Length Invalid"),
    TXN_ADJ_SIGN_LENGTH_INVALID("E400352", "Txn Adj Sign Length Invalid"),
    TXN_ORIGINAL_ID_LENGTH_INVALID("E400353", "Txn Original Id Length Invalid"),
    TXN_BIN_LENGTH_INVALID("E400354", "Txn Bin Length Invalid"),
    ARRANGEMENT_ID_LENGTH_INVALID("E400355", "Arrangement Id Length Invalid"),
    TXN_TIME_LENGTH_INVALID("E400356", "Txn Time Length Invalid"),
    TXN_APPCODE_LENGTH_INVALID("E400357", "Txn Appcode Length Invalid"),
    TXN_TOKEN_LENGTH_INVALID("E400358", "Txn Token Length Invalid"),
    TXN_SETTLEMENT_DATE_INVALID("E400359", "Txn Settlement Date Invalid"),
    TXN_SETTLEMENT_DATE_LENGTH_INVALID("E4003591", "Txn Settlement Date Length Invalid"),
    TXN_POST_DATE_INVALID("E400360", "Txn Post Date Invalid"),
    TXN_POST_DATE_LENGTH_INVALID("E4003601", "Txn Post Date Length Invalid"),
    TXN_QR_MERCHANT_IND_CANNOT_EMPTY("E400361", "Txn Qr Merchant Ind Can Not Be Empty"),
    TXN_QR_MERCHANT_IND_INVALID("E400362", "Txn Qr Merchant Ind Invalid"),
    TXN_QR_MERCHANT_IND_LENGTH_INVALID("E4003621", "Txn Qr Merchant Ind Length Invalid"),
    TXN_FOREIGN_EXC_RATE_LENGTH_INVALID("E400363", "Txn Foreign Exc Rate Length Invalid"),
    TXN_TERMOWNER_LENGTH_INVALID("E400364", "Txn Termowner Length Invalid"),
    TXN_FOREIGN_CCY_CODE_LENGTH_INVALID("E400365", "Txn Foreign Ccy Code Length Invalid"),
    TXN_BOUNDARY_LENGTH_INVALID("E400366", "Txn Boundary Length Invalid"),
    TXN_SUB_TYPE_LENGTH_INVALID("E400367", "Txn SubType Length Invalid"),
    TXN_CATEGORY_GROUP_LENGTH_INVALID("E400368", "Txn CategoryGroup Length Invalid"),
    TXN_PRODUCT_LINE_LENGTH_INVALID("E400369", "Txn Product Line Length Invalid"),
    TXN_PRODUCT_FAMILY_LENGTH_INVALID("E400370", "Txn Product Family Length Invalid"),

    FILE_FORMAT_INVALID("E400600", "File Format is Invalid"),
    FILE_ALREADY_EXIST("E400601", "File Already Exist"),
    FILE_WAS_PROCESSED("E400602", "File was processed"),
    FILE_DECRYPT_FAILED("E400603", "File Decrypt Failed"),
    CONTROL_FILE_IS_EMPTY("E400604", "Control File Is Empty"),
    DATA_FILE_NOT_FOUND("E400605", "Data File Not Found"),
    PROCESS_DATA_FILE_FAILED("E400606", "Process Data File Failed"),
    DATA_FILE_DUPLICATED("E400607", "Data File Duplicated"),
    DATA_FILE_ERROR("E400608", "One or more data files have errors"),
    READ_DATA_FILE_ERROR("E400609", "Read data file error"),
    ;

    private static final Map<String, EErrorCode> mapByValue;

    private static final Map<ServiceErrorCode, EErrorCode> mapByServiceErrorCode;

    static {
        mapByValue = new HashMap<>();
        for (EErrorCode e : values()) {
            mapByValue.put(e.getCode(), e);
        }
    }

    private final String code;

    private final String message;


    EErrorCode(String value, String message) {
        this.code = value;
        this.message = message;
    }

    @JsonValue
    public String getCode() {
        return this.code;
    }

    public static EErrorCode of(String value) {
        return mapByValue.get(value);
    }

    public static EErrorCode convert(Integer serviceErrorCode) {
        EErrorCode errorCode = mapByServiceErrorCode.get(ServiceErrorCode.of(serviceErrorCode));
        return Objects.nonNull(errorCode) ? errorCode : EErrorCode.UNKNOWN;
    }

    static {
        mapByServiceErrorCode = new HashMap<>();

        // Common Error
        mapByServiceErrorCode.put(ServiceErrorCode.SUCCESS, EErrorCode.SUCCESS);

        mapByServiceErrorCode.put(ServiceErrorCode.SERVER_ERROR, EErrorCode.UNKNOWN);

        // Business Error
        mapByServiceErrorCode.put(ServiceErrorCode.MEMBER_PRODUCT_ACCOUNT_NOT_FOUND, EErrorCode.LOYALTY_CUSTOMER_NOT_FOUND);
        mapByServiceErrorCode.put(ServiceErrorCode.MEMBER_NOT_FOUND, EErrorCode.LOYALTY_CUSTOMER_NOT_FOUND);
        mapByServiceErrorCode.put(ServiceErrorCode.USER_PROFILE_NOT_FOUND, EErrorCode.LOYALTY_CUSTOMER_NOT_FOUND);

        mapByServiceErrorCode.put(ServiceErrorCode.MEMBER_PRODUCT_ACCOUNT_NOT_ACTIVE, EErrorCode.LOYALTY_CUSTOMER_NOT_ACTIVE);
        mapByServiceErrorCode.put(ServiceErrorCode.MEMBER_NOT_ACTIVE, EErrorCode.LOYALTY_CUSTOMER_NOT_ACTIVE);
        mapByServiceErrorCode.put(ServiceErrorCode.USER_PROFILE_NOT_ACTIVE, EErrorCode.LOYALTY_CUSTOMER_NOT_ACTIVE);

        mapByServiceErrorCode.put(ServiceErrorCode.NOT_PASS_ANY_SCHEME, EErrorCode.NOT_PASS_ANY_SCHEME);

        mapByServiceErrorCode.put(ServiceErrorCode.INVOICE_NO_EXISTED, EErrorCode.INVOICE_NO_EXISTED);

        mapByServiceErrorCode.put(ServiceErrorCode.TRANSACTION_CODE_NOT_FOUND, EErrorCode.TRANSACTION_CODE_NOT_FOUND);

        mapByServiceErrorCode.put(ServiceErrorCode.TRANSACTION_CODE_NOT_ACTIVE, EErrorCode.TRANSACTION_CODE_NOT_ACTIVE);

        mapByServiceErrorCode.put(ServiceErrorCode.TRANSACTION_NOT_FOUND, EErrorCode.ORIGINAL_TXN_NOT_FOUND);

        mapByServiceErrorCode.put(ServiceErrorCode.ORIGINAL_INVOICE_NO_IS_REFUND, EErrorCode.ORIGINAL_TXN_ALREADY_REFUND);

        mapByServiceErrorCode.put(ServiceErrorCode.ACCOUNT_IS_BLOCKED, EErrorCode.LOYALTY_ACCOUNT_IS_BLOCKED);

        mapByServiceErrorCode.put(ServiceErrorCode.ACCOUNT_BALANCE_INSUFFICIENT, EErrorCode.LOYALTY_ACCOUNT_BALANCE_INSUFFICIENT);
    }
}
