package com.onemount.orchestration.constant;

public enum ScaleErrorCode {
    SUCCESS(0, "Success"),
    FAIL_TO_PARSE_BODY(1, "Fail to parse body"),
    K8S_NOT_FOUND_WORKLOAD(2, "Not found workload"),
    K8S_UPDATE_LOWERSCALE(3, "Update lower scale"),
    K8S_UPDATE_UPPERSCALE(4, " Update upper scale"),
    RESIZE_HPA_MISSING_BUSINESS_FIELD(5, "Business field resize missing"),
    RESIZE_HPA_NOT_FOUND_BUSINESS_CODE(6, " Business code not found"),
    ;

    private final int code;
    private final String messageTemplate;

    ScaleErrorCode(int code, String messageTemplate) {
        this.code = code;
        this.messageTemplate = messageTemplate;
    }

    public int getCode() {
        return code;
    }

    public String getMessage(Object... args) {
        return String.format(messageTemplate, args);
    }
} 
