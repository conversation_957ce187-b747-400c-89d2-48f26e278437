package com.onemount.orchestration.constant;

public interface Constant {

    String CREATED_BY_SYSTEM = "SYSTEM";

    class Header {
        public static final String PROCESS_ID = "process-id";
        public static final String THREAD_NUMS = "ThreadNums";
        public static final String DATA_FILE_NAME = "DataFileName";
        public static final String CONTROL_FILE_NAME = "ControlFileName";
        public static final String ORDER_KEY = "OrderKey";
        public static final String DATA_OUTPUT_FILE_NAME = "DataOutputFileName";
        public static final String CONTROL_FILE_TYPE = "ControlFileType";
    }

    class Properties {
        public static final String CONTROL_FILE_SKIP = "ControlFileSkip";
        public static final String DATA_FILE_SKIP = "DataFileSkip";
        public static final String ORDER_THREAD_INDEX = "OrderThreadIndex";
        public static final String DATA_FILE_TOTAL_LINES = "DataFileTotalLines";
        public static final String PROCESSED_FILE =  "ProcessedFile";
        public static final String DATA_FILE_TYPE =  "DataFileType";
        public static final String COB_DATE =  "CobDate";
    }
}
