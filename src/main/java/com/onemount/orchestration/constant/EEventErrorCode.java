package com.onemount.orchestration.constant;

import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public enum EEventErrorCode {
    SUCCESS("000", "Success"),
    INTERNAL_SERVER_ERROR("001", "Internal server error"),
    LOYALTY_CUSTOMER_NOT_FOUND("002", "Loyalty customer not found"),
    LOYALTY_ACCOUNT_IS_BLOCKED("003", "Loyalty account is blocked"),
    DUPLICATED_INVOICE_NO("004", "Duplicated invoice no"),
    CUSTOMER_ACCOUNT_BALANCE_NOT_SUFFICIENT("005", "Customer account balance not sufficient");

    private static final Map<String, EEventErrorCode> mapByValue;

    private static final Map<EErrorCode, EEventErrorCode> mapByErrorCode;

    static {
        mapByValue = new HashMap<>();
        for (EEventErrorCode e : values()) {
            mapByValue.put(e.getCode(), e);
        }
    }

    static {
        mapByErrorCode = new HashMap<>();

        mapByErrorCode.put(EErrorCode.SUCCESS, EEventErrorCode.SUCCESS);

        mapByErrorCode.put(EErrorCode.UNKNOWN, EEventErrorCode.INTERNAL_SERVER_ERROR);

        mapByErrorCode.put(EErrorCode.EVENT_ID_DUPLICATED, EEventErrorCode.DUPLICATED_INVOICE_NO);
        mapByErrorCode.put(EErrorCode.MESSAGE_ID_DUPLICATED, EEventErrorCode.DUPLICATED_INVOICE_NO);
        mapByErrorCode.put(EErrorCode.INVOICE_NO_EXISTED, EEventErrorCode.DUPLICATED_INVOICE_NO);

        mapByErrorCode.put(EErrorCode.LOYALTY_CUSTOMER_NOT_FOUND, EEventErrorCode.LOYALTY_CUSTOMER_NOT_FOUND);

        mapByErrorCode.put(EErrorCode.LOYALTY_ACCOUNT_IS_BLOCKED, EEventErrorCode.LOYALTY_ACCOUNT_IS_BLOCKED);

        mapByErrorCode.put(EErrorCode.LOYALTY_ACCOUNT_BALANCE_INSUFFICIENT, EEventErrorCode.CUSTOMER_ACCOUNT_BALANCE_NOT_SUFFICIENT);
    }

    private final String code;

    private final String message;

    EEventErrorCode(String value, String message) {
        this.code = value;
        this.message = message;
    }

    public static EEventErrorCode of(String value) {
        return mapByValue.get(value);
    }

    public static EEventErrorCode convert(EErrorCode errorCode) {
        EEventErrorCode tcbErrorCode = mapByErrorCode.get(errorCode);
        return Objects.nonNull(tcbErrorCode) ? tcbErrorCode : EEventErrorCode.INTERNAL_SERVER_ERROR;
    }

    @JsonValue
    public String getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}