package com.onemount.orchestration.constant;

import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public enum ErrorCode {
    SUCCESS("00", "Success"),
    UNKNOWN("E5000", "Undefined"),
    INVALID("E400000", "Invalid"),

    // Validation Error E4000xx
    PAYLOAD_CANNOT_NULL("E400001", "Payload Can Not Be Null"),
    TIMESTAMP_CANNOT_NULL("E400002", "Timestamp Can Not Be Null"),
    LOYALTY_CUS_ID_CANNOT_NULL("E400003", "Loyalty Cus Id Can Not Be Null"),
    MESSAGE_ID_DUPLICATED("E400004", "Message Id Duplicated"),
    INVALID_DOB("E400005", "DOB Invalid"),
    INVALID_GENDER("E400006", "Gender Invalid"),
    INVALID_TIER_EXPIRED("E400007", "Tier Expire Date Invalid"),
    TIER_CODE_CANNOT_NULL("E400008", "Tier Code Can Not Be Null"),
    LOYALTY_CUS_ID_INVALID("E400009", "Loyalty Cus Id Invalid"),

    // Business Error E4001xx
    CUSTOMER_EXISTED("E400101", "Customer Existed"),
    TIER_INVALID("E400102", "Tier Invalid"),

    // Validation Customer Info E4002xx
    LENGTH_INVALID_FORMAT("E4000001", "Length Invalid"),
    LOYALTY_OB_DATE_CANNOT_EMPTY("E400201", "Loyalty Ob Date Can Not Be Empty"),
    LOYALTY_OB_DATE_INVALID("E400202", "Loyalty Ob Date Invalid"),
    CST_GENDER_CANNOT_EMPTY("E400203", "Cst Gender Can Not Be Empty"),
    CST_GENDER_INVALID("E400204", "Cst Gender Invalid"),
    FRST_CTC_DT_INVALID("E400205", "Frst Ctc Dt Invalid"),
    CST_BRTH_DT_INVALID("E400206", "Cst Brth Dt Invalid"),
    TIER_START_DATE_INVALID("E400207", "Tier Start Date Invalid"),
    TIER_END_DATE_INVALID("E400208", "Tier End Date Invalid"),
    EMPLOYEE_IND_INVALID("E400209", "Employee Ind Invalid"),
    CST_MOBILE_IND_INVALID("E400210", "Cst Mobile Ind Invalid"),
    CST_FIRST_LOGIN_DATE_INVALID("E400211", "Cst First Login Date Invalid"),
    CST_LAST_LOGIN_DATE_INVALID("E400212", "Cst Last Login Date Invalid"),
    CST_EKYC_STATUS_INVALID("E400213", "Cst Ekyc Status Invalid"),
    CST_PRD_HOLDING_INVALID("E400214", "Cst Prd Holding Invalid"),
    CST_PRD_ACTIVE_INVALID("E400215", "Cst Prd Active Invalid"),

    // Validation Product Info E4003xx
    ARRANGEMENT_ID_CANNOT_EMPTY("E400301", "Arrangement Id Can Not Be Empty"),
    ARRANGEMENT_ID_DUPLICATED("E4003022", "Arrangement Id Duplicated"),
    PRODUCT_CODE_CANNOT_EMPTY("E400303", "Product Code Can Not Be Empty"),
    PRODUCT_LV1_CANNOT_EMPTY("E400304", "Product Lv1 Can Not Be Empty"),
    PRODUCT_LV2_CANNOT_EMPTY("E400305", "Product Lv2 Can Not Be Empty"),
    PRODUCT_LV3_CANNOT_EMPTY("E400306", "Product Lv3 Can Not Be Empty"),
    PRODUCT_LV4_CANNOT_EMPTY("E400307", "Product Lv4 Can Not Be Empty"),
    PROD_OPEN_DATE_INVALID("E4003081", "Prod Open Date Invalid"),
    PROD_EXPIRE_DATE_INVALID("E4003091", "Prod Expire Date Invalid"),
    PRD_ROLLOVER_DATE_INVALID("E4003101", "Prd Rollover Date Invalid"),

    // Validation Card Info E4004xx
    CARD_ID_CANNOT_EMPTY("E400401", "Card Id Can Not Be Empty"),
    CARD_ID_DUPLICATED("E400402", "Card Id Duplicated"),
    CARD_IND_CANNOT_EMPTY("E400403", "Card Ind Can Not Be Empty"),
    CARD_EXPIRE_DATE_CANNOT_EMPTY("E400404", "Card Expire Date Can Not Be Empty"),
    CARD_EXPIRE_DATE_INVALID("E400405", "Card Expire Date Invalid"),
    CARD_OPEN_DATE_INVALID("E400406", "Card Open Date Invalid"),
    CARD_ACTIVATED_DATE_INVALID("E400407", "Card Activated Date Invalid"),
    CARD_BILL_CYCLE_DATE_INVALID("E400408", "Card Bill Cycle Date Invalid"),
    CARD_PAYMENT_DATE_INVALID("E400409", "Card Payment Date Invalid"),
    CARD_PAN_CANNOT_EMPTY("E400410", "Card Pan Can Not Be Empty"),

    // Validation Data Length E4005xx
    LOYALTY_CUS_ID_LENGTH_INVALID("E400325", "Loyalty Cus Id Length Invalid"),
    LOYALTY_OB_DATE_LENGTH_INVALID("E400502", "Loyalty Ob Date Length Invalid"),
    FRST_CTC_DT_LENGTH_INVALID("E400503", "Frst Ctc Dt Length Invalid"),
    CST_BRTH_DT_LENGTH_INVALID("E400504", "Cst Brth Dt Length Invalid"),
    TIER_START_DATE_LENGTH_INVALID("E400505", "Tier Start Date Length Invalid"),
    TIER_END_DATE_LENGTH_INVALID("E400506", "Tier End Date Length Invalid"),
    CST_PRE_TIER_LENGTH_INVALID("E400507", "Cst Pre Tier Length Invalid"),
    CST_TIER_LENGTH_INVALID("E400508", "Cst Tier Length Invalid"),
    CST_LOYALTY_TIER_LENGTH_INVALID("E400509", "Cst Loyalty Tier Length Invalid"),
    TARGET_SEGMENT_CODE_LENGTH_INVALID("E400510", "Target Segment Code Length Invalid"),
    CST_NATIONALITY_LENGTH_INVALID("E400511", "Cst Nationality Length Invalid"),
    BIZ_LINE_LENGTH_INVALID("E400501", "Biz Line Length Invalid"),
    CST_FIRST_LOGIN_DATE_LENGTH_INVALID("E400512", "Cst First Login Date Length Invalid"),
    CST_LAST_LOGIN_DATE_LENGTH_INVALID("E400513", "Cst Last Login Date Length Invalid"),
    CST_AST_CLSS_LENGTH_INVALID("E400514", "Cst Ast Clss Length Invalid"),
    CST_BANK_SEGMENT_LENGTH_INVALID("E400515", "Cst Bank Segment Length Invalid"),
    CST_PBT_LENGTH_INVALID("E400516", "Cst Pbt Length Invalid"),
    CST_TOI_LENGTH_INVALID("E400517", "Cst Toi Length Invalid"),
    CST_COST_LENGTH_INVALID("E400518", "Cst Cost Length Invalid"),
    CST_FAMILY_GROUP_LENGTH_INVALID("E400519", "Cst Family Group Length Invalid"),
    CST_FAMILY_MEMBER_TYPE_LENGTH_INVALID("E400520", "Cst Family Member Type Length Invalid"),
    CST_OCCUPATION_LENGTH_INVALID("E400521", "Cst Occupation Length Invalid"),
    CST_HOME_BRANCH_LENGTH_INVALID("E400522", "Cst Home Branch Length Invalid"),
    CST_SUB_BRANCH_LENGTH_INVALID("E400523", "Cst Sub Branch Length Invalid"),
    CST_RM_BRANCH_LENGTH_INVALID("E400524", "Cst Rm Branch Length Invalid"),
    CST_MERCHANT_IND_LENGTH_INVALID("E400525", "Cst Merchant Ind Length Invalid"),
    CST_NOTI_CHANNEL_LENGTH_INVALID("E400526", "Cst Noti Channel Length Invalid"),
    CST_BLK_LV_LENGTH_INVALID("E400527", "Cst Blk Lv Length Invalid"),
    CST_WTCH_LIST_LENGTH_INVALID("E400528", "Cst Wtch List Length Invalid"),
    ARRANGEMENT_ID_LENGTH_INVALID("E400529", "Arrangement Id Length Invalid"),
    PRODUCT_CODE_LENGTH_INVALID("E400530", "Product Code Length Invalid"),
    PRODUCT_LV1_LENGTH_INVALID("E400531", "Product Lv1 Length Invalid"),
    PRODUCT_LV2_LENGTH_INVALID("E400532", "Product Lv2 Length Invalid"),
    PRODUCT_LV3_LENGTH_INVALID("E400533", "Product Lv3 Length Invalid"),
    PRODUCT_LV4_LENGTH_INVALID("E400534", "Product Lv4 Length Invalid"),
    PRODUCT_LV5_LENGTH_INVALID("E400535", "Product Lv5 Length Invalid"),
    PRD_BRANCH_CODE_LENGTH_INVALID("E400536", "Prd Branch Code Length Invalid"),
    PROD_OPEN_DATE_LENGTH_INVALID("E400537", "Prod Open Date Length Invalid"),
    PROD_EXPIRE_DATE_LENGTH_INVALID("E400538", "Prod Expire Date Length Invalid"),
    PRD_BILL_CYCLE_LENGTH_INVALID("E400539", "Prd Bill Cycle Length Invalid"),
    PRD_STATUS_LENGTH_INVALID("E400540", "Prd Status Length Invalid"),
    PRD_CCY_CODE_LENGTH_INVALID("E400541", "Prd Ccy Code Length Invalid"),
    PRD_TERM_LENGTH_INVALID("E400542", "Prd Term Length Invalid"),
    PRD_TERM_UNIT_LENGTH_INVALID("E400543", "Prd Term Unit Length Invalid"),
    EOP_BAL_LENGTH_INVALID("E400544", "Eop Bal Length Invalid"),
    PRD_ROLLOVER_DATE_LENGTH_INVALID("E400545", "Prd Rollover Date Length Invalid"),
    PRD_HOLDING_NBR_LENGTH_INVALID("E400546", "Prd Holding Nbr Length Invalid"),
    CARD_ID_LENGTH_INVALID("E400547", "Card Id Length Invalid"),
    CARD_IND_LENGTH_INVALID("E400548", "Card Ind Length Invalid"),
    CARD_EXPIRE_DATE_LENGTH_INVALID("E400549", "Card Expire Date Length Invalid"),
    CARD_STATUS_LENGTH_INVALID("E400550", "Card Status Length Invalid"),
    CARD_BLOCK_CODE_LENGTH_INVALID("E400551", "Card Block Code Length Invalid"),
    CARD_OPEN_DATE_LENGTH_INVALID("E400552", "Card Open Date Length Invalid"),
    CARD_ACTIVATED_DATE_LENGTH_INVALID("E400553", "Card Activated Date Length Invalid"),
    CARD_LIMIT_LENGTH_INVALID("E400554", "Card Limit Length Invalid"),
    CARD_ANNUAL_FEE_PAYMENT_LENGTH_INVALID("E400555", "Card Annual Fee Payment Length Invalid"),
    CARD_BILL_CYCLE_DATE_LENGTH_INVALID("E400556", "Card Bill Cycle Date Length Invalid"),
    CARD_PAYMENT_DATE_LENGTH_INVALID("E400557", "Card Payment Date Length Invalid"),
    CST_TIER_PROGRAM_LENGTH_INVALID("E400558", "Cst Tier Program Length Invalid"),
    PRODUCT_LV6_LENGTH_INVALID("E400559", "Product Lv6 Length Invalid"),
    CST_RESIDENCE_LENGTH_INVALID("E400560", "Cst Residence Length Invalid"),
    CARD_PAN_LENGTH_INVALID("E400561", "Card Pan Length Invalid"),

    // Validation Cst Target Group E4006xx
    START_DATE_INVALID("E400601", "Start Date Invalid"),
    END_DATE_INVALID("E400602", "End Date Invalid"),
    START_DATE_OR_END_DATE_INVALID("E400603", "Start date must be required if end date has been entered and vice versa"),
    START_DATE_MUST_BE_LESS_THAN_END_DATE("E400604", "start_date must be less than end_date"),
    STATUS_CANNOT_EMPTY("E400605", "Status Can Not Be Empty"),
    STATUS_INVALID("E400606", "Status Invalid"),

    ;

    private static final Map<String, ErrorCode> mapByValue;

    private static final Map<ServiceErrorCode, ErrorCode> mapByServiceErrorCode;


    static {
        mapByValue = new HashMap<>();
        for (ErrorCode e : values()) {
            mapByValue.put(e.getCode(), e);
        }
    }

    ErrorCode(String value, String displayName) {
        this.code = value;
        this.message = displayName;
    }

    private final String code;

    private final String message;

    private int value;

    ErrorCode(String code, String message, int value) {
        this.code = code;
        this.message = message;
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return this.name();
    }

    @JsonValue
    public String getCode() {
        return this.code;
    }

    public static ErrorCode of(String value) {
        if (value != null) {
            for (ErrorCode e : values()) {
                if (value.equals(e.value))
                    return e;
            }
        }
        return null;
    }

    public String getMessage() {
        return this.message;
    }

    public static ErrorCode convert(Integer serviceErrorCode) {
        ErrorCode errorCode = mapByServiceErrorCode.get(ServiceErrorCode.of(serviceErrorCode));
        return Objects.nonNull(errorCode) ? errorCode : ErrorCode.UNKNOWN;
    }

    public static ErrorCode fromCode(String code) {
        if (code != null) {
            ErrorCode errorCode = mapByValue.get(code);
            return errorCode != null ? errorCode : UNKNOWN;
        }
        return UNKNOWN;
    }

    static {
        mapByServiceErrorCode = new HashMap<>();

        // Common Error
        mapByServiceErrorCode.put(ServiceErrorCode.SUCCESS, ErrorCode.SUCCESS);

        mapByServiceErrorCode.put(ServiceErrorCode.SERVER_ERROR, ErrorCode.UNKNOWN);

        //Business Error
        mapByServiceErrorCode.put(ServiceErrorCode.MEMBER_ALREADY_EXISTED_IN_PROGRAM, ErrorCode.CUSTOMER_EXISTED);

        mapByServiceErrorCode.put(ServiceErrorCode.PROGRAM_TIER_NOT_FOUND, ErrorCode.TIER_INVALID);

        mapByServiceErrorCode.put(ServiceErrorCode.PROGRAM_TIER_NOT_ACTIVE, ErrorCode.TIER_INVALID);
    }
}
