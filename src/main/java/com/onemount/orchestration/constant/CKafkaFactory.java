package com.onemount.orchestration.constant;

public class CKafkaFactory {

    public static final String CONFLUENT_KAFKA_CONSUMER = "KafkaConfluentListenerContainerFactory";
    public static final String CONFLUENT_KAFKA_PRODUCER = "KafkaConfluentProducerContainerFactory";
    public static final String OL_SERVICE_KAFKA_CONSUMER = "KafkaOneLoyaltyServiceListenerContainerFactory";

    public static final String KAFKA_EXTERNAL_CONSUMER = "KafkaExternalListenerContainerFactory";
    public static final String KAFKA_EXTERNAL_PRODUCER = "KafkaExternalProducerContainerFactory";
}
