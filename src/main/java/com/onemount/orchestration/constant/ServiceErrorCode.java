package com.onemount.orchestration.constant;

import com.fasterxml.jackson.annotation.JsonFormat;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ServiceErrorCode {

    SUCCESS(200),
    NOT_FOUND(404),
    BAD_REQUEST(400),
    SERVER_ERROR(500),
    UNAUTHORIZED(401),
    FEATURE_ACCESS_DENIED(********),

    /*
     * Business - 404001XX
     */
    BUSINESS_NOT_FOUND(4040101),
    BUSINESS_NOT_ACTIVE(4040102),
    CORPORATION_NOT_FOUND(4040103),
    CORPORATION_NOT_ACTIVE(4040104),
    CHAIN_NOT_FOUND(4040105),
    CHAIN_NOT_ACTIVE(4040106),
    STORE_NOT_FOUND(4040107),
    POS_NOT_FOUND(4040108),
    TERMINAL_NOT_ACTIVE(4040109),
    PROGRAM_NOT_FOUND(4040110),
    PROGRAM_NOT_ACTIVE(4040111),
    MEMBER_NOT_FOUND(4040112),
    MEMBER_NOT_ACTIVE(4040113),
    MEMBER_PRODUCT_ACCOUNT_NOT_ACTIVE(4040114),
    MEMBER_PRODUCT_ACCOUNT_NOT_FOUND(4040115),
    MEMBER_PRODUCT_ACCOUNT_CODE_ALREADY_EXISTED(4040116),

    PROGRAM_TIER_NOT_FOUND(4040117),
    PROGRAM_TIER_NOT_ACTIVE(4040118),
    PROGRAM_TIER_CODE_EXISTED(4040119),
    TIER_RULE_CONDITION_NOT_FOUND(4040140),
    DUPLICATE_TIER_RULE_CONDITION_VALUE(4040141),
    TIER_RULE_ATTRIBUTE_NOT_VALID(4040142),
    TIER_RULE_CONDITION_OPERATOR_UNSUPPORTED(4040143),
    TIER_RULE_ATTRIBUTE_DATA_TYPE_UNSUPPORTED(4040144),
    TIER_RULE_CONDITION_VALUE_NOT_VALID(4040145),
    MEMBER_IS_CLOSED(4040146),

    STORE_NOT_ACTIVE(4040120),

    PROGRAM_ACCUMULATION_TYPE_CONFLICT(4070120),
    PROGRAM_ACCUMULATION_TYPE_NOT_FOUND(4040121),
    CURRENCY_NOT_FOUND(4040122),
    CURRENCY_RATE_NOT_FOUND(4040123),
    CURRENCY_BASE_CURRENCY_EQUAL(4040124),
    BASE_CURRENCY_NOT_FOUND(4040125),
    CURRENCY_NOT_ACTIVE(4040126),
    CURRENCY_RATE_NOT_ACTIVE(4040127),

    USER_PROFILE_NOT_FOUND(4040128),
    USER_PROFILE_NOT_ACTIVE(4040129),

    POOL_NOT_FOUND(4040130),
    POOL_NOT_ACTIVE(4040131),
    BUSINESS_CONFLICT_CODE(4040132),
    BUSINESS_CONFLICT_NAME(4040133),
    BUSINESS_CONFLICT_PHONE(4040134),
    COUNTRY_NOT_FOUND(4040135),
    PROVINCE_NOT_FOUND(4040136),
    DISTRICT_NOT_FOUND(4040137),
    WARD_NOT_FOUND(4040138),
    PROGRAM_PRODUCT_NOT_FOUND(4041320),
    PROGRAM_PRODUCT_NOT_ACTIVE(4041321),
    ATTRIBUTE_NOT_FOUND(4041322),
    OPERATOR_NOT_FOUND(4041323),
    CARD_TYPE_NOT_FOUND(4041324),
    CARD_TYPE_NOT_ACTIVE(4041325),
    CARD_TYPE_NOT_MATCHED(4041347),
    CARD_BIN_NOT_FOUND(4041326),
    CARD_BIN_NOT_ACTIVE(4041327),
    CARD_POLICY_NOT_FOUND(4041328),
    CARD_POLICY_NOT_ACTIVE(4041329),
    CARD_TMP_NOT_FOUND(4041330),
    CARD_TMP_NOT_PENDING(4041331),
    CARD_PRODUCTION_REQUEST_NOT_FOUND(4041332),
    CARD_PRODUCTION_REQUEST_NOT_ACTIVE(4041333),
    CARD_TRANSFER_NOT_FOUND(4041334),
    CARD_TRANSFER_NOT_PENDING(4041335),
    DISTRICT_NOT_BELONG_PROVINCE(4041336),
    WARD_NOT_BELONG_DISTRICT(4041337),
    GIFT_CARD_NOT_FOUND(4041338),
    GIFT_CARD_NOT_ACTIVE(4041339),
    GIFT_CARD_PRODUCTION_REQUEST_NOT_FOUND(4041340),
    GIFT_CARD_PRODUCTION_REQUEST_NOT_ACTIVE(4041341),
    GIFT_CARD_TYPE_NOT_FOUND(4041342),
    GIFT_CARD_TYPE_NOT_ACTIVE(4041343),
    GIFT_CARD_BIN_NOT_FOUND(4041344),
    GIFT_CARD_BIN_NOT_ACTIVE(4041345),
    GIFT_CARD_PRODUCTION_REQUEST_NOT_PENDING(4041346),
    GIFT_CARD_PRODUCTION_IN_PROGRESS(4041359),
    GCPR_INIT_STATUS_CAN_NOT_UPDATE(4041360),
    GIFT_CARD_STATUS_CAN_NOT_UPDATE(4041361),
    BENEFIT_PROVIDER_SCHEME_NOT_FOUND(4041362),
    BENEFIT_PROVIDER_NOT_FOUND(4041363),

    CORPORATION_CODE_EXISTED(4041347),
    CHAIN_NAME_EXISTED(4041348),
    STORE_NAME_EXISTED(4041349),
    TERMINAL_NAME_EXISTED(4041350),
    CARD_TRANSFER_NOT_NEW(4041351),
    PROVINCE_NOT_BELONG_COUNTRY(4041352),
    CORPORATION_NOT_IN_BUSINESS(4041353),
    PROGRAM_CORPORATION_NOT_ACTIVE(4041354),
    PROGRAM_CORPORATION_NOT_FOUND(4041355),

    CARD_TRANS_TMP_NOT_FOUND(4041356),
    CARD_TRANS_TMP_NOT_PENDING(4041357),

    CARD_POLICY_TYPE_INCORRECT(4041358),

    PROGRAM_ATTRIBUTE_NOT_FOUND(4041363),
    PROGRAM_ATTRIBUTE_NOT_ACTIVE(4041364),
    PROGRAM_ATTRIBUTE_VALUE_NOT_MATCHES(4041365),
    CHECKLOG_NOT_FOUND(4041366),
    MISSING_LAST_4_CARD_DIGIT(4041367),
    GIFT_CARD_BATCH_NO_NOT_FOUND(4041368),
    GIFT_CARD_TRANSFER_NOT_FOUND(4041369),
    GIFT_CARD_TRANSFER_NOT_ACTIVE(4041370),
    FILE_NOT_FOUND(4041371),
    NEXT_TIER_INVALID(4041372),
    IDENTIFY_TYPE_NOT_FOUND(4041373),
    SYSTEM_ATTRIBUTE_EXISTED(4041374),

    /*
        SCHEME AND RULE 40403xx -> 40404xx
     */
    SCHEME_NOT_FOUND(4040300),
    SCHEME_NOT_ACTIVE(4040301),
    NOT_PASS_ANY_SCHEME(4040302),
    SCHEME_RULE_NOT_FOUND(4040303),
    FORMULA_NOT_FOUND(4040304),
    CARD_NOT_ACTIVE(4040305),
    UNSUPPORTED_MULTIPLE_BASE_CURRENCY_RATES(4040306),
    CARD_TRANSFER_NOT_ACTIVE(4040307),
    CARD_TRANSFER_PROCESSED(4040308),
    FILE_TRANSACTION_NOT_FOUND(4040309),
    PROGRAM_LEVEL_UPGRADE_NOT_FOUND(4040310),
    PARTNER_NOT_FOUND(4040311),
    PROGRAM_TIER_POLICY_NOT_FOUND(4040312),
    PROGRAM_TIER_POLICY_NOT_ACTIVE(4040313),
    PROGRAM_LEVEL_NOT_FOUND(4040314),
    PROGRAM_LEVEL_DUPLICATE_RANK(4040315),

    /*
        Point Service 40405xx - 40406xx
        Point Service 40005xx - 40006xx
     */

    /*
     * Business existed - 40901XX
     * */
    MEMBER_ALREADY_EXISTED(4090101),
    PROGRAM_CODE_EXISTED(4090102),
    PROGRAM_TIER_RANK_NO_EXISTED(4090103),
    PROGRAM_CORPORATION_EXISTED(4090104),
    CURRENCY_EXISTED(4090105),
    CURRENCY_RATE_EXISTED(4090106),
    POOL_EXISTED(4090107),
    USER_PROFILE_EXISTED(4090108),
    POINT_ACCOUNT_EXISTED(4090109),
    MEMBER_ALREADY_EXISTED_IN_PROGRAM(4090110),
    SCHEME_RULE_EXISTED(4090111),
    SCHEME_CODE_EXISTED(4090112),
    CORPORATION_EXISTED(4090113),
    CHAIN_EXISTED(4090114),
    STORE_EXISTED(4090115),
    POS_EXISTED(4090116),
    CARD_BIN_EXISTED(4090117),
    CARD_TYPE_EXISTED(4090118),
    GIFTCARD_BIN_EXISTED(4090119),
    GIFTCARD_TYPE_EXISTED(4090120),
    IDENTIFY_NO_EXISTED(4090121),
    MEMBER_IS_LINKED(4090122),
    USER_PROFILE_IS_LINKED(4090123),
    PROGRAM_REQUIRED_CUSTOMER_REGISTER(4090124),
    USER_PROFILE_IS_NOT_LINKED(4090126),
    MEMBER_PROFILE_LINKED_NOT_MATCHED(4090127),
    INVALID_PROGRAM_POLICY(4090128),
    /*
     * Bad Request - 40001XX
     * */
    CANNOT_ADD_MULTI_CORPORATION_INTO_MULTI_PROGRAM(4000101),
    ID_TYPE_NOT_VALID(4000102),
    REDEEM_AMOUNT_MUST_LESS_GROSS_AMOUNT(4000103),
    CANNOT_CREATE_MULTI_CONDITION_INTO_MULTI_RULE(4000104),
    POOL_NOT_IN_PROGRAM(4000105),
    START_DATE_IS_LESS_END_DATE(4000106),
    CONDITION_NOT_IN_RULE(4000107),
    SCHEME_CANNOT_UPDATE(4000108),
    CARD_IS_TRANSFERRING(4000109),
    REGISTER_MEMBER_FILED_GREATER_MAX_LENGTH(4000110),
    REGISTER_MEMBER_FILED_NOT_NULL(4000111),
    //    MEMBER_LINKED_OTHER_PROFILE(40001112),
    PROGRAM_EMPTY_CONFIGURATION_LINK(40001113),
    MEMBER_PROFILE_LINK_NOT_MATCHED(40001114),
    MEMBER_ATTRIBUTE_ACTIVE_NOT_FOUND(40001115),
    MEMBER_PROFILE_LINK_INVALID(40001116),
    MEMBER_NULL_TIER(40001117),
    MEMBER_ATTRIBUTE_EXISTED(40001118),
    PARTNER_SUPPLIER_NOT_ACTIVE(40001119),
    PARTNER_SUPPLIER_NOT_FOUND(40001141),
    MEMBER_IS_NOT_LINKED(40001143),
    MEMBER_IS_LINKED_OTHER_PROFILE(40001144),
    MEMBER_IS_NOT_LINKED_AND_PROFILE_LINKED_OTHER_MEMBER(40001145),
    MEMBER_IS_LINKED_OTHER_PROFILE_AND_PROFILE_LINKED_OTHER_MEMBER(40001146),
    DUPLICATE_LAST_4_CARD_DIGIT(40001147),
    DUPLICATE_RULE_CODE(40001148),
    BUSINESS_DIFFERENT(40001149),
    CURRENCY_DIFFERENT(40001150),
    /*
     * Card Bad Request - 40001XX
     * */
    CARD_OVER_STOCK_LIMIT(4000112),
    CARD_TRANSFER_ACCEPT_INVALID(4000113),
    CARD_TRANSFER_QUANTITY_INVALID(4000114),
    CPR_OVER_MAX_CARD(4000115),
    CARD_TRANSFER_DENY_INVALID(4000116),
    CARD_PRODUCTION_REQUEST_NOT_NEW(4000117),
    CARD_ASSIGN_MEMBER_FOR_THIS_CARD_TYPE(4000118),
    GIFT_CARD_INVALID(4000119),
    CANNOT_UPDATE(40001120),
    CARD_NOT_FOUND(40001121),
    INVALID_CARD_PRODUCTION_REQUEST_STATUS(40001122),
    START_DATE_OR_END_DATE_NOT_VALID(40001123),
    MEMBER_CARD_NOT_STATUS_BLOCK(40001124),
    CARD_STATUS_IS_LOST(40001125),
    CARD_REPORT_LOST_FAIL(40001126),
    CARD_IS_EXPIRED(40001127),
    CARD_HAS_BEEN_CLOSED(40001128),
    CODE_BLOCK_NOT_MATCHED(40001129),
    CARD_STATUS_UPDATE_NOT_MATCHED(40001130),
    REDEEM_POINT_INVALID(********),
    CURRENCY_INVALID(********),
    MEMBER_CARD_IS_STILL_BEING_USED(********),
    START_DATE_OR_END_DATE_INVALID_FORMAT(********),
    REASON_CODE_NOT_FOUND(********),
    REASON_CODE_NOT_ACTIVE(********),
    FUNCTION_CODE_NOT_FOUND(********),
    FUNCTION_CODE_NOT_ACTIVE(********),
    LIMITED(********),
    PARTNER_CUSTOMER_MULTIPLE_LOYALTY_CODE(********),
    PARTNER_NOT_ACTIVE(********),

    /*
     * Core Point System  - 40405XX,40406XX,40005XX,40006XX
     * */
    ACCOUNT_NOT_FOUNT(4040500),
    ACCOUNT_IS_CLOSED(4040501),
    ACCOUNT_IS_BLOCKED(4040502),
    MEMBER_PROFILE_NOT_FOUND(4040505),
    ACCOUNT_BALANCE_NOT_FOUND(4000506),
    INVOICE_NO_EXISTED(4000500),
    ACCOUNT_EXISTED(4000501),
    ACCOUNT_BALANCE_INSUFFICIENT(4000502),
    TNX_NOT_FOUND(4000503),
    TNX_ALREADY_CONFIRM(4000504),
    TNX_IS_PENDING(4000505),
    CANNOT_CONFIRM_TRANSACTION(4000506),
    NOT_VALID_EXPIRY(4000507),
    TRANSACTION_IS_NOT_VALID(4000508),
    CORPORATION_NOT_IDENTIFICATION(4000509),
    ACCOUNT_BALANCE_EXPIRATION_NOT_FOUND(4000519),
    TXN_IS_NOT_PENDING(4000520),
    ACCOUNT_BALANCE_IS_EXPIRED(4000521),

    //refund
    ROUNDING_RULE_MUST_NOT_BE_NULL(4000510),
    FORMULA_MUST_NOT_BE_NULL(4000511),
    TXN_REFUND_INVALID(4000512),
    TXN_REFUND_AMOUNT_NOT_VALID(4000513),
    ORIGINAL_INVOICE_NO_IS_REFUND(4000514),
    TXN_REFUND_NOT_FOUND(4000515),
    REDEEMABLE_BALANCE_NOT_ENOUGH(4000516),
    COULD_NOT_REVERSE_ORIGINAL_TXN_ADJUSTMENT(4000517),
    POLICY_NEGATIVE_BALANCE_NOT_MATCH_BETWEEN_POOL(4000518),
    TRANSACTION_NOT_FOUND(4000600),
    CARD_POLICY_NOT_MATCHED(4000601),
    OPERATOR_ID_EXISTED(4000602),
    OPERATOR_IS_WAITING_FOR_APPROVE(4000603),
    OPERATOR_IS_NOT_PENDING(4000604),

    SCHEME_SEQUENCE_REQUEST_NOT_FOUND(4000605),
    SCHEME_SEQUENCE_REQUEST_NOT_ACTIVE(4000606),
    SCHEME_SEQUENCE_REQUEST_ALREADY_REQUESTED(4000607),
    SCHEME_SEQUENCE_PAYLOAD_IS_INVALID(4000608),
    SCHEME_SEQUENCE_PENDING_CHANGE_REQUEST_NOT_FOUND(4000610),

    REQUEST_FAILED_NEED_RETRY(4000609),
    NOT_FOUND_SERVICE(4000611),
    REQUEST_NOT_FOUND(4000612),
    RULE_NOT_FOUND(4000613),
    CONDITION_NOT_FOUND(4000614),
    LIMITATION_REQUEST_NOT_FOUND(4000616),
    RULE_CONDITION_NOT_FOUND(4000617),

    COUNTER_NOT_FOUND(4000618),
    COUNTER_NOT_ACTIVE(4000619),
    COUNTER_CODE_IS_BEING_USED(4000620),
    COUNTER_START_DATE_INVALID(4000621),
    COUNTER_END_DATE_INVALID(4000622),
    COUNTER_REQUEST_NOT_FOUND(4000623),
    COUNTER_REQUEST_NOT_ACTIVE(4000624),
    COUNTER_REQUEST_IS_ALREADY_REQUESTED(4000625),
    COUNTER_REQUEST_CAN_NOT_BE_EDITED(4000626),
    RULE_ATTRIBUTE_NOT_FOUND(4000628),
    LIMITATION_START_DATE_INVALID(4000629),
    LIMITATION_END_DATE_INVALID(4000631),

    LIMITATION_CODE_EXISTED(4000627),
    COUNTER_ID_USED_FOR_OTHER_LIMITATION(4000630),
    COUNTER_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED(4000632),
    LIMITATION_REQUEST_IS_ALREADY_REQUESTED(4000633),
    LIMITATION_REQUEST_CAN_NOT_BE_EDITED(4000634),
    LIMITATION_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED(4000635),
    TIER_CODE_IS_BEING_USED(4000636),
    TIER_POLICY_IS_INVALID(4000637),
    TIER_REQUEST_NOT_FOUND(4000638),
    TIER_REQUEST_IS_ALREADY_REQUESTED(4000639),
    TIER_REQUEST_CAN_NOT_BE_EDITED(4000640),
    INVALID_RULE_PAYLOAD(4000641),
    TIER_PRIVILEGE_REQUEST_NOT_FOUND(4000642),
    TIER_POLICY_REQUEST_NOT_FOUND(4000643),
    TIER_POLICY_CODE_EXISTED(4000644),
    TIER_POLICY_REQUEST_IS_ALREADY_REQUESTED(4000645),
    TIER_POLICY_REQUEST_CAN_NOT_BE_EDITED(4000646),
    TIER_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED(4000647),
    PROGRAM_TIER_POLICY_ALREADY_EXIST(4000648),
    OPERATOR_CAN_NOT_BE_EDITED(4000649),
    COUNTER_SERVICE_TYPE_IS_INVALID(4000650),
    COUNTER_REQUEST_SERVICE_TYPE_CAN_NOT_BE_EDITED(4000651),
    COUNTER_LEVEL_IS_NOT_AVAILABLE_FOR_SEVICE_TYPE(4000652),
    CAN_NOT_INACTIVATED_COUNTER_STATUS_LINKED_SERVICE_TYPES(4000653),
    PARTNER_SUPPLIER_IS_INVALID(4000654),
    INVALID_TIER_PRIVILEGE_PAYLOAD(4000655),
    INVALID_RULE_CONDITION_PAYLOAD(4000656),
    GIFT_CARD_PRODUCTION_REQUEST_IS_ALREADY_REQUESTED(4000657),
    GIFT_CARD_PRODUCTION_REQUEST_CAN_NOT_BE_EDITED(4000658),
    GIFT_CARD_PRODUCTION_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED(4000659),
    BATCH_ADJUST_PARTNER_TRANSACTION_NOT_SUPPORTED_YET(4000660),
    PAYLOAD_MUST_NOT_BE_NULL(4000661),
    BATCH_ADJUST_PARTNER_TRANSACTION_FIELD_MUST_NOT_BE_BLANK(4000662),
    BATCH_ADJUST_PARTNER_TRANSACTION_COLUMN_SIZE_IS_INVALID(4000663),
    BATCH_ADJUST_PARTNER_TRANSACTION_ROW_SIZE_IS_INVALID(4000664),
    BATCH_ADJUST_PARTNER_TRANSACTION_NOT_FOUND(4000665),
    BATCH_ADJUST_PARTNER_TRANSACTION_BATCH_FILE_EXTENSION_IS_INVALID(4000666),
    BATCH_ADJUST_PARTNER_TRANSACTION_DATA_IS_EMPTY(4000667),
    BENEFIT_VOUCHER_CODE_NOT_FOUND(4000668),
    BENEFIT_NOT_FOUND(4000669),
    BENEFIT_CODE_IS_BEING_USED(4000670),
    BENEFIT_START_DATE_INVALID(4000671),
    BENEFIT_END_DATE_INVALID(4000672),
    BENEFIT_REQUEST_IS_ALREADY_REQUESTED(4000673),
    BENEFIT_REQUEST_CAN_NOT_BE_EDITED(4000674),
    BENEFIT_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED(4000675),
    INVALID_BENEFIT_REWARD_PAYLOAD(4000676),
    TIER_ADJUSTMENT_REQUEST_NOT_FOUND(4000677),
    MEMBER_ALREADY_EXISTED_TIER_ADJUSTMENT_REQUEST(4000678),
    CARD_CHANGED_STATUS_REQUEST_NOT_FOUND(4000679),
    MEMBER_CARD_STATUS_CAN_NOT_CHANGE(4000680),
    MEMBER_CARD_REQUEST_IS_ALREADY_REQUESTED(4000681),
    SINGLE_GIFT_CARD_REQUEST_IS_ALREADY_REQUESTED(4000682),
    SINGLE_GIFT_CARD_REQUEST_NOT_FOUND(4000683),

    TIER_MATCHING_NOT_FOUND(4000684),
    TIER_MATCHING_NOT_ACTIVE(4000685),
    TIER_MATCHING_BASE_MEMBER_ALREADY_COMPLETED(4000686),
    TIER_MATCHING_MEMBER_HAS_NO_VERIFY(4000687),
    TIER_MATCHING_CONFIG_NOT_FOUND(4000688),
    TIER_MATCHING_CONFIG_NOT_ACTIVE(4000689),
    TIER_MATCHING_MATCH_MEMBER_ALREADY_COMPLETED(4000690),
    LIMITATION_NOT_ENABLE_RESET(4000691),
    LIMITATION_NOT_FOUND(4000692),
    COUNTER_HISTORY_NOT_FOUND(4000693),
    LIMITATION_RESET_COUNTER_INVALID_RESET_VALUE(4000694),
    LIMITATION_RESET_COUNTER_NOT_SUPPORT_PERIOD(4000695),
    LIMITATION_NOT_ACTIVE(4000696),
    TIER_ADJUSTMENT_BATCH_REQUEST_NOT_FOUND(4000697),
    TIER_ADJUSTMENT_BATCH_REQUEST_NOT_ACTIVE(4000698),

    TIER_ADJUSTMENT_REQUEST_NOT_ACTIVE(4000700),

    TIER_ADJUSTMENT_BATCH_REQUEST_NOT_COMPLETED(4000701),

    COUNTER_STATISTIC_REQUIRED_FILTER(4000702),

    TRANSACTION_BATCH_REQUEST_NOT_FOUND(4000703),

    TRANSACTION_BATCH_REQUEST_NOT_ACTIVE(4000704),

    TRANSACTION_REQUEST_NOT_FOUND(4000705),

    TRANSACTION_REQUEST_NOT_ACTIVE(4000706),

    TRANSACTION_HISTORY_NOT_FOUND(4000707),

    TRANSACTION_HISTORY_NOT_ACTIVE(4000708),

    SAP_SALE_ORDER_DATA_NOT_VALID(4000709),

    ALREADY_HAVE_SAP_SALE_ORDER(4000710),

    NOT_HAVE_SAP_SALE_ORDER(4000711),

    SAP_SALE_ORDER_NOT_FOUND(4000712),

    USER_PROFILE_REGISTRATION_POLICY_NOT_FOUND(4000713),

    USER_PROFILE_REGISTRATION_POLICY_NOT_ACTIVE(4000714),

    VALUE_POLICY_INVALID(4000715),
    TIER_EXPIRED(4000716),

    /*
     * OPS - 41XXXXX
     */
    PROGRAM_ATTRIBUTE_SERVICE_TYPE_ALREADY_CONFIGURED(4100001),
    PROGRAM_ATTRIBUTE_SERVICE_TYPE_NOT_CONFIGURED(4100002),
    PROGRAM_ATTRIBUTE_SERVICE_TYPE_ALREADY_REQUESTED(4100003),
    PROGRAM_ATTRIBUTE_SERVICE_TYPE_REQUEST_NOT_FOUND(4100004),
    M_SYSTEM_ATTRIBUTE_CODE_NOT_FOUND(4100005),
    M_PROGRAM_ATTRIBUTE_CODE_NOT_FOUND(4100006),
    M_MEMBER_ATTRIBUTE_CODE_NOT_FOUND(4100007),
    ATTRIBUTE_CODE_BEING_USED(4100008),
    PROGRAM_ATTRIBUTE_SERVICE_TYPE_REQUEST_REJECTED(4100009),
    PROGRAM_ATTRIBUTE_SERVICE_TYPE_PREVIOUS_VERSION_CAN_NOT_BE_EDITED(4100010),
    TRANSACTION_ATTRIBUTE_NOT_FOUND(4100011),
    TIER_MAPPING_CODE_EXISTED(4100012),
    TIER_MAPPING_REQUEST_NOT_FOUND(4100013),
    TIER_MAPPING_IS_ALREADY_REQUESTED(4100014),
    TIER_MAPPING_REJECTED_CAN_NOT_BE_EDITED(4100015),
    TIER_MAPPING_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED(4100016),
    TIER_MAPPING_NOT_FOUND(4100017),
    TIER_MAPPING_CONFIG_REQUEST_NOT_FOUND(4100018),
    TIER_MAPPING_CONFIG_BASE_TIER_ID_IS_INVALID(4100019),
    TIER_MAPPING_CONFIG_MATCHED_TIER_ID_IS_INVALID(4100020),
    TIER_MAPPING_CONFIG_EMPTY_MATCHED_TIER_STATUS_MUST_BE_INACTIVATE(4100021),
    TIER_MAPPING_CONFIG_REQUEST_PROGRAM_TIER_ID_IS_REQUIRED(4100022),
    TIER_MAPPING_CONFIG_REQUEST_PROGRAM_TIER_ID_IS_DUPLICATED(4100023),
    TIER_MAPPING_START_DATE_CANNOT_BE_EDITED(4100024),
    TIER_MAPPING_INVALID_START_DATE(4100025),
    MAKER_CHECKER_CHECK_ID_NOT_FOUND(4100026),
    MAKER_CHECKER_CHECK_ID_IS_DUPLICATED(4100027),
    MAKER_CHECKER_CANNOT_CHANGE_STATUS_REJECT(4100028),
    MAKER_CHECKER_CANNOT_CHANGE_STATUS(4100029),
    PROGRAM_REF_INVALID(4100030),
    PROGRAM_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED(4100031),
    PROGRAM_IS_ALREADY_CONFIGURED(4100032),
    PROGRAM_FUNCTION_PROFILE_ATTRIBUTE_INVALID(4100033),
    MAKER_CHECKER_REQUEST_ID_NOT_FOUND(4100034),
    MAKER_CHECKER_REQUEST_TYPE_NOT_MATCH(4100035),
    PROGRAM_IS_NOT_CONFIGURED(4100036),
    COUNTER_TYPE_INVALID_ATTRIBUTE(4100037),
    REQUEST_CODE_NOT_FOUND(4100038),
    REQUEST_STATUS_MUST_BE_PENDING(4100039),
    RECIPIENT_CODE_EXISTED(4100040),
    RECIPIENT_NOT_FOUND(4100041),
    RECIPIENT_NOT_ACTIVE(4100042),
    MISSING_SELL_ODER_IN_DESCRIPTION(4100043),
    TIER_ADJUSTMENT_APPROVAL_STATUS_NOT_VALID(4100044),
    TIER_ADJUSTMENT_PROCESS_STATUS_NOT_VALID(4100045),
    INVALID_LENGTH_PATTERN(4100046),
    INVALID_INVOICE_NO_PATTERN(4100047),

    TIER_ADJUSTMENT_COMPLETELY_FINISHED(4100048),
    TRANSACTION_REQUEST_APPROVAL_STATUS_NOT_VALID(4100049),
    TRANSACTION_REQUEST_PROCESS_STATUS_NOT_VALID(4100050),
    TRANSACTION_REQUEST_COMPLETELY_FINISHED(4100051),
    TRANSACTION_REQUEST_CAN_NOT_GENERATE_INVOICE_NO(4100052),
    PROGRAM_TRANSACTION_ATTRIBUTE_NOT_VALID(4100053),
    PROGRAM_TRANSACTION_ATTRIBUTE_CODE_EXISTED(4100054),
    ATTRIBUTE_MASTER_DATA_VALUE_NOT_VALID(4100055),
    PROGRAM_ATTRIBUTE_CODE_EXISTED(4100056),
    ATTRIBUTE_MASTER_DATA_VALUE_DUPLICATED(4100057),
    ATTRIBUTE_MASTER_DATA_STATUS_NOT_VALID(4100058),
    TRANSACTION_ATTRIBUTE_NOT_ACTIVE(4100059),
    FIX_TIME_INVALID(4100060),
    CAMPAIGN_NOT_FOUND(4100061),
    CAMPAIGN_NOT_ACTIVE(4100062),
    CAMPAIGN_CODE_EXISTED(4100063),
    CAMPAIGN_START_DATE_INVALID(4100064),
    CAMPAIGN_END_DATE_INVALID(4100065),
    CAMPAIGN_REQUEST_NOT_FOUND(4100066),
    TRANSACTION_CODE_NOT_FOUND(4100067),
    TRANSACTION_CODE_NOT_ACTIVE(4100068),
    TRANSACTION_CODE_EXISTED(4100069),
    SCHEME_EFFECTIVE_DATE_MUST_BE_WITHIN_CAMPAIGN(4100070),

    /*
     * OPS - Attribute Request - 42XXXXX
     */
    ATTRIBUTE_REQUEST_IS_ALREADY_REQUESTED(4200001),
    ATTRIBUTE_REQUEST_CAN_NOT_BE_EDITED(4200002),
    ATTRIBUTE_REQUEST_PREVIOUS_VERSION_CAN_NOT_BE_EDITED(4200003),
    ATTRIBUTE_REQUEST_NOT_FOUND(4200004),
    ATTRIBUTE_REQUEST_INVALID_REGEX(4200005),
    ATTRIBUTE_DATA_TYPE_IS_NOT_AVAILABLE_FOR_THIS_DATA_TYPE_DISPLAY(4200006),
    ATTRIBUTE_OPERATOR_IS_NOT_AVAILABLE_FOR_THIS_DATA_TYPE_DISPLAY(4200007),
    ATTRIBUTE_OPERATOR_CAN_NOT_REMOVE(4200008),
    ATTRIBUTE_REQUEST_EXISTED(4200009),
    INVALID_ATTRIBUTE_VALIDATION_PATTERN(4200010),
    INVALID_ATTRIBUTE_NUMBER_FORMAT(4200011),
    INVALID_ATTRIBUTE_DATETIME_FORMAT(4200012),
    INVALID_ATTRIBUTE_DATE_FORMAT(4200013),
    INVALID_ATTRIBUTE_MASTER_DATA(4200014),

    /*
     * System 00
     */
    SYSTEM_CALL_DB_ERROR(5000002),
    CHECK_SUM_NOT_MATCH(5000003),

    /*
     * Call Scheme Engine
     */
    SYSTEM_CALL_SCHEME_ENGINE_ERROR(5000101),
    SYSTEM_CALL_RULE_ENGINE_ERROR(5000102),


    /*
     * Call Point Service
     */
    SYSTEM_CALL_POINT_SERVICE_ERROR(5000201),
    CANNOT_FIND_FORMULA_IN_SCHEME(5000202),

    /*
     * Call Member Service
     */
    SYSTEM_CALL_MEMBER_SERVICE_ERROR(5000301),

    /*
     * Call Card Service
     */
    SYSTEM_CALL_CARD_SERVICE_ERROR(5000401),

    /*
     * Rule and Permission
     */
    SYSTEM_CALL_RULE_AND_PERMISSION_SERVICE_ERROR(5000501),

    SYSTEM_CALL_EVOUCHER_ERROR(5000502),

    /*
     * Call SAP
     */
    SYSTEM_CALL_SAP_ERROR(5000503);

    private int value;

    ServiceErrorCode(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return this.name();
    }

    public static ServiceErrorCode of(Integer value) {
        if (value != null) {
            for (ServiceErrorCode e : values()) {
                if (value.equals(e.value))
                    return e;
            }
        }
        return null;
    }
}
