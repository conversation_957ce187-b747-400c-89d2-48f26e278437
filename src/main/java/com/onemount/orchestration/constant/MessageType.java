package com.onemount.orchestration.constant;

import com.fasterxml.jackson.annotation.JsonValue;

public enum MessageType {
    CUSTOMER_INFO("CUSTOMER_INFO"),
    CUS_INFO_CREATE("CUSTOMER_INFO_CREATE"),
    CUS_INFO_UPDATE("CUSTOMER_INFO_UPDATE"),
    CUSTOMER_PROFILE("CUSTOMER_PROFILE"),
    TRANS_EARN("TRANS_EARN_POINT_PROC"),
    TRANS_BURN("TRANS_BURN_POINT_PROC"),
    TRANS_EARN_POINT_REQ("TRANS_EARN_POINT_REQ"),
    EVENT_EARN_POINT_REQ("EVENT_EARN_POINT_REQ"),
    TRANSACTION_LISTENER_STATUS("TRANSACTION_LISTENER_STATUS"),
    SYNC_AUDIT_TRAIL_TRANSACTION("SYNC_AUDIT_TRAIL_TRANSACTION");

    private final String value;

    MessageType(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return this.value;
    }
}
