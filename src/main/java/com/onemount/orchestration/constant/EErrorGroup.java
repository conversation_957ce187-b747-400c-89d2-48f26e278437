package com.onemount.orchestration.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.onemount.orchestration.converter.PersistentEnumConverter;

import java.util.HashMap;
import java.util.Map;

public enum EErrorGroup implements PersistentEnum<String> {
    SUCCESS("E001", "Success"),
    DATA_INVALID("E002", "Data not found/invalid"),
    NOT_PASSED_ANY_SCHEME("E003", "Not passed scheme"),
    CUSTOMER_NOT_QUALIFIED("E004", "Customer is not qualified to earn/refund points"),
    DATA_FORMAT_ERROR("E005", "Data format error"),
    INTERNAL_SERVER_ERROR("E006", "Internal Server Error"),
    UNKNOWN("E007", "Undefined");

    private static final Map<String, EErrorGroup> mapByValue;

    static {
        mapByValue = new HashMap<>();
        for (EErrorGroup e : values()) {
            mapByValue.put(e.getValue(), e);
        }
    }

    EErrorGroup(String code, String displayName) {
        this.code = code;
        this.displayName = displayName;
    }

    private final String code;

    private final String displayName;

    @Override
    @JsonValue
    public String getValue() {
        return this.code;
    }

    public static EErrorGroup of(String value) {
        return mapByValue.get(value);
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    public static class Converter extends PersistentEnumConverter<EErrorGroup, String> {
        public Converter() {
            super(EErrorGroup.class);
        }
    }
}
