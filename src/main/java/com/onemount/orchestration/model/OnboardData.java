package com.onemount.orchestration.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.onemount.orchestration.constant.ErrorCode;
import com.onemount.orchestration.dto.ServiceError;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OnboardData implements Serializable {

    private static final long serialVersionUID = 5801302780182860079L;

    private String dsPartitionDate;

    private String loyaltyCusId;

    private String registrationDate;

    @JsonIgnore
    private ErrorCode errorCode;

    @JsonIgnore
    private ServiceError serviceError;
}