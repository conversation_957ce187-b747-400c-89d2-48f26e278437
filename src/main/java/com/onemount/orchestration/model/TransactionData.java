package com.onemount.orchestration.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.oneid.oneloyalty.client.model.RevokeEscrowPointV2Res;
import com.oneid.oneloyalty.client.model.TransactionAdjustmentSchemeV2Res;
import com.oneid.oneloyalty.client.model.TransactionEarnRes;
import com.oneid.oneloyalty.client.model.TransactionReverseV2Res;
import com.onemount.orchestration.constant.EErrorCode;
import com.onemount.orchestration.dto.ServiceError;
import com.onemount.orchestration.support.utils.DateTimeConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter

public class TransactionData {

    private String loyaltyCusId;

    private String transactionId;

    private String txnCode;

    private String txnServiceType;

    private String arrangementId;

    private String cardId;

    private String txnProductFamily;

    private String txnProductLine;

    private String txnInOut;

    private String txnChannelLv1;

    private String txnChannelLv2;

    private String txnChannelLv3;

    private String txnMotherCategory;

    private String txnMcc;

    private String txnServiceLv1;

    private String txnServiceLv2;

    private String txnCategoryGroup;

    private String txnPurpose;

    private String txnMerchantPurpose;

    private String txnSupplier;

    private String txnCcyCode;

    private String txnAdjReason;

    private String txnSlrInd;

    private String txnDate;

    private String txnTime;

    private String txnGrossAmt;

    private String txnNetAmt;

    private String txnTerminalname;

    private String txnBrandname;

    private String txnTid;

    private String txnMid;

    private String txnTermlocation;

    private String txnForeignTxnAmt;

    private String txnForeignExcRate;

    private String txnStatus;

    private String txnAdjSign;

    private String txnOriginalId;

    private String txnAppcode;

    private String txnToken;

    private String txnBin;

    private String txnType;

    private String txnTermowner;

    private String txnSettlementDate;

    private String txnPostDate;

    private String txnQrMerchantInd;

    private String txnForeignCcyCode;

    private String txnBoundary;

    private String txnSubType;

    private String txnChannelLv4;

    private Long sort;

    private String txnRecurringInd;

    private String txnCardCombo;

    private String dsPartitionDate;

    private Integer retryCount;

    private String txnRankCheck;

    @JsonIgnore
    private Date txnDateTime;

    @JsonIgnore
    private Integer serviceErrorCode;

    @JsonIgnore
    private String serviceErrorMessage;

    @JsonIgnore
    private EErrorCode errorCode;

    @JsonIgnore
    private ServiceError serviceError;

    @JsonIgnore
    private TransactionEarnRes earnRes;

    @JsonIgnore
    private TransactionAdjustmentSchemeV2Res refundRes;

    @JsonIgnore
    private TransactionReverseV2Res reverseRes;

    @JsonIgnore
    private RevokeEscrowPointV2Res revokeRes;

    @JsonIgnore
    public String getLoyTxnRef() {
        return this.getEarnRes() != null &&
                this.getEarnRes().getData() != null &&
                this.getEarnRes().getData().getTransaction() != null ?
                this.getEarnRes().getData().getTransaction().getTxnRefNo()
                : null;
    }

    @JsonIgnore
    public Long getTxnDateTimeEpoch() {
        Date dateTime = getTxnDateTime();
        return dateTime.toInstant().getEpochSecond();
    }

    @JsonIgnore
    public Date getTxnDateTime() {
        if (this.txnDateTime == null) {
            String txnDateTimeStr = String.format("%s %s", this.txnDate.trim(), this.txnTime.trim());
            this.txnDateTime = DateTimeConverter.toDate(txnDateTimeStr);
        }
        return this.txnDateTime;
    }
}