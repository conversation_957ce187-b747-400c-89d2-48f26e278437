package com.onemount.orchestration.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.http.HttpStatus;

import java.util.List;

@Data
public class ScaleResponse {
    @JsonProperty("code")
    private int code;

    @JsonProperty("payload")
    private Payload payload;

    private HttpStatus httpStatus;

    @Data
    public static class Payload {
        @JsonProperty("data")
        private List<ScaleInfo> data;
    }
} 
