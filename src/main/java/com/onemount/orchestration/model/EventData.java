package com.onemount.orchestration.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.oneid.oneloyalty.client.model.TransactionEarnRes;
import com.onemount.orchestration.constant.EErrorCode;
import com.onemount.orchestration.dto.ServiceError;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter

public class EventData {
    private static final long serialVersionUID = -2632422612306271313L;

    @JsonIgnore
    private Integer serviceErrorCode;

    @JsonIgnore
    private String serviceErrorMessage;

    private String loyaltyCusId;

    private String eventId;

    private String eventCode;

    private String eventName;

    private String eventGroup;

    private String eventType;

    private String eventProduct;

    private String eventAmount;

    private String eventDate;

    private String serviceCode;

    private EErrorCode errorCode;

    private ServiceError serviceError;

    private TransactionEarnRes earnRes;

    private ServiceError boosterServiceError;

    private TransactionEarnRes boosterEarnRes;

    private String dsPartitionDate;

    private String groupId;

    @JsonIgnore
    public String getLoyTxnRef() {
        if (this.getEarnRes() != null && this.getEarnRes().getData() != null
                && this.getEarnRes().getData().getTransaction() != null) {
            return this.getEarnRes().getData().getTransaction().getTxnRefNo();
        }

        return null;
    }
}