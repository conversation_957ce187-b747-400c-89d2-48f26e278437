package com.onemount.orchestration.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.onemount.orchestration.constant.ErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomerProfileData implements Serializable {

    private static final long serialVersionUID = -7129141839624381598L;

    private Payload payload;

    private Long auditId;

    private String dsPartitionDate;

    @JsonIgnore
    private ErrorCode errorCode;

    @JsonIgnore
    private Integer serviceErrorCode;

    @JsonIgnore
    private String serviceErrorMessage;

    @Getter
    @Setter
    public static class Payload {
        private String loyaltyCusId;

        private CustomerInfo customerInfo;

        private List<ProductInfo> productInfo;

        private List<CardInfo> cardInfo;

        private List<AttributeInfo> cstTargetGroup;
    }

    @Getter
    @Setter
    public static class CustomerInfo {
        private String cstGender;

        private String prdUpdateFlag;

        private String loyaltyObDate;

        private String frstCtcDt;

        private String cstBrthDt;

        private String tierStartDate;

        private String tierEndDate;

        private String cstPreTier;

        private String cstTier;

        private String cstLoyaltyTier;

        private String targetSegmentCode;

        private String cstNationality;

        private String employeeInd;

        private String cstMobileInd;

        private String bizLine;

        private String cstFirstLoginDate;

        private String cstLastLoginDate;

        private String cstDebtGroup;

        private String cstAstClss;

        private String cstStaffInd;

        private String cstBankSegment;

        private String cstType;

        private BigDecimal cstPbt;

        private BigDecimal cstToi;

        private BigDecimal cstCost;

        private String cstFamilyGroup;

        private String cstFamilyMemberType;

        private String cstOccupation;

        private String cstEkycStatus;

        private String cstHomeBranch;

        private String cstSubBranch;

        private String cstRmBranch;

        private String cstMerchantInd;

        private String cstNotiChannel;

        private String cstBlkLv;

        private String cstWtchList;

        private String cstTierProgram;

        private String cstResidence;

        private String cstEkycChannel;

        private String cstPrdHolding;

        private String cstPrdActive;

        private String cstAeupoint;
    }

    @Getter
    @Setter
    public static class ProductInfo {
        private String prdUpdateFlag;

        private String arrangementId;

        private String productCode;

        private String productLv1;

        private String productLv2;

        private String productLv3;

        private String productLv4;

        private String productLv5;

        private String prdBranchCode;

        private String prodOpenDate;

        private String prodExpireDate;

        private String prdBillCycle;

        private String prdStatus;

        private String prdCcyCode;

        private BigDecimal prdTerm;

        private String prdTermUnit;

        private BigDecimal eopBal;

        private String prdRolloverDate;

        private BigDecimal prdHoldingNbr;

        private String productLv6;

        private String prdTenor;

        private String prdCombo;

        private String prdAccArrId;

        private String prdJointHolderInd;

        private String prdAutoBillInd;

        private String prdSalesChannel;

        private String prdPaymentDate;

        private String prdIssueDate;

        private String prdInsuranceRenew;

        private String prdRoll;

        private BigDecimal prdTdAmount;

        private String prdAutoRollInd;
    }

    @Getter
    @Setter
    public static class CardInfo {
        private String prdUpdateFlag;

        private String arrangementId;

        private String cardId;

        private String cardIdentifier;

        private String cardInd;

        private String productCode;

        private String productLv1;

        private String productLv2;

        private String productLv3;

        private String productLv4;

        private String productLv5;

        private String cardExpireDate;

        private String cardStatus;

        private String cardBlockCode;

        private String cardOpenDate;

        private String cardActivatedDate;

        private BigDecimal cardTerm;

        private BigDecimal cardLimit;

        private String cardAnnualFeePayment;

        private String cardBillCycleDate;

        private String cardPaymentDate;

        private String productLv6;

        private String cardPan;

        private String cardType;

        private String cardIssueType;

        private String cardNewAcquisitionInd;
    }

    @Getter
    @Setter
    public static class AttributeInfo {

        private String startDate;

        private String endDate;

        private String status;

        private String value;
    }
}
