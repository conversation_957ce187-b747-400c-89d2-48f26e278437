package com.onemount.orchestration.service;

import com.oneid.oneloyalty.common.entity.ControlFileHistory;
import com.oneid.oneloyalty.common.entity.DataFileHistory;
import com.onemount.orchestration.model.UpdateHistoryRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface HistoryService {

    Page<ControlFileHistory> getList(Pageable page);

    ControlFileHistory detail(String controlFileName);

    void updateStatus(UpdateHistoryRequest updateHistoryRequest);

    Page<DataFileHistory> getListDataFiles(String controlFileName, Pageable page);
}
