package com.onemount.orchestration.service;

import com.oneid.oneloyalty.common.util.JsonUtil;
import com.oneid.oneloyalty.common.util.Log;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class OlConfigParam implements InitializingBean {

    @Value("${app.oneloyalty.scale.base-url}")
    public String olScaleUrl;

    @Override
    public void afterPropertiesSet() throws Exception {
        Log.info("OL_CONFIG_PARAM: " + JsonUtil.writeValueAsString(this));
    }

}
