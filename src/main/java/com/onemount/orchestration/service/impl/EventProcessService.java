package com.onemount.orchestration.service.impl;

import com.oneid.oneloyalty.client.model.AttributeUpdateReq;
import com.oneid.oneloyalty.client.model.MemberAttributeUpdateReq;
import com.oneid.oneloyalty.client.model.OLServiceMemberAttributeUpdateRes;
import com.oneid.oneloyalty.client.model.TransactionEarnRes;
import com.oneid.oneloyalty.client.model.TransactionEarnServiceReq;
import com.oneid.oneloyalty.client.model.TransactionReq;
import com.oneid.oneloyalty.client.model.attributes.TransactionAttributeReq;
import com.oneid.oneloyalty.client.service.ServiceClient;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.entity.KafkaAuditTrailEarnEvent;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import com.oneid.oneloyalty.common.repository.KafkaAuditTrailEarnEventRepository;
import com.onemount.orchestration.constant.EAttributeType;
import com.onemount.orchestration.constant.EErrorCode;
import com.onemount.orchestration.constant.EEventErrorCode;
import com.onemount.orchestration.constant.EFileType;
import com.onemount.orchestration.constant.EProcessServiceId;
import com.onemount.orchestration.constant.MemberAttributeStatus;
import com.onemount.orchestration.constant.MessageType;
import com.onemount.orchestration.constant.ServiceErrorCode;
import com.onemount.orchestration.constant.TxnStatus;
import com.onemount.orchestration.dto.ServiceError;
import com.onemount.orchestration.entity.Ps2EarningErrorGroup;
import com.onemount.orchestration.kafka.event.produce.GamiEarnEventProduce;
import com.onemount.orchestration.kafka.service.ProduceService;
import com.onemount.orchestration.model.EventData;
import com.onemount.orchestration.model.ProcessData;
import com.onemount.orchestration.service.ContentProcessService;
import com.onemount.orchestration.service.DeserializeService;
import com.onemount.orchestration.service.ErrorGroupService;
import com.onemount.orchestration.support.DateConverter;
import com.onemount.orchestration.support.retry.RetryHelper;
import com.onemount.orchestration.support.utils.AttributeConverter;
import com.onemount.orchestration.support.utils.BigDecimalConverter;
import com.onemount.orchestration.support.utils.DateTimeConverter;
import com.onemount.orchestration.support.utils.LengthValidationUtil;
import com.onemount.orchestration.support.utils.Log;
import com.onemount.orchestration.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class EventProcessService implements ContentProcessService<EventData> {
    private static final int LENGTH_255 = 255;
    private static final String[] FILE_OUTPUT_HEADER = {"loyalty_id", "txn_ref_no", "event_id", "ds_partition_date", "error_group", "error_message", "error_message_detail", "retry"};

    private final ServiceClient serviceClient;
    private final DeserializeService deserializeService;
    private final KafkaAuditTrailEarnEventRepository kafkaAuditTrailEarnEventRepository;
    private final ProduceService produceService;
    private final RetryTemplate retryTemplate;
    private final ErrorGroupService errorGroupService;

    @Value("${app.business-code}")
    private String businessCode;
    @Value("${app.program-code}")
    private String programCode;
    @Value("${app.corporation-code}")
    private String corporationCode;
    @Value("${app.store-code.earn-event}")
    private String storeCode;
    @Value("${app.pos-code.earn-event}")
    private String posCode;
    @Value("${app.currency-code}")
    private String currencyCode;
    @Value("${app.service-code.earn-event}")
    private String serviceCodeEarnEvent;
    @Value("${app.channel-code}")
    private String channelCode;
    @Value("${app.casa-event-code}")
    private String casaEventCode;
    @Value("${app.eop-event-code}")
    private String eopEventCode;
    @Value("${app.end-of-month-dates}")
    private List<String> endOfMonthDates;
    @Value("${app.casa-booster-invoice-no-format}")
    private String casaBoosterInvoiceNoFormat;
    @Value("${app.enable-event-gami:false}")
    private Boolean enableEventGami;
    @Value("${kafka.topic.earn-event-gami-produce}")
    private String gamiEarnEventTopic;

    @Override
    public EProcessServiceId getProcessId() {
        return EProcessServiceId.EVENT;
    }

    @Override
    public String getOrderKey(EventData data) {
        if (Objects.nonNull(data)) {
            return data.getLoyaltyCusId();
        }
        return null;
    }

    @Override
    public EventData deserialize(String data) {
        return deserializeService.deserialize(data, EventData.class);
    }

    @Override
    public boolean isProcessed(ProcessData<EventData> processData) {
        EventData data = processData.getDataObj();
        Date dsPartitionDate =
                StringUtils.isNotBlank(data.getDsPartitionDate()) ?
                        DateConverter.toDateFormatterYYYYMMDD(data.getDsPartitionDate()) : null;
        List<KafkaAuditTrailEarnEvent> eventList =
                kafkaAuditTrailEarnEventRepository.findByLoyaltyCusIdAndEventIdAndFileNameAndDsPartitionDate(
                        data.getLoyaltyCusId(),
                        data.getEventId(),
                        processData.getFileName(),
                        dsPartitionDate
                );
        if (!eventList.isEmpty()) {
            data.setErrorCode(EErrorCode.of(eventList.get(0).getErrorCode()));
        }
        return !eventList.isEmpty();
    }

    @Override
    public String[] getHeader() {
        return FILE_OUTPUT_HEADER;
    }

    @Override
    public String[] generateOutput(ProcessData<EventData> processData) {
        EventData data = processData.getDataObj();

        Ps2EarningErrorGroup errorGroup = errorGroupService.mapping(
                EFileType.EVENT,
                data.getServiceErrorCode() != null ? String.valueOf(data.getServiceErrorCode()) : null,
                data.getErrorCode() != null ? data.getErrorCode().getCode() : null
        );

        return new String[]{
                data.getLoyaltyCusId(),
                data.getLoyTxnRef(),
                data.getEventId(),
                data.getDsPartitionDate(),
                errorGroup.getCode(),
                errorGroup.getMessage(),
                errorGroup.getMessageDetail(),
                null
        };
    }

    @Override
    public void process(ProcessData<EventData> processData) {
        String requestId = UUID.randomUUID().toString();
        EventData event = processData.getDataObj();
        EErrorCode errorCode = verifyEvent(event);
        event.setErrorCode(errorCode);

        // Handle Earn Transactions
        if (Objects.isNull(event.getErrorCode())) {
            if (isCasaEvent(event) || isEopEvent(event)) {
                ServiceError serviceError = ServiceError.builder().code(ServiceError.CODE_SUCCESS).build();

                // Handle case event
                if (isCasaEvent(event)) {
                    serviceError = updateMemberAttributes(event, requestId);
                }

                if (serviceError.isSuccess()) {

                    // Daily Casa Event
                    processEarnPoints(event, event.getEventId(), EBoolean.NO, requestId);

                    // End of Month Casa Event
                    EBoolean isEndOfMonth = isEndOfMonth(event.getEventDate());
                    if (EBoolean.YES.equals(isEndOfMonth) & !isEopEvent(event)) {
                        String boosterInvoiceNo = getCasaBoosterInvoiceNo(event.getEventId());
                        processEarnPoints(event, boosterInvoiceNo, isEndOfMonth, requestId);
                    }
                } else {
                    event.setServiceError(serviceError);
                }
            } else {
                // Daily Event
                processEarnPoints(event, event.getEventId(), null, requestId);
            }

            productEventForGami(event, requestId);
        } else {
            Log.warn(LogData.createLogData()
                    .append("msg", "EarnEventHandlerImpl -- Event is invalid format")
                    .append("event", event)
                    .append("request_id", requestId)
                    .append("error", event.getErrorCode().getMessage())
            );
        }

        ServiceError serviceError = event.getServiceError();

        if (Objects.nonNull(serviceError)) {
            event.setServiceErrorCode(Integer.valueOf(serviceError.getCode()));
            event.setServiceErrorMessage(serviceError.getMessage());

            if (Objects.isNull(event.getErrorCode())) {
                event.setErrorCode(EErrorCode.convert(Integer.valueOf(serviceError.getCode())));
            }
        }

        insertAuditTrail(processData, event, requestId);
    }

    private EErrorCode verifyEvent(EventData eventData) {
        if (StringUtils.isEmpty(eventData.getLoyaltyCusId())) {
            return EErrorCode.LOYALTY_CUS_ID_CANNOT_EMPTY;
        }

        if (!LengthValidationUtil.isValidString(eventData.getLoyaltyCusId(), LENGTH_255)) {
            return EErrorCode.LOYALTY_CUS_ID_LENGTH_INVALID;
        }

        if (StringUtils.isEmpty(eventData.getEventId())) {
            return EErrorCode.EVENT_ID_CANNOT_EMPTY;
        }

        if (!LengthValidationUtil.isValidString(eventData.getEventId(), LENGTH_255)) {
            return EErrorCode.EVENT_ID_LENGTH_INVALID;
        }

        if (StringUtils.isEmpty(eventData.getEventCode())) {
            return EErrorCode.EVENT_CODE_CANNOT_EMPTY;
        }

        if (!LengthValidationUtil.isValidString(eventData.getEventCode(), LENGTH_255)) {
            return EErrorCode.EVENT_CODE_LENGTH_INVALID;
        }

        if (StringUtils.isEmpty(eventData.getEventAmount())) {
            return EErrorCode.EVENT_AMOUNT_CANNOT_EMPTY;
        }

        if (!BigDecimalConverter.isValid(eventData.getEventAmount())) {
            return EErrorCode.EVENT_AMOUNT_INVALID_NUMBER_FORMAT;
        }

        if (StringUtils.isEmpty(eventData.getEventDate())) {
            return EErrorCode.EVENT_DATE_CANNOT_EMPTY;
        }

        if (!DateTimeConverter.isValidFormat(eventData.getEventDate(), "dd-MMM-yyyy HH:mm:ss")) {
            return EErrorCode.EVENT_DATE_INVALID_FORMAT;
        }

        if (StringUtils.isEmpty(eventData.getEventName())) {
            return EErrorCode.EVENT_NAME_CANNOT_EMPTY;
        }

        if (!LengthValidationUtil.isValidString(eventData.getEventName(), LENGTH_255)) {
            return EErrorCode.EVENT_NAME_LENGTH_INVALID;
        }

        if (StringUtils.isEmpty(eventData.getEventType())) {
            return EErrorCode.EVENT_TYPE_CANNOT_EMPTY;
        }

        if (!LengthValidationUtil.isValidString(eventData.getEventType(), LENGTH_255)) {
            return EErrorCode.EVENT_TYPE_LENGTH_INVALID;
        }

        if (StringUtils.isEmpty(eventData.getEventGroup())) {
            return EErrorCode.EVENT_GROUP_CANNOT_EMPTY;
        }


        if (!LengthValidationUtil.isValidString(eventData.getEventGroup(), LENGTH_255)) {
            return EErrorCode.EVENT_GROUP_LENGTH_INVALID;
        }

        if (StringUtils.isEmpty(eventData.getEventProduct())) {
            return EErrorCode.EVENT_PRODUCT_CANNOT_EMPTY;
        }

        if (!LengthValidationUtil.isValidString(eventData.getEventProduct(), LENGTH_255)) {
            return EErrorCode.EVENT_PRODUCT_LENGTH_INVALID;
        }


        return null;
    }

    private TransactionEarnServiceReq toEarnReq(String loyaltyCusId,
                                                EventData event,
                                                String invoiceNo,
                                                EBoolean isEndOfMonth,
                                                String groupId) {

        BigDecimal grossAmount = new BigDecimal(event.getEventAmount());
        Long transactionTime =
                !StringUtils.isEmpty(event.getEventDate()) ? DateTimeConverter.toEpochSecond(event.getEventDate())
                        : Instant.now().getEpochSecond();

        CustomerIdentify customerIdentify = toCustomerIdentify(loyaltyCusId);
        List<TransactionAttributeReq> attributes = toAttributes(event, isEndOfMonth, groupId)
                .stream().filter(att -> !StringUtils.isEmpty(att.getValue()))
                .collect(Collectors.toList());

        return TransactionEarnServiceReq.builder()
                .customerIdentifier(customerIdentify)
                .businessCode(businessCode)
                .programCode(programCode)
                .currencyCode(currencyCode)
                .channelCode(channelCode)
                .serviceCode(StringUtils.isNotBlank(event.getServiceCode()) ? event.getServiceCode() : serviceCodeEarnEvent)
                .transaction(
                        TransactionReq.builder()
                                .invoiceNo(invoiceNo)
                                .transactionCode(null)
                                .gmv(grossAmount)
                                .grossAmount(grossAmount)
                                .transactionTime(transactionTime)
                                .description(event.getEventName())
                                .corporationCode(corporationCode)
                                .storeId(storeCode)
                                .posCode(posCode)
                                .attributes(attributes)
                                .build()
                )
                .build();
    }

    private List<TransactionAttributeReq> toAttributes(EventData event, EBoolean isEndOfMonth, String groupId) {

        List<TransactionAttributeReq> attributes = new ArrayList<>();

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "EVENT_CODE", event.getEventCode()));

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "EVENT_NAME", event.getEventName()));

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "EVENT_GROUP", event.getEventGroup()));

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "EVENT_PRODUCT", event.getEventProduct()));

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.NUMBER, "EVENT_AMOUNT", event.getEventAmount()));

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "EVENT_TYPE", event.getEventType()));

        if (Objects.nonNull(isEndOfMonth)) {
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "IS_END_OF_MONTH", isEndOfMonth.getValue()));
        }

        if (event.getEventGroup().equals("16") && StringUtils.isNotEmpty(groupId)) {
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "GROUP_ID", groupId));
        }

        return attributes;
    }

    private CustomerIdentify toCustomerIdentify(String loyaltyCusId) {
        CustomerIdentify identify = new CustomerIdentify();
        identify.setId(loyaltyCusId);
        identify.setIdType(EIdType.PARTNER_CUSTOMER_ID);
        return identify;
    }

    private MemberAttributeUpdateReq toUpdateAttributesReq(EventData event) {
        List<AttributeUpdateReq> attributes = new ArrayList<>();
        AttributeUpdateReq attribute = new AttributeUpdateReq();
        attribute.setStatus(MemberAttributeStatus.ACTIVE.getValue());
        attribute.setCode("CASA_AVERAGE_BALANCE_90D");
        attribute.setValue(event.getEventAmount());
        attributes.add(attribute);

        MemberAttributeUpdateReq req = new MemberAttributeUpdateReq();
        req.setBusinessCode(businessCode);
        req.setProgramCode(programCode);
        req.setCustomerIdentifier(toCustomerIdentify(event.getLoyaltyCusId()));
        req.setAttributes(attributes);
        return req;
    }

    private void processEarnPoints(EventData event,
                                   String invoiceNo,
                                   EBoolean isEndOfMonth,
                                   String requestId
    ) {
        Integer serviceErrorCode = null;
        String serviceErrorMessage = null;
        TransactionEarnRes earnRes = new TransactionEarnRes();

        try {
            TransactionEarnServiceReq earnReq = toEarnReq(event.getLoyaltyCusId(), event, invoiceNo, isEndOfMonth, event.getGroupId());
            earnRes = callApiEarnV2(requestId, earnReq);
            serviceErrorCode = earnRes.getMeta().getCode();
            serviceErrorMessage = earnRes.getMeta().getMessage();
            Log.info(LogData.createLogData()
                    .append("msg", "EarnEventHandlerImpl -- Process earn point was successful.")
                    .append("event_id", event.getEventId())
                    .append("request_id", requestId)
                    .append("loyalty_cus_id", event.getLoyaltyCusId())
            );
        } catch (BusinessException e) {
            serviceErrorCode = e.getErrCode();
            serviceErrorMessage = e.getMessage();
            Log.warn(LogData.createLogData()
                    .append("msg", "EarnEventHandlerImpl -- Process earn point was unsuccessful.")
                    .append("err", e.getMessage())
                    .append("event_id", event.getEventId())
                    .append("request_id", requestId)
                    .append("loyalty_cus_id", event.getLoyaltyCusId())
            );
        } catch (Exception e) {
            serviceErrorCode = ServiceErrorCode.SERVER_ERROR.getValue();
            serviceErrorMessage = e.getMessage();
            Log.error(LogData.createLogData()
                    .append("msg", "EarnEventHandlerImpl -- Exception while processing earn point.")
                    .append("err", e.getMessage())
                    .append("event_id", event.getEventId())
                    .append("request_id", requestId)
                    .append("loyalty_cus_id", event.getLoyaltyCusId())
            );
        }

        ServiceError serviceError = ServiceError.builder()
                .code(String.valueOf(serviceErrorCode))
                .message(serviceErrorMessage)
                .build();

        if ((isCasaEvent(event) || isEopEvent(event)) && EBoolean.YES.equals(isEndOfMonth)) {
            event.setBoosterServiceError(serviceError);
            event.setBoosterEarnRes(earnRes);
        } else {
            event.setServiceError(serviceError);
            event.setEarnRes(earnRes);
        }
    }

    private TransactionEarnRes callApiEarnV2(String requestId, TransactionEarnServiceReq earnReq) {
        return RetryHelper.executeWithRetry(
                retryTemplate,
                () -> serviceClient.earnV2(earnReq, requestId),
                requestId,
                "CallApiEarnV2"
        );
    }

    private OLServiceMemberAttributeUpdateRes callApiUpdateAttribute(String requestId, MemberAttributeUpdateReq updateAttributesReq) {
        return RetryHelper.executeWithRetry(
                retryTemplate,
                () -> serviceClient.updateAttributesV1(updateAttributesReq, requestId),
                requestId,
                "CallApiUpdateAttributes"
        );
    }

    private ServiceError updateMemberAttributes(EventData event, String requestId) {
        Integer serviceErrorCode = null;
        String serviceErrorMessage = null;
        OLServiceMemberAttributeUpdateRes updateRes = new OLServiceMemberAttributeUpdateRes();

        try {
            MemberAttributeUpdateReq updateAttributesReq = toUpdateAttributesReq(event);
            updateRes = callApiUpdateAttribute(requestId, updateAttributesReq);
            serviceErrorCode = updateRes.getMeta().getCode();
            serviceErrorMessage = updateRes.getMeta().getMessage();

            Log.info(LogData.createLogData()
                    .append("msg", "EarnEventHandlerImpl -- Process update member attributes was successful.")
                    .append("event_id", event.getEventId())
                    .append("request_id", requestId)
                    .append("loyalty_cus_id", event.getLoyaltyCusId())
            );
        } catch (BusinessException e) {
            serviceErrorCode = e.getErrCode();
            serviceErrorMessage = e.getMessage();
            Log.warn(LogData.createLogData()
                    .append("msg", "EarnEventHandlerImpl -- Process update member attributes was unsuccessful.")
                    .append("event_id", event.getEventId())
                    .append("request_id", requestId)
                    .append("loyalty_cus_id", event.getLoyaltyCusId())
            );
        } catch (Exception e) {
            serviceErrorCode = ServiceErrorCode.SERVER_ERROR.getValue();
            serviceErrorMessage = e.getMessage();
            Log.error(LogData.createLogData()
                    .append("msg", "EarnEventHandlerImpl -- Exception while processing update member attributes.")
                    .append("event_id", event.getEventId())
                    .append("request_id", requestId)
                    .append("loyalty_cus_id", event.getLoyaltyCusId())
                    .append("error", e.getMessage())
            );
        }

        return ServiceError.builder()
                .code(String.valueOf(serviceErrorCode))
                .message(serviceErrorMessage)
                .build();
    }

    private void insertAuditTrail(ProcessData<?> processData,
                                  EventData eventData,
                                  String requestId
    ) {
        try {

            List<KafkaAuditTrailEarnEvent> auditTrails = new ArrayList<>();

            // Event Daily
            KafkaAuditTrailEarnEvent auditTrail = buildAuditTrail(eventData,
                    eventData.getEventId(),
                    eventData.getEarnRes(),
                    eventData.getServiceError(),
                    requestId,
                    processData.getFileName()
            );
            auditTrails.add(auditTrail);

            // Event Booster
            if (Objects.nonNull(eventData.getBoosterServiceError())) {
                String boosterInvoiceNo = getCasaBoosterInvoiceNo(eventData.getEventId());
                KafkaAuditTrailEarnEvent boosterAuditTrail = buildAuditTrail(eventData,
                        boosterInvoiceNo,
                        eventData.getBoosterEarnRes(),
                        eventData.getBoosterServiceError(),
                        requestId,
                        processData.getFileName()
                );
                auditTrails.add(boosterAuditTrail);
            }

            // Save all events
            kafkaAuditTrailEarnEventRepository.saveAll(auditTrails);
            Log.info(LogData.createLogData()
                    .append("msg", "EarnEventHandlerImpl -- Insert audit trail was successful.")
                    .append("loyalty_cus_id", eventData.getLoyaltyCusId())
                    .append("event_id", eventData.getEventId())
                    .append("request_id", requestId)
            );
        } catch (Exception ex) {
            Log.error(LogData.createLogData()
                    .append("msg", "EarnEventHandlerImpl -- Exception while inserting audit trail.")
                    .append("loyalty_cus_id", eventData.getLoyaltyCusId())
                    .append("event_id", eventData.getEventId())
                    .append("request_id", requestId)
                    .append("error", ex.getMessage())
            );
        }
    }

    private KafkaAuditTrailEarnEvent buildAuditTrail(
            EventData event,
            String invoiceNo,
            TransactionEarnRes earnRes,
            ServiceError serviceError,
            String requestId,
            String fileName
    ) {
        KafkaAuditTrailEarnEvent auditTrail = new KafkaAuditTrailEarnEvent();
        auditTrail.setCreatedAt(new Date());
        auditTrail.setMessageId(requestId);
        auditTrail.setMessageType(MessageType.TRANS_EARN_POINT_REQ.getValue());
        auditTrail.setBusinessCode(businessCode);
        auditTrail.setProgramCode(programCode);
        auditTrail.setCurrencyCode(currencyCode);
        auditTrail.setCorporationCode(corporationCode);
        auditTrail.setStoreCode(storeCode);
        auditTrail.setPosCode(posCode);
        auditTrail.setDsPartitionDate(StringUtils.isNotBlank(event.getDsPartitionDate()) ? DateConverter.toDateFormatterYYYYMMDD(event.getDsPartitionDate()) : null);
        auditTrail.setFileName(fileName);

        auditTrail.setLoyaltyCusId(event.getLoyaltyCusId());
        auditTrail.setEventId(invoiceNo);
        auditTrail.setEventCode(event.getEventCode());
        auditTrail.setEventName(event.getEventName());
        auditTrail.setEventGroup(event.getEventGroup());
        auditTrail.setEventProduct(event.getEventProduct());
        auditTrail.setEventAmount(event.getEventAmount());
        auditTrail.setEventDate(event.getEventDate());
        auditTrail.setEventType(event.getEventType());
        auditTrail.setServiceCode(StringUtils.isNotBlank(event.getServiceCode()) ? event.getServiceCode() : serviceCodeEarnEvent);
        auditTrail.setGroupId(StringUtils.isEmpty(event.getGroupId()) ? null : event.getGroupId());

        EErrorCode errorCode = null;
        EEventErrorCode tcbErrorCode = null;

        if (Objects.nonNull(earnRes) && Objects.nonNull(earnRes.getData()) && Objects.nonNull(earnRes.getData().getTransaction())) {
            auditTrail.setTxnRefNo(earnRes.getData().getTransaction().getTxnRefNo());
            auditTrail.setInvoiceNo(earnRes.getData().getTransaction().getInvoiceNo());
            auditTrail.setAwardPoint(earnRes.getData().getTransaction().getAwardedPoint());
            auditTrail.setTransactionTime(DateTimeConverter.toString(earnRes.getData().getTransaction().getTransactionTime()));
            auditTrail.setTransactionStatus(TxnStatus.SUCCESS);
        } else {
            auditTrail.setTransactionStatus(TxnStatus.FAILED);
        }

        if (Objects.nonNull(serviceError)) {
            auditTrail.setServiceErrorCode(serviceError.getCode());
            auditTrail.setServiceErrorMessage(serviceError.getMessage());
            errorCode = EErrorCode.convert(Integer.valueOf(serviceError.getCode()));
        }

        if (Objects.isNull(errorCode)) {
            if (Objects.nonNull(event.getErrorCode())) {
                errorCode = event.getErrorCode();
            }
        }

        if (Objects.nonNull(errorCode)) {
            auditTrail.setErrorCode(errorCode.getCode());
            auditTrail.setErrorMessage(errorCode.getMessage());
            tcbErrorCode = EEventErrorCode.convert(errorCode);
        }

        if (Objects.nonNull(tcbErrorCode)) {
            auditTrail.setTcbErrorCode(tcbErrorCode.getCode());
            auditTrail.setTcbErrorMessage(tcbErrorCode.getMessage());
        }

        return auditTrail;
    }

    private EBoolean isEndOfMonth(String eventDate) {
        String ddmmyyyy = DateTimeConverter.toDDMMYYYY(eventDate);

        if (Objects.isNull(ddmmyyyy)) {
            return EBoolean.NO;
        }

        return endOfMonthDates.contains(ddmmyyyy) ? EBoolean.YES : EBoolean.NO;
    }

    private boolean isCasaEvent(EventData event) {
        return casaEventCode.equals(event.getEventCode());
    }

    private boolean isEopEvent(EventData event) {
        return eopEventCode.equals(event.getEventCode());
    }

    private String getCasaBoosterInvoiceNo(String invoiceNo) {
        return String.format(casaBoosterInvoiceNoFormat, invoiceNo);
    }

    private void productEventForGami(EventData event, String requestId) {
        if (enableEventGami) {
            GamiEarnEventProduce earnEventGami = new GamiEarnEventProduce();
            earnEventGami.setId(requestId);
            earnEventGami.setUserId(event.getLoyaltyCusId());
            earnEventGami.setLoyaltyCusId(event.getLoyaltyCusId());
            earnEventGami.getPayloadGami().setPayloadEarnEventGami(event);
            CustomerIdentify customerIdentify = toCustomerIdentify(event.getLoyaltyCusId());
            earnEventGami.getPayloadGami().setCustomerIdentifier(customerIdentify);

            ProduceService.ProduceMessage produceMessage = new ProduceService.ProduceMessage();
            produceMessage.setKey(event.getLoyaltyCusId());
            produceMessage.setValue(earnEventGami);
            produceService.produceAsync(produceMessage, gamiEarnEventTopic, "");
        }
    }
}
