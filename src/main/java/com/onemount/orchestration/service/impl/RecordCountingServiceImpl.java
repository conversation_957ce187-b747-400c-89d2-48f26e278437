package com.onemount.orchestration.service.impl;

import com.onemount.orchestration.constant.EErrorCode;
import com.onemount.orchestration.constant.ErrorCode;
import com.onemount.orchestration.model.CustomerProfileData;
import com.onemount.orchestration.model.EventData;
import com.onemount.orchestration.model.OnboardData;
import com.onemount.orchestration.model.TransactionData;
import com.onemount.orchestration.service.RecordCountingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Implementation of the RecordCountingService interface.
 * Provides thread-safe methods for counting total and failed records.
 */
@Service
@Slf4j
public class RecordCountingServiceImpl implements RecordCountingService {

    private static final String SUCCESS_CODE = "00";

    @Override
    public long countFailedRecords(List<?> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return 0L;
        }
        
        AtomicLong failedCount = new AtomicLong(0);
        
        dataList.stream()
                .filter(Objects::nonNull)
                .forEach(item -> {
                    if (isRecordFailed(item)) {
                        failedCount.incrementAndGet();
                    }
                });
                
        return failedCount.get();
    }

    @Override
    public boolean isRecordFailed(Object item) {
        if (item == null) {
            return true;
        }
        
        if (item instanceof OnboardData) {
            return isErrorCodeFailed(((OnboardData) item).getErrorCode());
        } else if (item instanceof TransactionData) {
            return isErrorCodeFailed(((TransactionData) item).getErrorCode());
        } else if (item instanceof EventData) {
            return isErrorCodeFailed(((EventData) item).getErrorCode());
        } else if (item instanceof CustomerProfileData) {
            return isErrorCodeFailed(((CustomerProfileData) item).getErrorCode());
        }
        
        // For unknown data types, assume not failed
        log.warn("Unknown data type encountered during record failure check: {}", 
                item.getClass().getName());
        return false;
    }

    /**
     * Checks if an ErrorCode represents a failed state.
     *
     * @param errorCode the ErrorCode to check
     * @return true if failed, false if successful
     */
    private boolean isErrorCodeFailed(ErrorCode errorCode) {
        return (errorCode == null || !errorCode.getCode().equals(SUCCESS_CODE));
    }

    /**
     * Checks if an EErrorCode represents a failed state.
     *
     * @param errorCode the EErrorCode to check
     * @return true if failed, false if successful
     */
    private boolean isErrorCodeFailed(EErrorCode errorCode) {
        return (errorCode == null || !errorCode.equals(EErrorCode.SUCCESS));
    }
}
