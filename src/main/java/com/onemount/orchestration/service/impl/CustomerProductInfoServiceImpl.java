package com.onemount.orchestration.service.impl;

import com.oneid.oneloyalty.common.entity.CustomerProductInfo;
import com.oneid.oneloyalty.common.entity.CustomerProductInfoVersioning;
import com.oneid.oneloyalty.common.mapper.CustomerProductInfoMapper;
import com.oneid.oneloyalty.common.repository.CustomerProductInfoRepository;
import com.oneid.oneloyalty.common.repository.CustomerProductInfoVersioningRepository;
import com.onemount.orchestration.service.CustomerProductInfoService;
import com.onemount.orchestration.support.DateConverter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class CustomerProductInfoServiceImpl implements CustomerProductInfoService {

    private final CustomerProductInfoRepository customerProductInfoRepository;
    private final CustomerProductInfoVersioningRepository customerProductInfoVersioningRepository;
    private final CustomerProductInfoMapper customerProductInfoMapper;

    @Value("${app.enable-earning-versioning:false}")
    private Boolean enableEarningByVersioning;

    @Override
    public CustomerProductInfo getCustomerProductInfo(String loyCusId, String arrangementId, Date txnTime) {

        if (txnTime != null && enableEarningByVersioning) {
            Integer txnDate = DateConverter.toYearMonthDay(txnTime);

            CustomerProductInfoVersioning productInfoVersioning = getLessThan(loyCusId, arrangementId, txnDate);
            if (productInfoVersioning == null) {
                productInfoVersioning = getGreaterThan(loyCusId, arrangementId, txnDate);
            }

            if (productInfoVersioning != null) {
                return customerProductInfoMapper.toCustomerProductInfo(productInfoVersioning);
            }
        }

        return getCustomerProductInfo(loyCusId, arrangementId);
    }

    private CustomerProductInfo getCustomerProductInfo(String loyCusId, String arrangementId) {
        List<CustomerProductInfo> customerProductInfos = customerProductInfoRepository.find(loyCusId, arrangementId);
        if (CollectionUtils.isNotEmpty(customerProductInfos)) {
            return customerProductInfos.get(0);
        }
        return null;
    }

    private CustomerProductInfoVersioning getLessThan(String loyCusId, String arrangementId, Integer txnDate) {
        PageRequest pageRequest =
                PageRequest.of(0, 1, Sort.by("versionDate").descending());

        List<CustomerProductInfoVersioning> product =
                customerProductInfoVersioningRepository.findByVersionDateLTE(loyCusId, arrangementId, txnDate, pageRequest);
        return product.isEmpty() ? null : product.get(0);
    }

    private CustomerProductInfoVersioning getGreaterThan(String loyCusId, String arrangementId, Integer txnDate) {
        PageRequest pageRequest = PageRequest.of(0, 1, Sort.by("versionDate"));

        List<CustomerProductInfoVersioning> product =
                customerProductInfoVersioningRepository.findByVersionDateGTE(loyCusId, arrangementId, txnDate, pageRequest);
        return product.isEmpty() ? null : product.get(0);
    }
}
