package com.onemount.orchestration.service.impl;

import com.oneid.oneloyalty.client.model.FullMemberInquiryReq;
import com.oneid.oneloyalty.client.model.FullMemberInquiryRes;
import com.oneid.oneloyalty.client.model.MemberRegisterClientRes;
import com.oneid.oneloyalty.client.model.MemberServiceRegisterReq;
import com.oneid.oneloyalty.client.model.data.MemberProfile;
import com.oneid.oneloyalty.client.model.data.MemberRegister;
import com.oneid.oneloyalty.client.service.ServiceClient;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.entity.KafkaAuditTrailCusInfo;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import com.oneid.oneloyalty.common.repository.KafkaAuditTrailCusInfoRepository;
import com.onemount.orchestration.constant.Constant;
import com.onemount.orchestration.constant.EFileType;
import com.onemount.orchestration.constant.EProcessServiceId;
import com.onemount.orchestration.constant.ErrorCode;
import com.onemount.orchestration.constant.MessageType;
import com.onemount.orchestration.constant.ServiceErrorCode;
import com.onemount.orchestration.dto.ServiceError;
import com.onemount.orchestration.entity.Ps2EarningErrorGroup;
import com.onemount.orchestration.kafka.event.produce.CusInfo;
import com.onemount.orchestration.kafka.event.produce.CusInfoPayload;
import com.onemount.orchestration.kafka.event.produce.OnboardEventProduce;
import com.onemount.orchestration.kafka.service.ProduceService;
import com.onemount.orchestration.model.OnboardData;
import com.onemount.orchestration.model.ProcessData;
import com.onemount.orchestration.service.ContentProcessService;
import com.onemount.orchestration.service.DeserializeService;
import com.onemount.orchestration.service.ErrorGroupService;
import com.onemount.orchestration.support.DateConverter;
import com.onemount.orchestration.support.retry.RetryHelper;
import com.onemount.orchestration.support.utils.LengthValidationUtil;
import com.onemount.orchestration.support.utils.Log;
import com.onemount.orchestration.support.utils.LogData;
import com.onemount.orchestration.support.utils.StringUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class OnboardProcessService implements ContentProcessService<OnboardData> {

    private static final String MESSAGE_TYPE = "CUSTOMER_INFO";
    private static final int LENGTH_255 = 255;
    private static final String[] FILE_OUTPUT_HEADER = {
            "loyalty_id",
            "ds_partition_date",
            "error_group",
            "error_message",
            "error_message_detail",
            "retry"
    };

    private final DeserializeService deserializeService;
    private final ServiceClient serviceClient;
    private final ProduceService produceService;
    private final KafkaAuditTrailCusInfoRepository kafkaAuditTrailCusInfoRepository;
    private final RetryTemplate retryTemplate;
    private final ErrorGroupService errorGroupService;

    @Value("${app.program-code}")
    private String programId;

    @Value("${app.business-code}")
    private String businessId;

    @Value("${kafka.topic.cus-info-produce}")
    private String cusInfoProduceTopic;

    @Value("${kafka.type}")
    private String kafkaType;

    @Override
    public EProcessServiceId getProcessId() {
        return EProcessServiceId.ONBOARD;
    }

    @Override
    public String getOrderKey(OnboardData data) {
        if (Objects.nonNull(data)) {
            return data.getLoyaltyCusId();
        }
        return null;
    }

    @Override
    public OnboardData deserialize(String data) {
        return deserializeService.deserialize(data, OnboardData.class);
    }

    @Override
    public void process(ProcessData<OnboardData> processData) {

        String requestId = UUID.randomUUID().toString();
        OnboardData onboard = processData.getDataObj();
        ErrorCode errorCode = verifyMessage(onboard);
        ServiceError serviceErrorCode = null;
        MemberRegisterClientRes registerRes = null;
        if (errorCode == null) {
            try {
                registerRes = registerMember(onboard, requestId);
                onboard.setRegistrationDate(DateConverter.toString(registerRes.getData().getMemberProfile().getRegistrationDate()));
                serviceErrorCode = ServiceError.builder()
                        .code(String.valueOf(registerRes.getMeta().getCode()))
                        .message(registerRes.getMeta().getMessage())
                        .build();

                Log.info(LogData.createLogData()
                        .append("msg", "CusInfoHandler -- Process member registration was successful.")
                        .append("file_name", processData.getFileName())
                        .append("loy_cus_id", onboard.getLoyaltyCusId())
                        .append("request_id", requestId)
                );
            } catch (BusinessException ex) {
                serviceErrorCode = ServiceError.builder()
                        .code(String.valueOf(ex.getErrCode()))
                        .message(ex.getMessage())
                        .build();
                Log.warn(LogData.createLogData()
                        .append("msg", "CusInfoHandler -- Process register member was unsuccessful.")
                        .append("loyalty_cus_id", onboard.getLoyaltyCusId())
                        .append("file_name", processData.getFileName())
                        .append("request_id", requestId)
                        .append("error_message", ex.getMessage())
                );
            } catch (Exception ex) {
                serviceErrorCode = ServiceError.builder()
                        .code(String.valueOf(ServiceErrorCode.SERVER_ERROR.getValue()))
                        .message(ex.getMessage())
                        .build();
                Log.error(LogData.createLogData()
                        .append("msg", "CusInfoHandler -- Exception while processing register member.")
                        .append("file_name", processData.getFileName())
                        .append("loyalty_cus_id", onboard.getLoyaltyCusId())
                        .append("request_id", requestId)
                        .append("error", ex.getMessage())
                );
            } finally {
                onboard.setServiceError(serviceErrorCode);
            }
        } else {
            onboard.setErrorCode(errorCode);
        }

        insertAuditTrail(processData, onboard, requestId);

        produceResponseToMSK(processData, onboard, requestId, registerRes);
    }

    @Override
    public boolean isProcessed(ProcessData<OnboardData> processData) {
        OnboardData data = processData.getDataObj();
        List<KafkaAuditTrailCusInfo> cusInfoList =
                kafkaAuditTrailCusInfoRepository.getCusInfoByLoyaltyCusIdAndFileNameAndDsPartitionDate(
                        data.getLoyaltyCusId(),
                        processData.getFileName(),
                        data.getDsPartitionDate()
                );

        if (!cusInfoList.isEmpty()) {
            data.setErrorCode(ErrorCode.fromCode(cusInfoList.get(0).getErrorCode()));
        }

        return !cusInfoList.isEmpty();
    }

    @Override
    public String[] getHeader() {
        return FILE_OUTPUT_HEADER;
    }

    @Override
    public String[] generateOutput(ProcessData<OnboardData> processData) {
        OnboardData data = processData.getDataObj();

        Ps2EarningErrorGroup errorGroup = errorGroupService.mapping(
                EFileType.ONBOARD,
                data.getServiceError() != null ? data.getServiceError().getCode() : null,
                data.getErrorCode() != null ? data.getErrorCode().getCode() : null
        );

        return new String[]{
                data.getLoyaltyCusId(),
                data.getDsPartitionDate(),
                errorGroup.getCode(),
                errorGroup.getMessage(),
                errorGroup.getMessageDetail(),
                null
        };
    }

    private ErrorCode verifyMessage(OnboardData eventConsume) {
        if (StringUtils.isEmpty(eventConsume.getLoyaltyCusId())) {
            return ErrorCode.LOYALTY_CUS_ID_CANNOT_NULL;
        }

        if (!LengthValidationUtil.isValidString(eventConsume.getLoyaltyCusId(), LENGTH_255)) {
            return ErrorCode.LOYALTY_CUS_ID_LENGTH_INVALID;
        }

        if (!StringUtil.isAlphanumeric(eventConsume.getLoyaltyCusId())) {
            return ErrorCode.LOYALTY_CUS_ID_INVALID;
        }

        return null;
    }

    /*
        Prepare request
        Call service to register new member
     */
    private MemberRegisterClientRes registerMember(OnboardData onboard,
                                                   String requestId) {

        MemberServiceRegisterReq registerReq = new MemberServiceRegisterReq();
        registerReq.setProgramId(programId);
        registerReq.setBusinessId(businessId);
        registerReq.setPartnerCustomerId(onboard.getLoyaltyCusId());

        return RetryHelper.executeWithRetry(
                retryTemplate,
                () -> serviceClient.registerV2(registerReq, requestId),
                requestId,
                "CallApiMemberRegister"
        );
    }

    private void insertAuditTrail(ProcessData<?> processData,
                                  OnboardData onboardData,
                                  String requestId
    ) {
        try {
            KafkaAuditTrailCusInfo cusInfo = createAuditCusInfo(processData, onboardData, requestId);
            kafkaAuditTrailCusInfoRepository.save(cusInfo);
            Log.info(LogData.createLogData()
                    .append("msg", "CusInfoHandler -- Insert audit trail was successful.")
                    .append("file_name", processData.getFileName())
                    .append("loyalty_cus_id", cusInfo.getLoyaltyCusId())
                    .append("request_id", requestId)
            );
        } catch (Exception ex) {
            Log.error(LogData.createLogData()
                    .append("msg", "CusInfoHandler -- Exception while inserting audit trail.")
                    .append("file_name", processData.getFileName())
                    .append("request_id", requestId)
                    .append("error", ex.getMessage())
            );
        }
    }

    private KafkaAuditTrailCusInfo createAuditCusInfo(ProcessData<?> processData,
                                                      OnboardData onboard,
                                                      String requestId) {

        KafkaAuditTrailCusInfo cusInfo = new KafkaAuditTrailCusInfo();
        cusInfo.setDsPartitionDate(onboard.getDsPartitionDate());
        cusInfo.setFileName(processData.getFileName());
        cusInfo.setMessageId(requestId);
        cusInfo.setMessageType(MessageType.CUSTOMER_INFO.getValue());
        cusInfo.setLoyaltyCusId(onboard.getLoyaltyCusId());

        if (onboard.getErrorCode() != null) {
            ErrorCode errorCode = onboard.getErrorCode();
            cusInfo.setErrorCode(errorCode.getCode());
            cusInfo.setErrorMessage(errorCode.getMessage());
        }

        if (onboard.getServiceError() != null) {
            ServiceError serviceError = onboard.getServiceError();
            cusInfo.setServiceErrorCode(serviceError.getCode());
            cusInfo.setServiceErrorMessage(serviceError.getMessage());
        }

        cusInfo.setCreatedAt(new Date());
        cusInfo.setCreatedBy(Constant.CREATED_BY_SYSTEM);

        return cusInfo;
    }

    private void produceResponseToMSK(ProcessData<?> processData,
                                      OnboardData onboard,
                                      String requestId,
                                      MemberRegisterClientRes registerRes
    ) {
        try {
            OnboardEventProduce onboardEventProduce = new OnboardEventProduce();

            onboardEventProduce.setMessageId(requestId);
            onboardEventProduce.setMessageRefId(processData.getProcessId());
            onboardEventProduce.setTimestamp(System.currentTimeMillis());
            onboardEventProduce.setMessageType(MESSAGE_TYPE);

            onboard.setRegistrationDate(getRegistrationDate(onboard, requestId));

            CusInfo cusInfo = new CusInfo();
            cusInfo.setDsPartitionDate(onboard.getDsPartitionDate());
            cusInfo.setLoyaltyCusId(onboard.getLoyaltyCusId());
            cusInfo.setRegistrationDate(onboard.getRegistrationDate());

            if (Objects.nonNull(registerRes)) {
                MemberRegister registerResData = registerRes.getData();
                MemberProfile memberProfile = registerResData.getMemberProfile();
                cusInfo.setMemberCode(memberProfile.getMemberCode());
            }

            CusInfoPayload cusInfoPayload = new CusInfoPayload();
            cusInfoPayload.setCustomerInfo(cusInfo);

            onboardEventProduce.setPayload(cusInfoPayload);
            onboardEventProduce.setErrorCode(onboard.getErrorCode().getCode());
            onboardEventProduce.setErrorMessage(onboard.getErrorCode().getMessage());

            ProduceService.ProduceMessage produceMessage = new ProduceService.ProduceMessage();
            produceMessage.setKey(requestId);
            produceMessage.setValue(onboardEventProduce);

            produceService.produceAsync(produceMessage, cusInfoProduceTopic, kafkaType);

            Log.info(LogData.createLogData()
                    .append("msg", "CusInfoHandler -- Produce response to MSK was successful.")
                    .append("payload", onboardEventProduce)
            );

        } catch (Exception ex) {
            Log.error(LogData.createLogData()
                    .append("msg", "CusInfoHandler -- Exception while producing response to MSK.")
                    .append("request_id", requestId)
                    .append("err", ex.getMessage())
            );
        }
    }

    private String getRegistrationDate(OnboardData onboard, String requestId) {
        if (ErrorCode.SUCCESS.getCode().equals(onboard.getErrorCode().getCode())) {
            return onboard.getRegistrationDate();
        } else if (ErrorCode.CUSTOMER_EXISTED.getCode().equals(onboard.getErrorCode().getCode())) {
            FullMemberInquiryReq inquiryReq = toMemberInquireReq(onboard);
            FullMemberInquiryRes inquiryRes = serviceClient.inquiry(inquiryReq, requestId);

            return DateConverter.toString(inquiryRes.getData().getMemberProfile().getRegistrationDate());
        }

        return null;
    }

    private FullMemberInquiryReq toMemberInquireReq(OnboardData onboard) {
        return FullMemberInquiryReq.builder()
                .businessId(businessId)
                .programId(programId)
                .customerIdentifier(toCustomerIdentify(onboard.getLoyaltyCusId()))
                .profileRequired(true)
                .balanceRequired(false)
                .vdCardRequired(false)
                .build();
    }

    private CustomerIdentify toCustomerIdentify(String idNo) {
        CustomerIdentify identify = new CustomerIdentify();
        identify.setId(idNo);
        identify.setIdType(EIdType.PARTNER_CUSTOMER_ID);
        return identify;
    }
}
