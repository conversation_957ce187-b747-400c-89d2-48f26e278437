package com.onemount.orchestration.service.impl;

import com.oneid.oneloyalty.client.model.AttributeUpdateReq;
import com.oneid.oneloyalty.client.model.MemberAttributeUpdateReq;
import com.oneid.oneloyalty.client.model.MemberServiceProfileUpdateReq;
import com.oneid.oneloyalty.client.service.ServiceClient;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.entity.AttributeInfoAuditTrail;
import com.oneid.oneloyalty.common.entity.CustomerAttributeInfo;
import com.oneid.oneloyalty.common.entity.CustomerCardInfo;
import com.oneid.oneloyalty.common.entity.CustomerCardInfoVersioning;
import com.oneid.oneloyalty.common.entity.CustomerProductInfo;
import com.oneid.oneloyalty.common.entity.CustomerProductInfoVersioning;
import com.oneid.oneloyalty.common.entity.CustomerProfile;
import com.oneid.oneloyalty.common.entity.CustomerProfileVersioning;
import com.oneid.oneloyalty.common.entity.KafkaAuditTrailCardInfo;
import com.oneid.oneloyalty.common.entity.KafkaAuditTrailCustomerProfile;
import com.oneid.oneloyalty.common.entity.KafkaAuditTrailProductInfo;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import com.oneid.oneloyalty.common.repository.AttributeInfoAuditTrailRepository;
import com.oneid.oneloyalty.common.repository.CustomerAttributeInfoRepository;
import com.oneid.oneloyalty.common.repository.CustomerCardInfoRepository;
import com.oneid.oneloyalty.common.repository.CustomerCardInfoVersioningRepository;
import com.oneid.oneloyalty.common.repository.CustomerProductInfoRepository;
import com.oneid.oneloyalty.common.repository.CustomerProductInfoVersioningRepository;
import com.oneid.oneloyalty.common.repository.CustomerProfileRepository;
import com.oneid.oneloyalty.common.repository.CustomerProfileVersioningRepository;
import com.oneid.oneloyalty.common.repository.KafkaAuditTrailCardInfoRepository;
import com.oneid.oneloyalty.common.repository.KafkaAuditTrailCustomerProfileRepository;
import com.oneid.oneloyalty.common.repository.KafkaAuditTrailProductInfoRepository;
import com.oneid.oneloyalty.common.util.DateTimes;
import com.onemount.orchestration.constant.EAttributeType;
import com.onemount.orchestration.constant.EFileType;
import com.onemount.orchestration.constant.EProcessServiceId;
import com.onemount.orchestration.constant.ErrorCode;
import com.onemount.orchestration.constant.MessageType;
import com.onemount.orchestration.constant.ServiceErrorCode;
import com.onemount.orchestration.entity.Ps2EarningErrorGroup;
import com.onemount.orchestration.kafka.event.produce.CustomerProfileEventProduce;
import com.onemount.orchestration.kafka.service.ProduceService;
import com.onemount.orchestration.model.CustomerProfileData;
import com.onemount.orchestration.model.ProcessData;
import com.onemount.orchestration.service.ContentProcessService;
import com.onemount.orchestration.service.DeserializeService;
import com.onemount.orchestration.service.ErrorGroupService;
import com.onemount.orchestration.support.DateConverter;
import com.onemount.orchestration.support.retry.RetryHelper;
import com.onemount.orchestration.support.utils.AttributeConverter;
import com.onemount.orchestration.support.utils.LengthValidationUtil;
import com.onemount.orchestration.support.utils.Log;
import com.onemount.orchestration.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class CustomerProcessService implements ContentProcessService<CustomerProfileData> {
    private static final int LENGTH_1000 = 1000;
    private static final int DIGITS_38 = 38;
    private static final int FRACTION_DIGITS_6 = 6;
    private static final int DIGITS_30 = 30;
    private static final int FRACTION_DIGITS_0 = 0;
    private static final List<String> VALID_GENDERS = List.of("M", "F", "U", "N");
    private static final String CST_TARGET_GROUP = "CST_TARGET_GROUP";
    private static final String MESSAGE_TYPE = "CUSTOMER_INFO";
    private static final List<String> VALID_STATUS = List.of("A", "I");
    private static final String FORMATTER = "yyyy-MM-dd";
    private static final String VERSION_FORMATTER = "ddMMyyyy";
    private static final String[] FILE_OUTPUT_HEADER = {"loyalty_id", "card_arrangement_id", "product_arrangement_id", "ds_partition_date", "error_group", "error_message", "error_message_detail", "retry"};
    private final KafkaAuditTrailCustomerProfileRepository auditCustomerProfileRepository;
    private final CustomerProfileRepository customerProfileRepository;
    private final KafkaAuditTrailCardInfoRepository auditCardInfoRepository;
    private final KafkaAuditTrailProductInfoRepository auditProductInfoRepository;
    private final CustomerProductInfoRepository customerProductInfoRepository;
    private final CustomerCardInfoRepository customerCardInfoRepository;
    private final DeserializeService deserializeService;
    private final ServiceClient serviceClient;
    private final CustomerAttributeInfoRepository customerAttributeInfoRepository;
    private final AttributeInfoAuditTrailRepository attributeInfoAuditTrailRepository;
    private final RetryTemplate retryTemplate;
    private final ProduceService produceService;
    private final ErrorGroupService errorGroupService;
    private final CustomerProfileVersioningRepository customerProfileVersioningRepository;
    private final CustomerCardInfoVersioningRepository customerCardInfoVersioningRepository;
    private final CustomerProductInfoVersioningRepository customerProductInfoVersioningRepository;

    @Value("${app.program-code}")
    private String programId;

    @Value("${app.business-code}")
    private String businessId;

    @Value("${app.enable-update-member-attributes:true}")
    private Boolean enableUpdateMemberAttributes;

    @Value("${kafka.topic.customer-profile-produce}")
    private String customerProfileProduceTopic;

    @Override
    public EProcessServiceId getProcessId() {
        return EProcessServiceId.CUSTOMER;
    }

    @Override
    public String getOrderKey(CustomerProfileData data) {
        if (Objects.nonNull(data) && Objects.nonNull(data.getPayload())) {
            return data.getPayload().getLoyaltyCusId();
        }
        return null;
    }

    @Override
    public CustomerProfileData deserialize(String data) {
        return deserializeService.deserialize(data, CustomerProfileData.class);
    }

    @Override
    public String[] getHeader() {
        return FILE_OUTPUT_HEADER;
    }

    @Override
    public String[] generateOutput(ProcessData<CustomerProfileData> processData) {
        CustomerProfileData data = processData.getDataObj();

        String cardIds = "";
        String productIds = "";

        if (Objects.nonNull(data.getPayload().getCardInfo()) && !data.getPayload().getCardInfo().isEmpty()) {
            cardIds = data.getPayload().getCardInfo().stream()
                    .map(CustomerProfileData.CardInfo::getArrangementId)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.joining("|"));
        }
        if (Objects.nonNull(data.getPayload().getProductInfo()) && !data.getPayload().getProductInfo().isEmpty()) {
            productIds =
                    data.getPayload().getProductInfo().stream()
                            .map(CustomerProfileData.ProductInfo::getArrangementId)
                            .filter(StringUtils::isNotEmpty)
                            .collect(Collectors.joining("|"));
        }

        Ps2EarningErrorGroup errorGroup = errorGroupService.mapping(
                EFileType.CUSTOMER,
                data.getServiceErrorCode() != null ? String.valueOf(data.getServiceErrorCode()) : null,
                data.getErrorCode() != null ? data.getErrorCode().getCode() : null
        );

        return new String[]{
                data.getPayload().getLoyaltyCusId(),
                cardIds,
                productIds,
                data.getDsPartitionDate(),
                errorGroup.getCode(),
                errorGroup.getMessage(),
                errorGroup.getMessageDetail(),
                null
        };
    }

    @Override
    public void process(ProcessData<CustomerProfileData> processData) {
        String requestId = UUID.randomUUID().toString();
        CustomerProfileData cusProfileData = processData.getDataObj();
        ErrorCode errorCode = verifyMessage(cusProfileData);
        Integer serviceErrorCode = null;
        String serviceErrorMessage = null;
        if (Objects.isNull(errorCode)) {
            try {
                CustomerProfileData.Payload payload = cusProfileData.getPayload();

                List<AttributeUpdateReq> oldAttributes;
                List<AttributeUpdateReq> newAttributes;
                List<AttributeUpdateReq> customerProfileAttributes = new ArrayList<>();
                List<AttributeUpdateReq> productAttributes = new ArrayList<>();
                List<AttributeUpdateReq> cardAttributes = new ArrayList<>();
                List<AttributeUpdateReq> cstTargetGroupAttributes = new ArrayList<>();
                List<AttributeUpdateReq> finalAttributes = new ArrayList<>();

                CustomerProfile customerProfile = null;
                CustomerProfileVersioning customerProfileVersioning = null;
                List<CustomerProductInfoVersioning> customerProductInfoVersionings = new ArrayList<>();
                List<CustomerProductInfo> newProducts = new ArrayList<>();
                List<CustomerCardInfo> newCards = new ArrayList<>();
                List<CustomerCardInfoVersioning> customerCardInfoVersioningList = new ArrayList<>();
                List<CustomerAttributeInfo> newAttributeInfos = new ArrayList<>();
                Long cobDate = extractTimestampFromFilename(processData.getFileName());
                Date versionDate = getVersionDate(processData.getFileName());
                String versionCode = getVersionCode(versionDate);

                // Get Customer Profile Attributes
                if (Objects.nonNull(payload.getCustomerInfo())) {
                    customerProfile = customerProfileRepository.findByLoyaltyCusId(payload.getLoyaltyCusId()).orElse(null);

                    oldAttributes = Objects.nonNull(customerProfile) ? toAttributes(customerProfile) : Collections.emptyList();

                    customerProfile = toCustomerProfile(customerProfile, payload.getCustomerInfo(), payload.getLoyaltyCusId());

                    newAttributes = toAttributes(customerProfile);

                    customerProfileAttributes = AttributeConverter.toFinalAttributesUniqueLevel0(oldAttributes, newAttributes);

                    if (cobDate != null) {
                        customerProfileVersioning = toCustomerProfileVersioning(customerProfile, DateTimes.date(cobDate));
                    }
                }

                // Get Product Attributes
                if (CollectionUtils.isNotEmpty(payload.getProductInfo())) {
                    oldAttributes = new ArrayList<>();
                    newAttributes = new ArrayList<>();

                    Map<String, CustomerProductInfo> productByArrangementId = customerProductInfoRepository
                            .findByLoyaltyCusId(payload.getLoyaltyCusId())
                            .stream().collect(Collectors.toMap(CustomerProductInfo::getArrangementId, c -> c, (k1, k2) -> k1));

                    for (CustomerProfileData.ProductInfo productInfo : payload.getProductInfo()) {
                        CustomerProductInfo product = productByArrangementId.get(productInfo.getArrangementId());

                        if (Objects.nonNull(product)) {
                            oldAttributes.addAll(toAttributes(product));
                        }

                        product = toCustomerProductInfo(product, productInfo, payload.getLoyaltyCusId());

                        productByArrangementId.put(product.getArrangementId(), product);
                        newProducts.add(product);

                        if (cobDate != null) {
                            CustomerProductInfoVersioning versioning = toCustomerProductInfoVersioning(productInfo, DateTimes.date(cobDate), payload.getLoyaltyCusId());
                            customerProductInfoVersionings.add(versioning);
                        }
                    }

                    for (CustomerProductInfo product : productByArrangementId.values()) {
                        newAttributes.addAll(toAttributes(product));
                    }

                    productAttributes = AttributeConverter.toFinalAttributes(oldAttributes, newAttributes);
                }

                // Get Card Attributes
                if (CollectionUtils.isNotEmpty(payload.getCardInfo())) {
                    oldAttributes = new ArrayList<>();
                    newAttributes = new ArrayList<>();

                    Map<String, CustomerCardInfo> cardByCardId = customerCardInfoRepository
                            .findByLoyaltyCusId(payload.getLoyaltyCusId())
                            .stream().collect(Collectors.toMap(CustomerCardInfo::getCardId, c -> c, (k1, k2) -> k1));

                    Set<String> currentCardIds = payload.getCardInfo().stream()
                            .map(CustomerProfileData.CardInfo::getCardId)
                            .collect(Collectors.toSet());

                    for (CustomerProfileData.CardInfo cardInfo : payload.getCardInfo()) {
                        CustomerCardInfo card = cardByCardId.get(cardInfo.getCardId());

                        if (Objects.nonNull(card)) {
                            oldAttributes.addAll(toAttributes(card));
                        }

                        card = toCustomerCardInfo(card, cardInfo, payload.getLoyaltyCusId());

                        cardByCardId.put(card.getCardId(), card);
                        newCards.add(card);

                        customerCardInfoVersioningList.add(toCustomerCardInfoVersioning(card, versionCode, versionDate));
                    }

                    for (CustomerCardInfo card : cardByCardId.values()) {
                        if (!currentCardIds.contains(card.getCardId())) {
                            customerCardInfoVersioningList.add(toCustomerCardInfoVersioning(card, versionCode, versionDate));
                        }
                    }

                    for (CustomerCardInfo card : cardByCardId.values()) {
                        newAttributes.addAll(toAttributes(card));
                    }

                    cardAttributes = AttributeConverter.toFinalAttributes(oldAttributes, newAttributes);
                }

                // Get Cst Target Group Attributes
                if (CollectionUtils.isNotEmpty(payload.getCstTargetGroup())) {
                    oldAttributes = new ArrayList<>();
                    newAttributes = new ArrayList<>();
                    Map<String, CustomerAttributeInfo> attributeByValue = customerAttributeInfoRepository
                            .findByLoyaltyCusIdAndCode(payload.getLoyaltyCusId(), CST_TARGET_GROUP)
                            .stream().collect(Collectors.toMap(CustomerAttributeInfo::getValue, c -> c, (k1, k2) -> k1));

                    for (CustomerProfileData.AttributeInfo attributeInfo : payload.getCstTargetGroup()) {
                        CustomerAttributeInfo attribute = attributeByValue.get(attributeInfo.getValue());

                        if (Objects.nonNull(attribute)) {
                            oldAttributes.addAll(toAttributes(attribute));
                        }

                        attribute = toCustomerAttributeInfo(attribute, attributeInfo, payload.getLoyaltyCusId(), CST_TARGET_GROUP);

                        attributeByValue.put(attribute.getValue(), attribute);
                        newAttributeInfos.add(attribute);
                    }

                    for (CustomerAttributeInfo attribute : attributeByValue.values()) {
                        newAttributes.addAll(toAttributes(attribute));
                    }

                    cstTargetGroupAttributes = AttributeConverter.toFinalAttributesFilterStatus(oldAttributes, newAttributes);
                }

                /*
                    Update Member Profile
                 */
                if (Objects.nonNull(payload.getCustomerInfo())) {
                    MemberServiceProfileUpdateReq updateMemberProfileReq = toUpdateMemberProfileReq(cusProfileData);
                    callApiUpdateProfileV2(updateMemberProfileReq, requestId);
                    Log.info(LogData.createLogData()
                            .append("msg", "CustomerProfileHandler -- Process update member profile was successful.")
                            .append("request_id", requestId)
                            .append("loyalty_cus_id", payload.getLoyaltyCusId())
                            .append("file_name", processData.getFileName())
                    );
                }

                // Update Member Attributes
                if (CollectionUtils.isNotEmpty(customerProfileAttributes)) {
                    finalAttributes.addAll(customerProfileAttributes);
                }

                if (CollectionUtils.isNotEmpty(productAttributes)) {
                    finalAttributes.addAll(productAttributes);
                }

                if (CollectionUtils.isNotEmpty(cardAttributes)) {
                    finalAttributes.addAll(cardAttributes);
                }

                if (CollectionUtils.isNotEmpty(cstTargetGroupAttributes)) {
                    finalAttributes.addAll(cstTargetGroupAttributes);
                }

                /*
                    Update Member Attribute
                 */
                if (Boolean.TRUE.equals(enableUpdateMemberAttributes) && CollectionUtils.isNotEmpty(finalAttributes)) {
                    MemberAttributeUpdateReq updateAttributesReq = toUpdateAttributesReq(cusProfileData, finalAttributes);

                    /*
                     * Call api update attribute to loyalty core
                     */
                    callApiUpdateAttributeV1(updateAttributesReq, requestId);

                    Log.info(LogData.createLogData()
                            .append("msg", "CustomerProfileHandler -- Process update member attributes was successful.")
                            .append("request_id", requestId)
                            .append("loyalty_cus_id", payload.getLoyaltyCusId())
                            .append("file_name", processData.getFileName())
                    );
                }

                // Save Data

                if (Objects.nonNull(customerProfile)) {
                    customerProfileRepository.save(customerProfile);
                }

                if (Objects.nonNull(customerProfileVersioning)) {
                    customerProfileVersioningRepository.save(customerProfileVersioning);
                }

                if (CollectionUtils.isNotEmpty(newProducts)) {
                    customerProductInfoRepository.saveAll(newProducts);
                }

                if (Objects.nonNull(customerProductInfoVersionings)) {
                    customerProductInfoVersioningRepository.saveAll(customerProductInfoVersionings);
                }

                if (CollectionUtils.isNotEmpty(newCards)) {
                    customerCardInfoRepository.saveAll(newCards);
                }

                if (CollectionUtils.isNotEmpty(customerCardInfoVersioningList)) {
                    customerCardInfoVersioningRepository.saveAll(customerCardInfoVersioningList);
                }

                if (CollectionUtils.isNotEmpty(newAttributeInfos)) {
                    customerAttributeInfoRepository.saveAll(newAttributeInfos);
                }

                // Produce message
                produceMessage(cusProfileData, requestId, processData.getProcessId());

                serviceErrorCode = ServiceErrorCode.SUCCESS.getValue();
                serviceErrorMessage = ErrorCode.SUCCESS.getMessage();
            } catch (BusinessException e) {
                serviceErrorCode = e.getErrCode();
                serviceErrorMessage = e.getMessage();

                Log.warn(LogData.createLogData()
                        .append("msg", "CustomerProfileHandler -- Process update profile was unsuccessful.")
                        .append("loyalty_cus_id", processData.getOrderKey())
                        .append("request_id", requestId)
                        .append("file_name", processData.getFileName())
                        .append("error_message", serviceErrorMessage)
                );
            } catch (Exception e) {
                e.printStackTrace();
                serviceErrorCode = ServiceErrorCode.SERVER_ERROR.getValue();
                serviceErrorMessage = e.getMessage();

                Log.error(LogData.createLogData()
                        .append("msg", "CustomerProfileHandler – Exception while updating member profile.")
                        .append("loyalty_cus_id", processData.getOrderKey())
                        .append("request_id", requestId)
                        .append("file_name", processData.getFileName())
                        .append("error", e.getMessage())
                );
            } finally {
                errorCode = ErrorCode.convert(serviceErrorCode);
            }
        }
        // Message is invalid
        else {
            Log.error(
                    LogData.createLogData()
                            .append("msg", "CustomerProfileHandler -- Update profile payload is invalid format")
                            .append("payload", processData.getDataObj())
                            .append("request_id", requestId)
                            .append("file_name", processData.getFileName())
                            .append("error", errorCode.getMessage())
            );
        }
        cusProfileData.setErrorCode(errorCode);
        cusProfileData.setServiceErrorCode(serviceErrorCode);
        cusProfileData.setServiceErrorMessage(serviceErrorMessage);

        insertAuditTrail(cusProfileData, processData, requestId);
    }

    private void callApiUpdateAttributeV1(MemberAttributeUpdateReq updateAttributesReq, String requestId) {

        RetryHelper.executeWithRetry(
                retryTemplate,
                () -> serviceClient.updateAttributesV1(updateAttributesReq, requestId),
                requestId,
                "CallApiUpdateAttributes"
        );
    }

    private void callApiUpdateProfileV2(MemberServiceProfileUpdateReq updateMemberProfileReq, String requestId) {
        RetryHelper.executeWithRetry(
                retryTemplate,
                () -> serviceClient.updateMemberProfileV2(updateMemberProfileReq, requestId),
                requestId,
                "CallApiUpdateMemberProfileV2"
        );
    }

    @Override
    public boolean isProcessed(ProcessData<CustomerProfileData> processData) {
        CustomerProfileData data = processData.getDataObj();
        CustomerProfileData.Payload payload = data.getPayload();
        List<KafkaAuditTrailCustomerProfile> customerProfileList =
                auditCustomerProfileRepository.findByLoyaltyCusIdAndFileNameAndDsPartitionDate(
                        payload.getLoyaltyCusId(),
                        processData.getFileName(),
                        data.getDsPartitionDate()
                );

        if (!customerProfileList.isEmpty()) {
            data.setErrorCode(ErrorCode.of(customerProfileList.get(0).getErrorCode()));
        }

        return !customerProfileList.isEmpty();
    }

    private ErrorCode verifyMessage(CustomerProfileData customerProfile) {
        if (!isValidMaxLength(customerProfile)) {
            return ErrorCode.LENGTH_INVALID_FORMAT;
        }

        if (Objects.isNull(customerProfile.getPayload())) {
            return ErrorCode.PAYLOAD_CANNOT_NULL;
        }

        if (StringUtils.isEmpty(customerProfile.getPayload().getLoyaltyCusId())) {
            return ErrorCode.LOYALTY_CUS_ID_CANNOT_NULL;
        }

        ErrorCode errorCode = verifyCustomerInfo(customerProfile);
        if (Objects.nonNull(errorCode)) {
            return errorCode;
        }

        errorCode = verifyProductInfo(customerProfile);
        if (Objects.nonNull(errorCode)) {
            return errorCode;
        }

        errorCode = verifyCardInfo(customerProfile);
        if (Objects.nonNull(errorCode)) {
            return errorCode;
        }

        errorCode = verifyTargetGroup(customerProfile);
        if (Objects.nonNull(errorCode)) {
            return errorCode;
        }

        return null;
    }

    private boolean isValidMaxLength(CustomerProfileData customerProfile) {
        boolean isValidMaxLengthLoyaltyCusId = true;
        boolean isValidMaxLengthProfile = true;
        boolean isValidMaxLengthProducts = true;
        boolean isValidMaxLengthCards = true;

        if (Objects.nonNull(customerProfile.getPayload())) {
            // LOYALTY_CUS_ID
            if (!LengthValidationUtil.isValidString(customerProfile.getPayload().getLoyaltyCusId(), LENGTH_1000)) {
                customerProfile.getPayload().setLoyaltyCusId(LengthValidationUtil.trimString(customerProfile.getPayload().getLoyaltyCusId(), LENGTH_1000));
                isValidMaxLengthLoyaltyCusId = false;
            }

            if (Objects.nonNull(customerProfile.getPayload().getCustomerInfo())) {
                isValidMaxLengthProfile = isValidMaxLengthProfile(customerProfile.getPayload().getCustomerInfo());
            }

            if (CollectionUtils.isNotEmpty(customerProfile.getPayload().getProductInfo())) {
                isValidMaxLengthProducts = isValidMaxLengthProducts(customerProfile.getPayload().getProductInfo());
            }

            if (CollectionUtils.isNotEmpty(customerProfile.getPayload().getCardInfo())) {
                isValidMaxLengthCards = isValidMaxLengthCards(customerProfile.getPayload().getCardInfo());
            }
        }

        return isValidMaxLengthLoyaltyCusId && isValidMaxLengthProfile && isValidMaxLengthProducts && isValidMaxLengthCards;
    }

    private boolean isValidMaxLengthProfile(CustomerProfileData.CustomerInfo customerInfo) {
        boolean isValidMaxLengthProfile = true;

        // PRD_UPDATE_FLAG
        if (!LengthValidationUtil.isValidString(customerInfo.getPrdUpdateFlag(), LENGTH_1000)) {
            customerInfo.setPrdUpdateFlag(LengthValidationUtil.trimString(customerInfo.getPrdUpdateFlag(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // LOYALTY_OB_DATE
        if (!LengthValidationUtil.isValidString(customerInfo.getLoyaltyObDate(), LENGTH_1000)) {
            customerInfo.setLoyaltyObDate(LengthValidationUtil.trimString(customerInfo.getLoyaltyObDate(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // FRST_CTC_DT
        if (!LengthValidationUtil.isValidString(customerInfo.getFrstCtcDt(), LENGTH_1000)) {
            customerInfo.setFrstCtcDt(LengthValidationUtil.trimString(customerInfo.getFrstCtcDt(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_BRTH_DT
        if (!LengthValidationUtil.isValidString(customerInfo.getCstBrthDt(), LENGTH_1000)) {
            customerInfo.setCstBrthDt(LengthValidationUtil.trimString(customerInfo.getCstBrthDt(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // TIER_START_DATE
        if (!LengthValidationUtil.isValidString(customerInfo.getTierStartDate(), LENGTH_1000)) {
            customerInfo.setTierStartDate(LengthValidationUtil.trimString(customerInfo.getTierStartDate(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // TIER_END_DATE
        if (!LengthValidationUtil.isValidString(customerInfo.getTierEndDate(), LENGTH_1000)) {
            customerInfo.setTierEndDate(LengthValidationUtil.trimString(customerInfo.getTierEndDate(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_PRE_TIER
        if (!LengthValidationUtil.isValidString(customerInfo.getCstPreTier(), LENGTH_1000)) {
            customerInfo.setCstPreTier(LengthValidationUtil.trimString(customerInfo.getCstPreTier(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_TIER
        if (!LengthValidationUtil.isValidString(customerInfo.getCstTier(), LENGTH_1000)) {
            customerInfo.setCstTier(LengthValidationUtil.trimString(customerInfo.getCstTier(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_LOYALTY_TIER
        if (!LengthValidationUtil.isValidString(customerInfo.getCstLoyaltyTier(), LENGTH_1000)) {
            customerInfo.setCstLoyaltyTier(LengthValidationUtil.trimString(customerInfo.getCstLoyaltyTier(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // TARGET_SEGMENT_CODE
        if (!LengthValidationUtil.isValidString(customerInfo.getTargetSegmentCode(), LENGTH_1000)) {
            customerInfo.setTargetSegmentCode(LengthValidationUtil.trimString(customerInfo.getTargetSegmentCode(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_NATIONALITY
        if (!LengthValidationUtil.isValidString(customerInfo.getCstNationality(), LENGTH_1000)) {
            customerInfo.setCstNationality(LengthValidationUtil.trimString(customerInfo.getCstNationality(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // EMPLOYEE_IND
        if (!LengthValidationUtil.isValidString(customerInfo.getEmployeeInd(), LENGTH_1000)) {
            customerInfo.setEmployeeInd(LengthValidationUtil.trimString(customerInfo.getEmployeeInd(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_MOBILE_IND
        if (!LengthValidationUtil.isValidString(customerInfo.getCstMobileInd(), LENGTH_1000)) {
            customerInfo.setCstMobileInd(LengthValidationUtil.trimString(customerInfo.getCstMobileInd(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // BIZ_LINE
        if (!LengthValidationUtil.isValidString(customerInfo.getBizLine(), LENGTH_1000)) {
            customerInfo.setBizLine(LengthValidationUtil.trimString(customerInfo.getBizLine(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_FIRST_LOGIN_DATE
        if (!LengthValidationUtil.isValidString(customerInfo.getCstFirstLoginDate(), LENGTH_1000)) {
            customerInfo.setCstFirstLoginDate(LengthValidationUtil.trimString(customerInfo.getCstFirstLoginDate(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_LAST_LOGIN_DATE
        if (!LengthValidationUtil.isValidString(customerInfo.getCstLastLoginDate(), LENGTH_1000)) {
            customerInfo.setCstLastLoginDate(LengthValidationUtil.trimString(customerInfo.getCstLastLoginDate(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_DEBT_GROUP
        if (!LengthValidationUtil.isValidString(customerInfo.getCstDebtGroup(), LENGTH_1000)) {
            customerInfo.setCstDebtGroup(LengthValidationUtil.trimString(customerInfo.getCstDebtGroup(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_AST_CLSS
        if (!LengthValidationUtil.isValidString(customerInfo.getCstAstClss(), LENGTH_1000)) {
            customerInfo.setCstAstClss(LengthValidationUtil.trimString(customerInfo.getCstAstClss(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_STAFF_IND
        if (!LengthValidationUtil.isValidString(customerInfo.getCstStaffInd(), LENGTH_1000)) {
            customerInfo.setCstStaffInd(LengthValidationUtil.trimString(customerInfo.getCstStaffInd(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_BANK_SEGMENT
        if (!LengthValidationUtil.isValidString(customerInfo.getCstBankSegment(), LENGTH_1000)) {
            customerInfo.setCstBankSegment(LengthValidationUtil.trimString(customerInfo.getCstBankSegment(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_TYPE
        if (!LengthValidationUtil.isValidString(customerInfo.getCstType(), LENGTH_1000)) {
            customerInfo.setCstType(LengthValidationUtil.trimString(customerInfo.getCstType(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_PBT
        if (!LengthValidationUtil.isValidBigDecimal(customerInfo.getCstPbt(), DIGITS_38, FRACTION_DIGITS_6)) {
            customerInfo.setCstPbt(LengthValidationUtil.trimBigDecimal(customerInfo.getCstPbt(), DIGITS_38, FRACTION_DIGITS_6));
            isValidMaxLengthProfile = false;
        }

        // CST_TOI
        if (!LengthValidationUtil.isValidBigDecimal(customerInfo.getCstToi(), DIGITS_38, FRACTION_DIGITS_6)) {
            customerInfo.setCstToi(LengthValidationUtil.trimBigDecimal(customerInfo.getCstToi(), DIGITS_38, FRACTION_DIGITS_6));
            isValidMaxLengthProfile = false;
        }

        // CST_COST
        if (!LengthValidationUtil.isValidBigDecimal(customerInfo.getCstCost(), DIGITS_38, FRACTION_DIGITS_6)) {
            customerInfo.setCstCost(LengthValidationUtil.trimBigDecimal(customerInfo.getCstCost(), DIGITS_38, FRACTION_DIGITS_6));
            isValidMaxLengthProfile = false;
        }

        // CST_FAMILY_GROUP
        if (!LengthValidationUtil.isValidString(customerInfo.getCstFamilyGroup(), LENGTH_1000)) {
            customerInfo.setCstFamilyGroup(LengthValidationUtil.trimString(customerInfo.getCstFamilyGroup(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_FAMILY_MEMBER_TYPE
        if (!LengthValidationUtil.isValidString(customerInfo.getCstFamilyMemberType(), LENGTH_1000)) {
            customerInfo.setCstFamilyMemberType(LengthValidationUtil.trimString(customerInfo.getCstFamilyMemberType(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_OCCUPATION
        if (!LengthValidationUtil.isValidString(customerInfo.getCstOccupation(), LENGTH_1000)) {
            customerInfo.setCstOccupation(LengthValidationUtil.trimString(customerInfo.getCstOccupation(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_EKYC_STATUS
        if (!LengthValidationUtil.isValidString(customerInfo.getCstEkycStatus(), LENGTH_1000)) {
            customerInfo.setCstEkycStatus(LengthValidationUtil.trimString(customerInfo.getCstEkycStatus(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_HOME_BRANCH
        if (!LengthValidationUtil.isValidString(customerInfo.getCstHomeBranch(), LENGTH_1000)) {
            customerInfo.setCstHomeBranch(LengthValidationUtil.trimString(customerInfo.getCstHomeBranch(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_SUB_BRANCH
        if (!LengthValidationUtil.isValidString(customerInfo.getCstSubBranch(), LENGTH_1000)) {
            customerInfo.setCstSubBranch(LengthValidationUtil.trimString(customerInfo.getCstSubBranch(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_RM_BRANCH
        if (!LengthValidationUtil.isValidString(customerInfo.getCstRmBranch(), LENGTH_1000)) {
            customerInfo.setCstRmBranch(LengthValidationUtil.trimString(customerInfo.getCstRmBranch(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_MERCHANT_IND
        if (!LengthValidationUtil.isValidString(customerInfo.getCstMerchantInd(), LENGTH_1000)) {
            customerInfo.setCstMobileInd(LengthValidationUtil.trimString(customerInfo.getCstMerchantInd(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_NOTI_CHANNEL
        if (!LengthValidationUtil.isValidString(customerInfo.getCstNotiChannel(), LENGTH_1000)) {
            customerInfo.setCstNotiChannel(LengthValidationUtil.trimString(customerInfo.getCstNotiChannel(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_BLK_LV
        if (!LengthValidationUtil.isValidString(customerInfo.getCstBlkLv(), LENGTH_1000)) {
            customerInfo.setCstBlkLv(LengthValidationUtil.trimString(customerInfo.getCstBlkLv(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_WTCH_LIST
        if (!LengthValidationUtil.isValidString(customerInfo.getCstWtchList(), LENGTH_1000)) {
            customerInfo.setCstWtchList(LengthValidationUtil.trimString(customerInfo.getCstWtchList(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_GENDER
        if (!LengthValidationUtil.isValidString(customerInfo.getCstGender(), LENGTH_1000)) {
            customerInfo.setCstGender(LengthValidationUtil.trimString(customerInfo.getCstGender(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_TIER_PROGRAM
        if (!LengthValidationUtil.isValidString(customerInfo.getCstTierProgram(), LENGTH_1000)) {
            customerInfo.setCstTierProgram(LengthValidationUtil.trimString(customerInfo.getCstTierProgram(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_RESIDENCE
        if (!LengthValidationUtil.isValidString(customerInfo.getCstResidence(), LENGTH_1000)) {
            customerInfo.setCstResidence(LengthValidationUtil.trimString(customerInfo.getCstResidence(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_EKYC_CHANNEL
        if (!LengthValidationUtil.isValidString(customerInfo.getCstEkycChannel(), LENGTH_1000)) {
            customerInfo.setCstEkycChannel(LengthValidationUtil.trimString(customerInfo.getCstEkycChannel(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_PRD_HOLDING
        if (!LengthValidationUtil.isValidString(customerInfo.getCstPrdHolding(), LENGTH_1000)) {
            customerInfo.setCstPrdHolding(LengthValidationUtil.trimString(customerInfo.getCstPrdHolding(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_PRD_ACTIVE
        if (!LengthValidationUtil.isValidString(customerInfo.getCstPrdActive(), LENGTH_1000)) {
            customerInfo.setCstPrdActive(LengthValidationUtil.trimString(customerInfo.getCstPrdActive(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        // CST_AEUPOINT
        if (!LengthValidationUtil.isValidString(customerInfo.getCstAeupoint(), LENGTH_1000)) {
            customerInfo.setCstAeupoint(LengthValidationUtil.trimString(customerInfo.getCstAeupoint(), LENGTH_1000));
            isValidMaxLengthProfile = false;
        }

        return isValidMaxLengthProfile;
    }

    private boolean isValidMaxLengthProducts(List<CustomerProfileData.ProductInfo> productInfo) {
        boolean isValidMaxLengthProducts = true;

        for (CustomerProfileData.ProductInfo product : productInfo) {
            // PRD_UPDATE_FLAG
            if (!LengthValidationUtil.isValidString(product.getPrdUpdateFlag(), LENGTH_1000)) {
                product.setPrdUpdateFlag(LengthValidationUtil.trimString(product.getPrdUpdateFlag(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // ARRANGEMENT_ID
            if (!LengthValidationUtil.isValidString(product.getArrangementId(), LENGTH_1000)) {
                product.setArrangementId(LengthValidationUtil.trimString(product.getArrangementId(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRODUCT_CODE
            if (!LengthValidationUtil.isValidString(product.getProductCode(), LENGTH_1000)) {
                product.setProductCode(LengthValidationUtil.trimString(product.getProductCode(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRODUCT_LV1
            if (!LengthValidationUtil.isValidString(product.getProductLv1(), LENGTH_1000)) {
                product.setProductLv1(LengthValidationUtil.trimString(product.getProductLv1(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRODUCT_LV2
            if (!LengthValidationUtil.isValidString(product.getProductLv2(), LENGTH_1000)) {
                product.setProductLv2(LengthValidationUtil.trimString(product.getProductLv2(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRODUCT_LV3
            if (!LengthValidationUtil.isValidString(product.getProductLv3(), LENGTH_1000)) {
                product.setProductLv3(LengthValidationUtil.trimString(product.getProductLv3(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRODUCT_LV4
            if (!LengthValidationUtil.isValidString(product.getProductLv4(), LENGTH_1000)) {
                product.setProductLv4(LengthValidationUtil.trimString(product.getProductLv4(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRODUCT_LV5
            if (!LengthValidationUtil.isValidString(product.getProductLv5(), LENGTH_1000)) {
                product.setProductLv5(LengthValidationUtil.trimString(product.getProductLv5(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRD_BRANCH_CODE
            if (!LengthValidationUtil.isValidString(product.getPrdBranchCode(), LENGTH_1000)) {
                product.setPrdBranchCode(LengthValidationUtil.trimString(product.getPrdBranchCode(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PROD_OPEN_DATE
            if (!LengthValidationUtil.isValidString(product.getProdOpenDate(), LENGTH_1000)) {
                product.setProdOpenDate(LengthValidationUtil.trimString(product.getProdOpenDate(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PROD_EXPIRE_DATE
            if (!LengthValidationUtil.isValidString(product.getProdExpireDate(), LENGTH_1000)) {
                product.setProdExpireDate(LengthValidationUtil.trimString(product.getProdExpireDate(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRD_BILL_CYCLE
            if (!LengthValidationUtil.isValidString(product.getPrdBillCycle(), LENGTH_1000)) {
                product.setPrdBillCycle(LengthValidationUtil.trimString(product.getPrdBillCycle(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRD_STATUS
            if (!LengthValidationUtil.isValidString(product.getPrdStatus(), LENGTH_1000)) {
                product.setPrdStatus(LengthValidationUtil.trimString(product.getPrdStatus(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRD_CCY_CODE
            if (!LengthValidationUtil.isValidString(product.getPrdCcyCode(), LENGTH_1000)) {
                product.setPrdCcyCode(LengthValidationUtil.trimString(product.getPrdCcyCode(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRD_TERM
            if (!LengthValidationUtil.isValidBigDecimal(product.getPrdTerm(), DIGITS_30, FRACTION_DIGITS_0)) {
                product.setPrdTerm(LengthValidationUtil.trimBigDecimal(product.getPrdTerm(), DIGITS_30, FRACTION_DIGITS_0));
                isValidMaxLengthProducts = false;
            }

            // PRD_TERM_UNIT
            if (!LengthValidationUtil.isValidString(product.getPrdTermUnit(), LENGTH_1000)) {
                product.setPrdTermUnit(LengthValidationUtil.trimString(product.getPrdTermUnit(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // EOP_BAL
            if (!LengthValidationUtil.isValidBigDecimal(product.getEopBal(), DIGITS_38, FRACTION_DIGITS_6)) {
                product.setEopBal(LengthValidationUtil.trimBigDecimal(product.getEopBal(), DIGITS_38, FRACTION_DIGITS_6));
                isValidMaxLengthProducts = false;
            }

            // PRD_ROLLOVER_DATE
            if (!LengthValidationUtil.isValidString(product.getPrdRolloverDate(), LENGTH_1000)) {
                product.setPrdRolloverDate(LengthValidationUtil.trimString(product.getPrdRolloverDate(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRD_HOLDING_NBR
            if (!LengthValidationUtil.isValidBigDecimal(product.getPrdHoldingNbr(), DIGITS_30, FRACTION_DIGITS_0)) {
                product.setPrdHoldingNbr(LengthValidationUtil.trimBigDecimal(product.getPrdHoldingNbr(), DIGITS_30, FRACTION_DIGITS_0));
                isValidMaxLengthProducts = false;
            }

            // PRODUCT_LV6
            if (!LengthValidationUtil.isValidString(product.getProductLv6(), LENGTH_1000)) {
                product.setProductLv6(LengthValidationUtil.trimString(product.getProductLv6(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRD_TENOR
            if (!LengthValidationUtil.isValidString(product.getPrdTenor(), LENGTH_1000)) {
                product.setPrdTenor(LengthValidationUtil.trimString(product.getPrdTenor(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRD_COMBO
            if (!LengthValidationUtil.isValidString(product.getPrdCombo(), LENGTH_1000)) {
                product.setPrdCombo(LengthValidationUtil.trimString(product.getPrdCombo(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRD_ACC_ARR_ID
            if (!LengthValidationUtil.isValidString(product.getPrdAccArrId(), LENGTH_1000)) {
                product.setPrdAccArrId(LengthValidationUtil.trimString(product.getPrdAccArrId(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRD_JOINT_HOLDER_IND
            if (!LengthValidationUtil.isValidString(product.getPrdJointHolderInd(), LENGTH_1000)) {
                product.setPrdJointHolderInd(LengthValidationUtil.trimString(product.getPrdJointHolderInd(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }

            // PRD_AUTO_BILL_IND
            if (!LengthValidationUtil.isValidString(product.getPrdAutoBillInd(), LENGTH_1000)) {
                product.setPrdAutoBillInd(LengthValidationUtil.trimString(product.getPrdAutoBillInd(), LENGTH_1000));
                isValidMaxLengthProducts = false;
            }
        }

        return isValidMaxLengthProducts;
    }

    private boolean isValidMaxLengthCards(List<CustomerProfileData.CardInfo> cardInfo) {
        boolean isValidMaxLengthCards = true;

        for (CustomerProfileData.CardInfo card : cardInfo) {
            // PRD_UPDATE_FLAG
            if (!LengthValidationUtil.isValidString(card.getPrdUpdateFlag(), LENGTH_1000)) {
                card.setPrdUpdateFlag(LengthValidationUtil.trimString(card.getPrdUpdateFlag(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // ARRANGEMENT_ID
            if (!LengthValidationUtil.isValidString(card.getArrangementId(), LENGTH_1000)) {
                card.setArrangementId(LengthValidationUtil.trimString(card.getArrangementId(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_ID
            if (!LengthValidationUtil.isValidString(card.getCardId(), LENGTH_1000)) {
                card.setCardId(LengthValidationUtil.trimString(card.getCardId(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_IDENTIFIER
            if (!LengthValidationUtil.isValidString(card.getCardIdentifier(), LENGTH_1000)) {
                card.setCardIdentifier(LengthValidationUtil.trimString(card.getCardIdentifier(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_IND
            if (!LengthValidationUtil.isValidString(card.getCardInd(), LENGTH_1000)) {
                card.setCardInd(LengthValidationUtil.trimString(card.getCardInd(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // PRODUCT_CODE
            if (!LengthValidationUtil.isValidString(card.getProductCode(), LENGTH_1000)) {
                card.setProductCode(LengthValidationUtil.trimString(card.getProductCode(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // PRODUCT_LV1
            if (!LengthValidationUtil.isValidString(card.getProductLv1(), LENGTH_1000)) {
                card.setProductLv1(LengthValidationUtil.trimString(card.getProductLv1(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // PRODUCT_LV2
            if (!LengthValidationUtil.isValidString(card.getProductLv2(), LENGTH_1000)) {
                card.setProductLv2(LengthValidationUtil.trimString(card.getProductLv2(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // PRODUCT_LV3
            if (!LengthValidationUtil.isValidString(card.getProductLv3(), LENGTH_1000)) {
                card.setProductLv3(LengthValidationUtil.trimString(card.getProductLv3(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // PRODUCT_LV4
            if (!LengthValidationUtil.isValidString(card.getProductLv4(), LENGTH_1000)) {
                card.setProductLv4(LengthValidationUtil.trimString(card.getProductLv4(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // PRODUCT_LV5
            if (!LengthValidationUtil.isValidString(card.getProductLv5(), LENGTH_1000)) {
                card.setProductLv5(LengthValidationUtil.trimString(card.getProductLv5(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_EXPIRE_DATE
            if (!LengthValidationUtil.isValidString(card.getCardExpireDate(), LENGTH_1000)) {
                card.setCardExpireDate(LengthValidationUtil.trimString(card.getCardExpireDate(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_STATUS
            if (!LengthValidationUtil.isValidString(card.getCardStatus(), LENGTH_1000)) {
                card.setCardStatus(LengthValidationUtil.trimString(card.getCardStatus(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_BLOCK_CODE
            if (!LengthValidationUtil.isValidString(card.getCardBlockCode(), LENGTH_1000)) {
                card.setCardBlockCode(LengthValidationUtil.trimString(card.getCardBlockCode(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_OPEN_DATE
            if (!LengthValidationUtil.isValidString(card.getCardOpenDate(), LENGTH_1000)) {
                card.setCardOpenDate(LengthValidationUtil.trimString(card.getCardOpenDate(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_ACTIVATED_DATE
            if (!LengthValidationUtil.isValidString(card.getCardActivatedDate(), LENGTH_1000)) {
                card.setCardActivatedDate(LengthValidationUtil.trimString(card.getCardActivatedDate(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_TERM
            if (!LengthValidationUtil.isValidBigDecimal(card.getCardTerm(), DIGITS_30, FRACTION_DIGITS_0)) {
                card.setCardTerm(LengthValidationUtil.trimBigDecimal(card.getCardTerm(), DIGITS_30, FRACTION_DIGITS_0));
                isValidMaxLengthCards = false;
            }

            // CARD_LIMIT
            if (!LengthValidationUtil.isValidBigDecimal(card.getCardLimit(), DIGITS_38, FRACTION_DIGITS_6)) {
                card.setCardLimit(LengthValidationUtil.trimBigDecimal(card.getCardLimit(), DIGITS_38, FRACTION_DIGITS_6));
                isValidMaxLengthCards = false;
            }

            // CARD_ANNUAL_FEE_PAYMENT
            if (!LengthValidationUtil.isValidString(card.getCardAnnualFeePayment(), LENGTH_1000)) {
                card.setCardAnnualFeePayment(LengthValidationUtil.trimString(card.getCardAnnualFeePayment(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_BILL_CYCLE_DATE
            if (!LengthValidationUtil.isValidString(card.getCardBillCycleDate(), LENGTH_1000)) {
                card.setCardBillCycleDate(LengthValidationUtil.trimString(card.getCardBillCycleDate(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_PAYMENT_DATE
            if (!LengthValidationUtil.isValidString(card.getCardPaymentDate(), LENGTH_1000)) {
                card.setCardPaymentDate(LengthValidationUtil.trimString(card.getCardPaymentDate(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // PRODUCT_LV6
            if (!LengthValidationUtil.isValidString(card.getProductLv6(), LENGTH_1000)) {
                card.setProductLv6(LengthValidationUtil.trimString(card.getProductLv6(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_PAN
            if (!LengthValidationUtil.isValidString(card.getCardPan(), LENGTH_1000)) {
                card.setCardPan(LengthValidationUtil.trimString(card.getCardPan(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_TYPE
            if (!LengthValidationUtil.isValidString(card.getCardType(), LENGTH_1000)) {
                card.setCardType(LengthValidationUtil.trimString(card.getCardType(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_ISSUE_TYPE
            if (!LengthValidationUtil.isValidString(card.getCardIssueType(), LENGTH_1000)) {
                card.setCardIssueType(LengthValidationUtil.trimString(card.getCardIssueType(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }

            // CARD_NEW_ACQUISITION_IND
            if (!LengthValidationUtil.isValidString(card.getCardNewAcquisitionInd(), LENGTH_1000)) {
                card.setCardNewAcquisitionInd(LengthValidationUtil.trimString(card.getCardNewAcquisitionInd(), LENGTH_1000));
                isValidMaxLengthCards = false;
            }
        }

        return isValidMaxLengthCards;
    }

    private ErrorCode verifyCustomerInfo(CustomerProfileData customerProfile) {
        CustomerProfileData.CustomerInfo customerInfo = customerProfile.getPayload().getCustomerInfo();

        if (Objects.isNull(customerInfo)) {
            return null;
        }

        if (StringUtils.isEmpty(customerInfo.getLoyaltyObDate())) {
            return ErrorCode.LOYALTY_OB_DATE_CANNOT_EMPTY;
        }

        if (!DateConverter.isValidFormat(customerInfo.getLoyaltyObDate(), "dd-MMM-yyyy")) {
            return ErrorCode.LOYALTY_OB_DATE_INVALID;
        }

        if (StringUtils.isEmpty(customerInfo.getCstGender())) {
            return ErrorCode.CST_GENDER_CANNOT_EMPTY;
        }

        if (!VALID_GENDERS.contains(customerInfo.getCstGender())) {
            return ErrorCode.CST_GENDER_INVALID;
        }

        if (!DateConverter.isValidFormat(customerInfo.getFrstCtcDt(), "dd-MMM-yyyy")) {
            return ErrorCode.FRST_CTC_DT_INVALID;
        }

        if (!DateConverter.isValidFormat(customerInfo.getCstBrthDt(), "dd-MMM-yyyy")) {
            return ErrorCode.CST_BRTH_DT_INVALID;
        }

        if (!StringUtils.isEmpty(customerInfo.getEmployeeInd()) &&
                Objects.isNull(EBoolean.of(customerInfo.getEmployeeInd()))) {
            return ErrorCode.EMPLOYEE_IND_INVALID;
        }

        if (!StringUtils.isEmpty(customerInfo.getCstMobileInd()) &&
                Objects.isNull(EBoolean.of(customerInfo.getCstMobileInd()))) {
            return ErrorCode.CST_MOBILE_IND_INVALID;
        }

        if (!DateConverter.isValidDate(customerInfo.getCstFirstLoginDate())) {
            return ErrorCode.CST_FIRST_LOGIN_DATE_INVALID;
        }

        if (!DateConverter.isValidDate(customerInfo.getCstLastLoginDate())) {
            return ErrorCode.CST_LAST_LOGIN_DATE_INVALID;
        }

        if (!StringUtils.isEmpty(customerInfo.getCstEkycStatus()) &&
                Objects.isNull(EBoolean.of(customerInfo.getCstEkycStatus()))) {
            return ErrorCode.CST_EKYC_STATUS_INVALID;
        }

        if (!StringUtils.isEmpty(customerInfo.getCstPrdHolding()) &&
                Objects.isNull(EBoolean.of(customerInfo.getCstPrdHolding()))) {
            return ErrorCode.CST_PRD_HOLDING_INVALID;
        }

        if (!StringUtils.isEmpty(customerInfo.getCstPrdActive()) &&
                Objects.isNull(EBoolean.of(customerInfo.getCstPrdActive()))) {
            return ErrorCode.CST_PRD_ACTIVE_INVALID;
        }

        return null;
    }

    private ErrorCode verifyProductInfo(CustomerProfileData customerProfile) {
        List<CustomerProfileData.ProductInfo> productInfoList = customerProfile.getPayload().getProductInfo();

        if (CollectionUtils.isEmpty(productInfoList)) {
            return null;
        }

        Set<String> arrangementIdSet = new HashSet<>();
        for (CustomerProfileData.ProductInfo productInfo : productInfoList) {
            if (StringUtils.isEmpty(productInfo.getArrangementId())) {
                return ErrorCode.ARRANGEMENT_ID_CANNOT_EMPTY;
            }

            if (StringUtils.isEmpty(productInfo.getProductCode())) {
                return ErrorCode.PRODUCT_CODE_CANNOT_EMPTY;
            }

            if (StringUtils.isEmpty(productInfo.getProductLv1())) {
                return ErrorCode.PRODUCT_LV1_CANNOT_EMPTY;
            }

            if (!DateConverter.isValidFormat(productInfo.getProdOpenDate(), "dd-MMM-yyyy")) {
                return ErrorCode.PROD_OPEN_DATE_INVALID;
            }

            if (!DateConverter.isValidFormat(productInfo.getProdExpireDate(), "dd-MMM-yyyy")) {
                return ErrorCode.PROD_EXPIRE_DATE_INVALID;
            }

            if (!DateConverter.isValidFormat(productInfo.getPrdRolloverDate(), "dd-MMM-yyyy")) {
                return ErrorCode.PRD_ROLLOVER_DATE_INVALID;
            }

            arrangementIdSet.add(productInfo.getArrangementId());
        }

        if (arrangementIdSet.size() < productInfoList.size()) {
            return ErrorCode.ARRANGEMENT_ID_DUPLICATED;
        }

        return null;
    }

    private ErrorCode verifyCardInfo(CustomerProfileData customerProfile) {
        List<CustomerProfileData.CardInfo> cardInfoList = customerProfile.getPayload().getCardInfo();

        if (CollectionUtils.isEmpty(cardInfoList)) {
            return null;
        }

        Set<String> cardIdSet = new HashSet<>();
        for (CustomerProfileData.CardInfo cardInfo : cardInfoList) {
            if (StringUtils.isEmpty(cardInfo.getCardId())) {
                return ErrorCode.CARD_ID_CANNOT_EMPTY;
            }

            if (StringUtils.isEmpty(cardInfo.getArrangementId())) {
                return ErrorCode.ARRANGEMENT_ID_CANNOT_EMPTY;
            }

            if (StringUtils.isEmpty(cardInfo.getCardInd())) {
                return ErrorCode.CARD_IND_CANNOT_EMPTY;
            }

            if (StringUtils.isEmpty(cardInfo.getProductCode())) {
                return ErrorCode.PRODUCT_CODE_CANNOT_EMPTY;
            }

            if (StringUtils.isEmpty(cardInfo.getProductLv1())) {
                return ErrorCode.PRODUCT_LV1_CANNOT_EMPTY;
            }

            if (StringUtils.isEmpty(cardInfo.getProductLv2())) {
                return ErrorCode.PRODUCT_LV2_CANNOT_EMPTY;
            }

            if (StringUtils.isEmpty(cardInfo.getProductLv3())) {
                return ErrorCode.PRODUCT_LV3_CANNOT_EMPTY;
            }

            if (StringUtils.isEmpty(cardInfo.getProductLv4())) {
                return ErrorCode.PRODUCT_LV4_CANNOT_EMPTY;
            }

            if (!DateConverter.isValidFormat(cardInfo.getCardExpireDate(), "dd-MMM-yyyy")) {
                return ErrorCode.CARD_EXPIRE_DATE_INVALID;
            }

            if (!DateConverter.isValidFormat(cardInfo.getCardOpenDate(), "dd-MMM-yyyy")) {
                return ErrorCode.CARD_OPEN_DATE_INVALID;
            }

            if (!DateConverter.isValidFormat(cardInfo.getCardActivatedDate(), "dd-MMM-yyyy")) {
                return ErrorCode.CARD_ACTIVATED_DATE_INVALID;
            }

            if (!DateConverter.isValidFormat(cardInfo.getCardBillCycleDate(), "dd-MMM-yyyy")) {
                return ErrorCode.CARD_BILL_CYCLE_DATE_INVALID;
            }

            if (!DateConverter.isValidFormat(cardInfo.getCardPaymentDate(), "dd-MMM-yyyy")) {
                return ErrorCode.CARD_PAYMENT_DATE_INVALID;
            }

            cardIdSet.add(cardInfo.getCardId());
        }

        if (cardIdSet.size() < cardInfoList.size()) {
            return ErrorCode.CARD_ID_DUPLICATED;
        }

        return null;
    }

    private List<AttributeUpdateReq> toAttributes(CustomerProfile customerProfile) {
        List<AttributeUpdateReq> attributes = new ArrayList<>();

        attributes.add(AttributeConverter.toAttribute(EAttributeType.DATE, "FRST_CTC_DT", customerProfile.getFrstCtcDt()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "TARGET_SEGMENT_CODE", customerProfile.getTargetSegmentCode()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_NATIONALITY", customerProfile.getCstNationality()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "EMPLOYEE_IND", customerProfile.getEmployeeInd()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_GENDER", customerProfile.getCstGender()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_MOBILE_IND", customerProfile.getCstMobileInd()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "BIZ_LINE", customerProfile.getBizLine()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.DATE, "CST_FIRST_LOGIN_DATE", customerProfile.getCstFirstLoginDate()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.DATE, "CST_LAST_LOGIN_DATE", customerProfile.getCstLastLoginDate()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_AST_CLSS", customerProfile.getCstAstClss()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_BANK_SEGMENT", customerProfile.getCstBankSegment()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_PBT", nullSaferValueOf(customerProfile.getCstPbt())));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.NUMBER, "CST_TOI", nullSaferValueOf(customerProfile.getCstToi())));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.NUMBER, "CST_COST", nullSaferValueOf(customerProfile.getCstCost())));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_FAMILY_GROUP", customerProfile.getCstFamilyGroup()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_FAMILY_MEMBER_TYPE", customerProfile.getCstFamilyMemberType()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_OCCUPATION", customerProfile.getCstOccupation()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_EKYC_STATUS", customerProfile.getCstEkycStatus()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_HOME_BRANCH", customerProfile.getCstHomeBranch()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_SUB_BRANCH", customerProfile.getCstSubBranch()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_RM_BRANCH", customerProfile.getCstRmBranch()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_MERCHANT_IND", customerProfile.getCstMerchantInd()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_NOTI_CHANNEL", customerProfile.getCstNotiChannel()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_BLK_LV", customerProfile.getCstBlkLv()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_WTCH_LIST", customerProfile.getCstWtchList()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_TIER_PROGRAM", customerProfile.getCstTierProgram()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_RESIDENCE", customerProfile.getCstResidence()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_EKYC_CHANNEL", customerProfile.getCstEkycChannel()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_PRD_HOLDING", customerProfile.getCstPrdHolding()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_PRD_ACTIVE", customerProfile.getCstPrdActive()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.DATE, "LOYALTY_OB_DATE", customerProfile.getLoyaltyObDate()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.DATE, "TIER_START_DATE", customerProfile.getTierStartDate()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.DATE, "TIER_END_DATE", customerProfile.getTierEndDate()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_PRE_TIER", customerProfile.getCstPreTier()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_TIER", customerProfile.getCstTier()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_LOYALTY_TIER", customerProfile.getCstLoyaltyTier()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.DATE, "CST_BRTH_DT", customerProfile.getCstBrthDt()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CST_AEUPOINT", customerProfile.getCstAeupoint()));

        return attributes;
    }

    private List<AttributeUpdateReq> toAttributes(CustomerProductInfo customerProductInfo) {
        List<AttributeUpdateReq> attributes = new ArrayList<>();

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "PRODUCT_CODE", customerProductInfo.getProductCode()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "PRODUCT_LV1", customerProductInfo.getProductLv1()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "PRODUCT_LV2", customerProductInfo.getProductLv2()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "PRODUCT_LV3", customerProductInfo.getProductLv3()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "PRODUCT_LV4", customerProductInfo.getProductLv4()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "PRODUCT_LV5", customerProductInfo.getProductLv5()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "PRODUCT_LV6", customerProductInfo.getProductLv6()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "PRD_BRANCH_CODE", customerProductInfo.getPrdBranchCode()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "PRD_STATUS", customerProductInfo.getPrdStatus()));

        return attributes;
    }

    private List<AttributeUpdateReq> toAttributes(CustomerCardInfo customerCardInfo) {
        List<AttributeUpdateReq> attributes = new ArrayList<>();

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CARD_IDENTIFIER", customerCardInfo.getCardIdentifier()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CARD_IND", customerCardInfo.getCardInd()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CARD_PAN", customerCardInfo.getCardPan()));

        attributes.add(AttributeConverter.toAttribute(EAttributeType.TEXT, "CARD_STATUS", customerCardInfo.getCardStatus()));

        return attributes;
    }

    private List<AttributeUpdateReq> toAttributes(CustomerAttributeInfo customerAttributeInfo) {
        List<AttributeUpdateReq> attributes = new ArrayList<>();

        attributes.add(AttributeConverter
                .toAttribute(EAttributeType.TEXT, customerAttributeInfo.getCode(), customerAttributeInfo.getValue(),
                        customerAttributeInfo.getStatus(), customerAttributeInfo.getStartDate(), customerAttributeInfo.getEndDate()));

        return attributes;
    }

    private CustomerProfile toCustomerProfile(
            CustomerProfile customerProfile,
            CustomerProfileData.CustomerInfo customerInfo,
            String loyaltyCusId
    ) {
        if (Objects.isNull(customerProfile)) {
            customerProfile = new CustomerProfile();
            customerProfile.setCreatedAt(new Date());
        }

        customerProfile.setBusinessCode(businessId);
        customerProfile.setProgramCode(programId);
        customerProfile.setLoyaltyCusId(loyaltyCusId);
        customerProfile.setPrdUpdateFlag(customerInfo.getPrdUpdateFlag());
        customerProfile.setLoyaltyObDate(customerInfo.getLoyaltyObDate());
        customerProfile.setFrstCtcDt(customerInfo.getFrstCtcDt());
        customerProfile.setCstBrthDt(customerInfo.getCstBrthDt());
        customerProfile.setTierStartDate(customerInfo.getTierStartDate());
        customerProfile.setTierEndDate(customerInfo.getTierEndDate());
        customerProfile.setCstPreTier(customerInfo.getCstPreTier());
        customerProfile.setCstTier(customerInfo.getCstTier());
        customerProfile.setCstLoyaltyTier(customerInfo.getCstLoyaltyTier());
        customerProfile.setTargetSegmentCode(customerInfo.getTargetSegmentCode());
        customerProfile.setCstNationality(customerInfo.getCstNationality());
        customerProfile.setEmployeeInd(customerInfo.getEmployeeInd());
        customerProfile.setCstGender(customerInfo.getCstGender());
        customerProfile.setCstMobileInd(customerInfo.getCstMobileInd());
        customerProfile.setBizLine(customerInfo.getBizLine());
        customerProfile.setCstFirstLoginDate(customerInfo.getCstFirstLoginDate());
        customerProfile.setCstLastLoginDate(customerInfo.getCstLastLoginDate());
        customerProfile.setCstDebtGroup(customerInfo.getCstDebtGroup());
        customerProfile.setCstAstClss(customerInfo.getCstAstClss());
        customerProfile.setCstStaffInd(customerInfo.getCstStaffInd());
        customerProfile.setCstBankSegment(customerInfo.getCstBankSegment());
        customerProfile.setCstType(customerInfo.getCstType());
        customerProfile.setCstPbt(customerInfo.getCstPbt());
        customerProfile.setCstToi(customerInfo.getCstToi());
        customerProfile.setCstCost(customerInfo.getCstCost());
        customerProfile.setCstFamilyGroup(customerInfo.getCstFamilyGroup());
        customerProfile.setCstFamilyMemberType(customerInfo.getCstFamilyMemberType());
        customerProfile.setCstOccupation(customerInfo.getCstOccupation());
        customerProfile.setCstEkycStatus(customerInfo.getCstEkycStatus());
        customerProfile.setCstHomeBranch(customerInfo.getCstHomeBranch());
        customerProfile.setCstSubBranch(customerInfo.getCstSubBranch());
        customerProfile.setCstRmBranch(customerInfo.getCstRmBranch());
        customerProfile.setCstMerchantInd(customerInfo.getCstMerchantInd());
        customerProfile.setCstNotiChannel(customerInfo.getCstNotiChannel());
        customerProfile.setCstBlkLv(customerInfo.getCstBlkLv());
        customerProfile.setCstWtchList(customerInfo.getCstWtchList());
        customerProfile.setCstTierProgram(customerInfo.getCstTierProgram());
        customerProfile.setCstResidence(customerInfo.getCstResidence());
        customerProfile.setCstEkycChannel(customerInfo.getCstEkycChannel());
        customerProfile.setCstPrdHolding(customerInfo.getCstPrdHolding());
        customerProfile.setCstPrdActive(customerInfo.getCstPrdActive());
        customerProfile.setUpdatedAt(new Date());
        customerProfile.setCstAeupoint(customerInfo.getCstAeupoint());

        return customerProfile;
    }

    private MemberServiceProfileUpdateReq toUpdateMemberProfileReq(CustomerProfileData customerProfile) {
        CustomerProfileData.Payload payload = customerProfile.getPayload();
        CustomerProfileData.CustomerInfo customerInfo = customerProfile.getPayload().getCustomerInfo();

        MemberServiceProfileUpdateReq req = new MemberServiceProfileUpdateReq();
        req.setBusinessId(businessId);
        req.setProgramId(programId);
        req.setCustomerIdentifier(toCustomerIdentify(payload.getLoyaltyCusId()));

        if (!StringUtils.isEmpty(customerInfo.getCstBrthDt())) {
            req.setDob(DateConverter.toYearMonthDay(customerProfile.getPayload().getCustomerInfo().getCstBrthDt()));
        }

        if (StringUtils.isNotEmpty(customerInfo.getCstGender())) {
            req.setGender(customerInfo.getCstGender());
        }

        if (!StringUtils.isEmpty(customerInfo.getCstTier())) {
            req.setTierCode(customerInfo.getCstTier());
        }

        if (!StringUtils.isEmpty(customerInfo.getTierEndDate())) {
            req.setTierExpiredDate(DateConverter.toEpochSecond(customerInfo.getTierEndDate()));
        }

        if (!StringUtils.isEmpty(customerInfo.getLoyaltyObDate())) {
            req.setRegistrationDate(DateConverter.toDate(customerInfo.getLoyaltyObDate()));
        }

        if (!StringUtils.isEmpty(customerInfo.getTierStartDate())) {
            req.setTierStartDate(DateConverter.toEpochSecond(customerInfo.getTierStartDate()));
        }

        if (!StringUtils.isEmpty(customerInfo.getCstTierProgram())) {
            req.setTierProgram(customerInfo.getCstTierProgram());
        }

        return req;
    }

    private MemberAttributeUpdateReq toUpdateAttributesReq(CustomerProfileData customerProfile, List<AttributeUpdateReq> attributes) {
        MemberAttributeUpdateReq req = new MemberAttributeUpdateReq();
        req.setBusinessCode(businessId);
        req.setProgramCode(programId);
        req.setCustomerIdentifier(toCustomerIdentify(customerProfile.getPayload().getLoyaltyCusId()));
        req.setAttributes(attributes);
        return req;
    }

    private CustomerIdentify toCustomerIdentify(String idNo) {
        CustomerIdentify identify = new CustomerIdentify();
        identify.setId(idNo);
        identify.setIdType(EIdType.PARTNER_CUSTOMER_ID);
        return identify;
    }

    private CustomerProductInfo toCustomerProductInfo(CustomerProductInfo customerProductInfo, CustomerProfileData.ProductInfo productInfo, String loyaltyCusId) {
        if (Objects.isNull(customerProductInfo)) {
            customerProductInfo = new CustomerProductInfo();
            customerProductInfo.setCreatedAt(new Date());
        }

        customerProductInfo.setBusinessCode(businessId);
        customerProductInfo.setProgramCode(programId);
        customerProductInfo.setLoyaltyCusId(loyaltyCusId);
        customerProductInfo.setPrdUpdateFlag(productInfo.getPrdUpdateFlag());
        customerProductInfo.setArrangementId(productInfo.getArrangementId());
        customerProductInfo.setProductCode(productInfo.getProductCode());
        customerProductInfo.setProductLv1(productInfo.getProductLv1());
        customerProductInfo.setProductLv2(productInfo.getProductLv2());
        customerProductInfo.setProductLv3(productInfo.getProductLv3());
        customerProductInfo.setProductLv4(productInfo.getProductLv4());
        customerProductInfo.setProductLv5(productInfo.getProductLv5());
        customerProductInfo.setProductLv6(productInfo.getProductLv6());
        customerProductInfo.setPrdBranchCode(productInfo.getPrdBranchCode());
        customerProductInfo.setProdOpenDate(productInfo.getProdOpenDate());
        customerProductInfo.setProdExpireDate(productInfo.getProdExpireDate());
        customerProductInfo.setPrdBillCycle(productInfo.getPrdBillCycle());
        customerProductInfo.setPrdStatus(productInfo.getPrdStatus());
        customerProductInfo.setPrdCcyCode(productInfo.getPrdCcyCode());
        customerProductInfo.setPrdTerm(productInfo.getPrdTerm());
        customerProductInfo.setPrdTermUnit(productInfo.getPrdTermUnit());
        customerProductInfo.setEopBal(productInfo.getEopBal());
        customerProductInfo.setPrdRolloverDate(productInfo.getPrdRolloverDate());
        customerProductInfo.setPrdHoldingNbr(productInfo.getPrdHoldingNbr());
        customerProductInfo.setPrdTenor(productInfo.getPrdTenor());
        customerProductInfo.setPrdCombo(productInfo.getPrdCombo());
        customerProductInfo.setPrdAccArrId(productInfo.getPrdAccArrId());
        customerProductInfo.setPrdJointHolderInd(productInfo.getPrdJointHolderInd());
        customerProductInfo.setPrdAutoBillInd(productInfo.getPrdAutoBillInd());
        customerProductInfo.setUpdatedAt(new Date());
        customerProductInfo.setPrdSalesChannel(productInfo.getPrdSalesChannel());
        customerProductInfo.setPrdPaymentDate(productInfo.getPrdPaymentDate());
        customerProductInfo.setPrdIssueDate(productInfo.getPrdIssueDate());
        customerProductInfo.setPrdInsuranceRenew(productInfo.getPrdInsuranceRenew());
        customerProductInfo.setPrdRoll(productInfo.getPrdRoll());
        customerProductInfo.setPrdAutoRollInd(productInfo.getPrdAutoRollInd());
        customerProductInfo.setPrdTdAmount(productInfo.getPrdTdAmount());

        return customerProductInfo;
    }

    private CustomerCardInfo toCustomerCardInfo(CustomerCardInfo customerCardInfo, CustomerProfileData.CardInfo cardInfo, String loyaltyCusId) {
        if (Objects.isNull(customerCardInfo)) {
            customerCardInfo = new CustomerCardInfo();
            customerCardInfo.setCreatedAt(new Date());
        }

        customerCardInfo.setBusinessCode(businessId);
        customerCardInfo.setProgramCode(programId);
        customerCardInfo.setLoyaltyCusId(loyaltyCusId);
        customerCardInfo.setPrdUpdateFlag(cardInfo.getPrdUpdateFlag());
        customerCardInfo.setArrangementId(cardInfo.getArrangementId());
        customerCardInfo.setCardId(cardInfo.getCardId());
        customerCardInfo.setCardIdentifier(cardInfo.getCardIdentifier());
        customerCardInfo.setCardInd(cardInfo.getCardInd());
        customerCardInfo.setProductCode(cardInfo.getProductCode());
        customerCardInfo.setProductLv1(cardInfo.getProductLv1());
        customerCardInfo.setProductLv2(cardInfo.getProductLv2());
        customerCardInfo.setProductLv3(cardInfo.getProductLv3());
        customerCardInfo.setProductLv4(cardInfo.getProductLv4());
        customerCardInfo.setProductLv5(cardInfo.getProductLv5());
        customerCardInfo.setProductLv6(cardInfo.getProductLv6());
        customerCardInfo.setCardExpireDate(cardInfo.getCardExpireDate());
        customerCardInfo.setCardStatus(cardInfo.getCardStatus());
        customerCardInfo.setCardBlockCode(cardInfo.getCardBlockCode());
        customerCardInfo.setCardOpenDate(cardInfo.getCardOpenDate());
        customerCardInfo.setCardActivatedDate(cardInfo.getCardActivatedDate());
        customerCardInfo.setCardTerm(cardInfo.getCardTerm());
        customerCardInfo.setCardLimit(cardInfo.getCardLimit());
        customerCardInfo.setCardAnnualFeePayment(cardInfo.getCardAnnualFeePayment());
        customerCardInfo.setCardBillCycleDate(cardInfo.getCardBillCycleDate());
        customerCardInfo.setCardPaymentDate(cardInfo.getCardPaymentDate());
        customerCardInfo.setCardPan(cardInfo.getCardPan());
        customerCardInfo.setCardType(cardInfo.getCardType());
        customerCardInfo.setCardIssueType(cardInfo.getCardIssueType());
        customerCardInfo.setCardNewAcquisitionInd(cardInfo.getCardNewAcquisitionInd());
        customerCardInfo.setUpdatedAt(new Date());

        return customerCardInfo;
    }

    private CustomerAttributeInfo toCustomerAttributeInfo(
            CustomerAttributeInfo customerAttributeInfo,
            CustomerProfileData.AttributeInfo attributeInfo,
            String loyaltyCusId,
            String code
    ) {
        if (Objects.isNull(customerAttributeInfo)) {
            customerAttributeInfo = new CustomerAttributeInfo();
            customerAttributeInfo.setCreatedAt(new Date());
        } else {
            if (ECommonStatus.INACTIVE.getValue().equals(attributeInfo.getStatus())
                    && ECommonStatus.INACTIVE.getValue().equals(customerAttributeInfo.getStatus())) {
                return customerAttributeInfo;
            }
        }

        customerAttributeInfo.setBusinessCode(businessId);
        customerAttributeInfo.setProgramCode(programId);
        customerAttributeInfo.setLoyaltyCusId(loyaltyCusId);
        customerAttributeInfo.setCode(code);
        customerAttributeInfo.setValue(attributeInfo.getValue());
        customerAttributeInfo.setStartDate(attributeInfo.getStartDate());
        customerAttributeInfo.setEndDate(attributeInfo.getEndDate());
        customerAttributeInfo.setStatus(attributeInfo.getStatus());
        customerAttributeInfo.setUpdatedAt(new Date());

        return customerAttributeInfo;
    }

    private void insertAuditTrail(CustomerProfileData customerProfile,
                                  ProcessData<?> processData,
                                  String requestId
    ) {
        try {
            // Case Retry
            if (Objects.nonNull(customerProfile.getAuditId())) {
                KafkaAuditTrailCustomerProfile auditTrail =
                        auditCustomerProfileRepository.findById(customerProfile.getAuditId())
                                .orElse(null);

                if (Objects.nonNull(auditTrail)) {
                    auditTrail.setUpdatedAt(new Date());

                    if (Objects.nonNull(customerProfile.getErrorCode())) {
                        auditTrail.setErrorCode(customerProfile.getErrorCode().getCode());
                        auditTrail.setErrorMessage(customerProfile.getErrorCode().getMessage());
                    }

                    if (Objects.nonNull(customerProfile.getServiceErrorCode())) {
                        auditTrail.setServiceErrorCode(String.valueOf(customerProfile.getServiceErrorCode()));
                    }

                    if (Objects.nonNull(customerProfile.getServiceErrorMessage())) {
                        auditTrail.setServiceErrorMessage(customerProfile.getServiceErrorMessage());
                    }

                    auditCustomerProfileRepository.save(auditTrail);
                    return;
                }
            }

            // Case Create
            KafkaAuditTrailCustomerProfile auditTrail = createAuditCustomerInfo(customerProfile, processData, requestId);
            auditCustomerProfileRepository.save(auditTrail);

            createAuditProductInfo(customerProfile, auditTrail.getId());

            createAuditCardInfo(customerProfile, auditTrail.getId());

            createAuditCstTargetGroupInfo(customerProfile, auditTrail.getId());

            Log.info(LogData.createLogData()
                    .append("msg", "CustomerProfileHandler -- Insert audit trail was successful.")
                    .append("request_id", requestId)
            );
        } catch (Exception ex) {
            Log.error(LogData.createLogData()
                    .append("msg", "CustomerProfileHandler -- Exception while inserting audit trail.")
                    .append("request_id", requestId)
                    .append("error", ex.getMessage())
            );
        }
    }

    private KafkaAuditTrailCustomerProfile createAuditCustomerInfo(CustomerProfileData customerProfile,
                                                                   ProcessData<?> processData,
                                                                   String requestId) {
        KafkaAuditTrailCustomerProfile auditTrail = new KafkaAuditTrailCustomerProfile();
        CustomerProfileData.Payload payload = customerProfile.getPayload();

        auditTrail.setBusinessCode(businessId);
        auditTrail.setProgramCode(programId);
        auditTrail.setMessageId(requestId);
        auditTrail.setMessageType(MessageType.CUSTOMER_PROFILE.getValue());
        auditTrail.setCreatedAt(new Date());
        auditTrail.setUpdatedAt(new Date());
        auditTrail.setDsPartitionDate(customerProfile.getDsPartitionDate());
        auditTrail.setFileName(processData.getFileName());

        if (Objects.nonNull(customerProfile.getErrorCode())) {
            auditTrail.setErrorCode(customerProfile.getErrorCode().getCode());
            auditTrail.setErrorMessage(customerProfile.getErrorCode().getMessage());
        }

        if (Objects.nonNull(customerProfile.getServiceErrorCode())) {
            auditTrail.setServiceErrorCode(String.valueOf(customerProfile.getServiceErrorCode()));
        }

        if (Objects.nonNull(customerProfile.getServiceErrorMessage())) {
            auditTrail.setServiceErrorMessage(customerProfile.getServiceErrorMessage());
        }

        if (Objects.nonNull(payload)) {
            auditTrail.setLoyaltyCusId(payload.getLoyaltyCusId());
            CustomerProfileData.CustomerInfo customerInfo = payload.getCustomerInfo();
            if (Objects.nonNull(customerInfo)) {
                auditTrail.setPrdUpdateFlag(customerInfo.getPrdUpdateFlag());
                auditTrail.setLoyaltyObDate(customerInfo.getLoyaltyObDate());
                auditTrail.setFrstCtcDt(customerInfo.getFrstCtcDt());
                auditTrail.setCstBrthDt(customerInfo.getCstBrthDt());
                auditTrail.setTierStartDate(customerInfo.getTierStartDate());
                auditTrail.setTierEndDate(customerInfo.getTierEndDate());
                auditTrail.setCstPreTier(customerInfo.getCstPreTier());
                auditTrail.setCstTier(customerInfo.getCstTier());
                auditTrail.setCstLoyaltyTier(customerInfo.getCstLoyaltyTier());
                auditTrail.setTargetSegmentCode(customerInfo.getTargetSegmentCode());
                auditTrail.setCstNationality(customerInfo.getCstNationality());
                auditTrail.setEmployeeInd(customerInfo.getEmployeeInd());
                auditTrail.setCstMobileInd(customerInfo.getCstMobileInd());
                auditTrail.setBizLine(customerInfo.getBizLine());
                auditTrail.setCstFirstLoginDate(customerInfo.getCstFirstLoginDate());
                auditTrail.setCstLastLoginDate(customerInfo.getCstLastLoginDate());
                auditTrail.setCstDebtGroup(customerInfo.getCstDebtGroup());
                auditTrail.setCstAstClss(customerInfo.getCstAstClss());
                auditTrail.setCstStaffInd(customerInfo.getCstStaffInd());
                auditTrail.setCstBankSegment(customerInfo.getCstBankSegment());
                auditTrail.setCstType(customerInfo.getCstType());
                auditTrail.setCstPbt(customerInfo.getCstPbt());
                auditTrail.setCstToi(customerInfo.getCstToi());
                auditTrail.setCstCost(customerInfo.getCstCost());
                auditTrail.setCstFamilyGroup(customerInfo.getCstFamilyGroup());
                auditTrail.setCstFamilyMemberType(customerInfo.getCstFamilyMemberType());
                auditTrail.setCstOccupation(customerInfo.getCstOccupation());
                auditTrail.setCstEkycStatus(customerInfo.getCstEkycStatus());
                auditTrail.setCstHomeBranch(customerInfo.getCstHomeBranch());
                auditTrail.setCstSubBranch(customerInfo.getCstSubBranch());
                auditTrail.setCstRmBranch(customerInfo.getCstRmBranch());
                auditTrail.setCstMerchantInd(customerInfo.getCstMerchantInd());
                auditTrail.setCstNotiChannel(customerInfo.getCstNotiChannel());
                auditTrail.setCstBlkLv(customerInfo.getCstBlkLv());
                auditTrail.setCstWtchList(customerInfo.getCstWtchList());
                auditTrail.setCstTierProgram(customerInfo.getCstTierProgram());
                auditTrail.setCstResidence(customerInfo.getCstResidence());
                auditTrail.setCstGender(customerInfo.getCstGender());
                auditTrail.setCstEkycChannel(customerInfo.getCstEkycChannel());
                auditTrail.setCstPrdHolding(customerInfo.getCstPrdHolding());
                auditTrail.setCstPrdActive(customerInfo.getCstPrdActive());
                auditTrail.setCstAeupoint(customerInfo.getCstAeupoint());
            }
        }
        return auditTrail;
    }

    private void createAuditProductInfo(CustomerProfileData customerProfile, Long auditId) {
        CustomerProfileData.Payload payload = customerProfile.getPayload();
        List<KafkaAuditTrailProductInfo> auditProducts = new ArrayList<>();
        if (Objects.nonNull(payload) && CollectionUtils.isNotEmpty(payload.getProductInfo())) {
            for (CustomerProfileData.ProductInfo productInfo : customerProfile.getPayload().getProductInfo()) {
                KafkaAuditTrailProductInfo auditProduct = new KafkaAuditTrailProductInfo();
                auditProduct.setAuditId(auditId);
                auditProduct.setPrdUpdateFlag(productInfo.getPrdUpdateFlag());
                auditProduct.setArrangementId(productInfo.getArrangementId());
                auditProduct.setProductCode(productInfo.getProductCode());
                auditProduct.setProductLv1(productInfo.getProductLv1());
                auditProduct.setProductLv2(productInfo.getProductLv2());
                auditProduct.setProductLv3(productInfo.getProductLv3());
                auditProduct.setProductLv4(productInfo.getProductLv4());
                auditProduct.setProductLv5(productInfo.getProductLv5());
                auditProduct.setProductLv6(productInfo.getProductLv6());
                auditProduct.setPrdBranchCode(productInfo.getPrdBranchCode());
                auditProduct.setProdOpenDate(productInfo.getProdOpenDate());
                auditProduct.setProdExpireDate(productInfo.getProdExpireDate());
                auditProduct.setPrdBillCycle(productInfo.getPrdBillCycle());
                auditProduct.setPrdStatus(productInfo.getPrdStatus());
                auditProduct.setPrdCcyCode(productInfo.getPrdCcyCode());
                auditProduct.setPrdTerm(productInfo.getPrdTerm());
                auditProduct.setPrdTermUnit(productInfo.getPrdTermUnit());
                auditProduct.setEopBal(productInfo.getEopBal());
                auditProduct.setPrdRolloverDate(productInfo.getPrdRolloverDate());
                auditProduct.setPrdHoldingNbr(productInfo.getPrdHoldingNbr());
                auditProduct.setPrdTenor(productInfo.getPrdTenor());
                auditProduct.setPrdCombo(productInfo.getPrdCombo());
                auditProduct.setPrdAccArrId(productInfo.getPrdAccArrId());
                auditProduct.setPrdJointHolderInd(productInfo.getPrdJointHolderInd());
                auditProduct.setPrdAutoBillInd(productInfo.getPrdAutoBillInd());
                auditProduct.setCreatedAt(new Date());
                auditProduct.setPrdSalesChannel(productInfo.getPrdSalesChannel());
                auditProduct.setPrdPaymentDate(productInfo.getPrdPaymentDate());
                auditProduct.setPrdIssueDate(productInfo.getPrdIssueDate());
                auditProduct.setPrdInsuranceRenew(productInfo.getPrdInsuranceRenew());
                auditProduct.setPrdAutoRollInd(productInfo.getPrdAutoRollInd());
                auditProduct.setPrdTdAmount(productInfo.getPrdTdAmount());
                auditProduct.setPrdRoll(productInfo.getPrdRoll());

                auditProducts.add(auditProduct);
            }
            auditProductInfoRepository.saveAll(auditProducts);
        }
    }

    private void createAuditCardInfo(CustomerProfileData customerProfile, Long auditId) {
        CustomerProfileData.Payload payload = customerProfile.getPayload();
        List<KafkaAuditTrailCardInfo> auditCards = new ArrayList<>();
        if (Objects.nonNull(payload) && CollectionUtils.isNotEmpty(payload.getCardInfo())) {
            for (CustomerProfileData.CardInfo cardInfo : payload.getCardInfo()) {
                KafkaAuditTrailCardInfo auditCard = new KafkaAuditTrailCardInfo();
                auditCard.setAuditId(auditId);
                auditCard.setPrdUpdateFlag(cardInfo.getPrdUpdateFlag());
                auditCard.setArrangementId(cardInfo.getArrangementId());
                auditCard.setCardId(cardInfo.getCardId());
                auditCard.setCardIdentifier(cardInfo.getCardIdentifier());
                auditCard.setCardInd(cardInfo.getCardInd());
                auditCard.setProductCode(cardInfo.getProductCode());
                auditCard.setProductLv1(cardInfo.getProductLv1());
                auditCard.setProductLv2(cardInfo.getProductLv2());
                auditCard.setProductLv3(cardInfo.getProductLv3());
                auditCard.setProductLv4(cardInfo.getProductLv4());
                auditCard.setProductLv5(cardInfo.getProductLv5());
                auditCard.setProductLv6(cardInfo.getProductLv6());
                auditCard.setCardExpireDate(cardInfo.getCardExpireDate());
                auditCard.setCardStatus(cardInfo.getCardStatus());
                auditCard.setCardBlockCode(cardInfo.getCardBlockCode());
                auditCard.setCardOpenDate(cardInfo.getCardOpenDate());
                auditCard.setCardActivatedDate(cardInfo.getCardActivatedDate());
                auditCard.setCardTerm(cardInfo.getCardTerm());
                auditCard.setCardLimit(cardInfo.getCardLimit());
                auditCard.setCardAnnualFeePayment(cardInfo.getCardAnnualFeePayment());
                auditCard.setCardBillCycleDate(cardInfo.getCardBillCycleDate());
                auditCard.setCardPaymentDate(cardInfo.getCardPaymentDate());
                auditCard.setCardPan(cardInfo.getCardPan());
                auditCard.setCardType(cardInfo.getCardType());
                auditCard.setCardIssueType(cardInfo.getCardIssueType());
                auditCard.setCardNewAcquisitionInd(cardInfo.getCardNewAcquisitionInd());
                auditCard.setCreatedAt(new Date());
                auditCards.add(auditCard);
                auditCardInfoRepository.saveAll(auditCards);
            }
        }
    }

    private void createAuditCstTargetGroupInfo(CustomerProfileData customerProfile, Long auditId) {
        CustomerProfileData.Payload payload = customerProfile.getPayload();
        List<AttributeInfoAuditTrail> auditCstTargetGroups = new ArrayList<>();
        if (Objects.nonNull(payload) && CollectionUtils.isNotEmpty(payload.getCstTargetGroup())) {
            for (CustomerProfileData.AttributeInfo cstTargetGroupInfo : payload.getCstTargetGroup()) {
                AttributeInfoAuditTrail auditCstTargetGroup = new AttributeInfoAuditTrail();
                auditCstTargetGroup.setAuditId(auditId);
                auditCstTargetGroup.setCode(CST_TARGET_GROUP);
                auditCstTargetGroup.setValue(cstTargetGroupInfo.getValue());
                auditCstTargetGroup.setStartDate(cstTargetGroupInfo.getStartDate());
                auditCstTargetGroup.setEndDate(cstTargetGroupInfo.getEndDate());
                auditCstTargetGroup.setStatus(cstTargetGroupInfo.getStatus());
                auditCstTargetGroup.setCreatedAt(new Date());
                auditCstTargetGroups.add(auditCstTargetGroup);
                attributeInfoAuditTrailRepository.saveAll(auditCstTargetGroups);
            }
        }
    }

    private String nullSaferValueOf(BigDecimal bigDecimal) {
        return Objects.nonNull(bigDecimal) ? bigDecimal.toString() : null;
    }

    private void produceMessage(CustomerProfileData cusProfileData, String requestId, String processId) {
        try {
            CustomerProfileEventProduce customerProfileEventProduce = new CustomerProfileEventProduce();

            customerProfileEventProduce.setMessageId(requestId);
            customerProfileEventProduce.setMessageRefId(processId);
            customerProfileEventProduce.setTimestamp(System.currentTimeMillis());
            customerProfileEventProduce.setMessageType(MESSAGE_TYPE);
            customerProfileEventProduce.setPayload(cusProfileData.getPayload());

            if (cusProfileData.getErrorCode() != null) {
                customerProfileEventProduce.setErrorCode(cusProfileData.getErrorCode().getCode());
                customerProfileEventProduce.setErrorMessage(cusProfileData.getErrorCode().getMessage());
            }

            ProduceService.ProduceMessage produceMessage = new ProduceService.ProduceMessage();
            produceMessage.setKey(requestId);
            produceMessage.setValue(customerProfileEventProduce);

            produceService.produceAsync(produceMessage, customerProfileProduceTopic, "");

            Log.info(LogData.createLogData()
                    .append("msg", "CustomerProfileHandler -- Produce response was successful")
                    .append("payload", customerProfileEventProduce)
            );
        } catch (Exception ex) {
            Log.error(LogData.createLogData()
                    .append("msg", "CustomerProfileHandler -- Exception while producing response")
                    .append("request_id", requestId)
                    .append("err", ex.getMessage())
            );
            ex.printStackTrace();
        }
    }

    private ErrorCode verifyTargetGroup(CustomerProfileData customerProfile) {
        List<CustomerProfileData.AttributeInfo> cstTargetGroup = customerProfile.getPayload().getCstTargetGroup();

        if (CollectionUtils.isEmpty(cstTargetGroup)) {
            return null;
        }

        for (CustomerProfileData.AttributeInfo attributeInfo : cstTargetGroup) {
            Long startDate = null;
            Long endDate = null;
            if (!StringUtils.isEmpty(attributeInfo.getStartDate())) {
                startDate = DateConverter.toEpochSecond(attributeInfo.getStartDate(), FORMATTER);
                if (startDate == null) {
                    return ErrorCode.START_DATE_INVALID;
                }
            }

            if (!StringUtils.isEmpty(attributeInfo.getEndDate())) {
                endDate = DateConverter.toEpochSecond(attributeInfo.getEndDate(), FORMATTER);
                if (endDate == null) {
                    return ErrorCode.END_DATE_INVALID;
                }
                if (startDate != null && startDate.compareTo(endDate) > 0) {
                    return ErrorCode.START_DATE_MUST_BE_LESS_THAN_END_DATE;
                }
            }

            if ((startDate != null && endDate == null) || (startDate == null && endDate != null)) {
                return ErrorCode.START_DATE_OR_END_DATE_INVALID;
            }

            if (StringUtils.isEmpty(attributeInfo.getStatus())) {
                return ErrorCode.STATUS_CANNOT_EMPTY;
            }

            if (!VALID_STATUS.contains(attributeInfo.getStatus())) {
                return ErrorCode.STATUS_INVALID;
            }
        }

        return null;
    }

    private CustomerProfileVersioning toCustomerProfileVersioning(
            CustomerProfile customerProfile,
            Date cobDate
    ) {
        Integer versionDate = DateConverter.toYearMonthDay(cobDate);

        CustomerProfileVersioning customerProfileVersioning =
                customerProfileVersioningRepository.find(customerProfile.getLoyaltyCusId(), versionDate)
                        .orElse(null);

        if (Objects.isNull(customerProfileVersioning)) {
            customerProfileVersioning = new CustomerProfileVersioning();
            customerProfileVersioning.setCreatedAt(new Date());
            customerProfileVersioning.setVersionDate(versionDate);
        }

        customerProfileVersioning.setBusinessCode(businessId);
        customerProfileVersioning.setProgramCode(programId);
        customerProfileVersioning.setLoyaltyCusId(customerProfile.getLoyaltyCusId());
        customerProfileVersioning.setPrdUpdateFlag(customerProfile.getPrdUpdateFlag());
        customerProfileVersioning.setLoyaltyObDate(customerProfile.getLoyaltyObDate());
        customerProfileVersioning.setFrstCtcDt(customerProfile.getFrstCtcDt());
        customerProfileVersioning.setCstBrthDt(customerProfile.getCstBrthDt());
        customerProfileVersioning.setTierStartDate(customerProfile.getTierStartDate());
        customerProfileVersioning.setTierEndDate(customerProfile.getTierEndDate());
        customerProfileVersioning.setCstPreTier(customerProfile.getCstPreTier());
        customerProfileVersioning.setCstTier(customerProfile.getCstTier());
        customerProfileVersioning.setCstLoyaltyTier(customerProfile.getCstLoyaltyTier());
        customerProfileVersioning.setTargetSegmentCode(customerProfile.getTargetSegmentCode());
        customerProfileVersioning.setCstNationality(customerProfile.getCstNationality());
        customerProfileVersioning.setEmployeeInd(customerProfile.getEmployeeInd());
        customerProfileVersioning.setCstGender(customerProfile.getCstGender());
        customerProfileVersioning.setCstMobileInd(customerProfile.getCstMobileInd());
        customerProfileVersioning.setBizLine(customerProfile.getBizLine());
        customerProfileVersioning.setCstFirstLoginDate(customerProfile.getCstFirstLoginDate());
        customerProfileVersioning.setCstLastLoginDate(customerProfile.getCstLastLoginDate());
        customerProfileVersioning.setCstDebtGroup(customerProfile.getCstDebtGroup());
        customerProfileVersioning.setCstAstClss(customerProfile.getCstAstClss());
        customerProfileVersioning.setCstStaffInd(customerProfile.getCstStaffInd());
        customerProfileVersioning.setCstBankSegment(customerProfile.getCstBankSegment());
        customerProfileVersioning.setCstType(customerProfile.getCstType());
        customerProfileVersioning.setCstPbt(customerProfile.getCstPbt());
        customerProfileVersioning.setCstToi(customerProfile.getCstToi());
        customerProfileVersioning.setCstCost(customerProfile.getCstCost());
        customerProfileVersioning.setCstFamilyGroup(customerProfile.getCstFamilyGroup());
        customerProfileVersioning.setCstFamilyMemberType(customerProfile.getCstFamilyMemberType());
        customerProfileVersioning.setCstOccupation(customerProfile.getCstOccupation());
        customerProfileVersioning.setCstEkycStatus(customerProfile.getCstEkycStatus());
        customerProfileVersioning.setCstHomeBranch(customerProfile.getCstHomeBranch());
        customerProfileVersioning.setCstSubBranch(customerProfile.getCstSubBranch());
        customerProfileVersioning.setCstRmBranch(customerProfile.getCstRmBranch());
        customerProfileVersioning.setCstMerchantInd(customerProfile.getCstMerchantInd());
        customerProfileVersioning.setCstNotiChannel(customerProfile.getCstNotiChannel());
        customerProfileVersioning.setCstBlkLv(customerProfile.getCstBlkLv());
        customerProfileVersioning.setCstWtchList(customerProfile.getCstWtchList());
        customerProfileVersioning.setCstTierProgram(customerProfile.getCstTierProgram());
        customerProfileVersioning.setCstResidence(customerProfile.getCstResidence());
        customerProfileVersioning.setCstEkycChannel(customerProfile.getCstEkycChannel());
        customerProfileVersioning.setCstPrdHolding(customerProfile.getCstPrdHolding());
        customerProfileVersioning.setCstPrdActive(customerProfile.getCstPrdActive());
        customerProfileVersioning.setCstAeupoint(customerProfile.getCstAeupoint());
        customerProfileVersioning.setUpdatedAt(new Date());

        return customerProfileVersioning;
    }

    private CustomerCardInfoVersioning toCustomerCardInfoVersioning(
            CustomerCardInfo customerCardInfo,
            String versionCode,
            Date cobDate
    ) {
        Integer versionDate = DateConverter.toYearMonthDay(cobDate);
        CustomerCardInfoVersioning customerCardInfoVersioning =
                customerCardInfoVersioningRepository.find(customerCardInfo.getLoyaltyCusId(), customerCardInfo.getCardId(), versionDate)
                        .orElse(null);

        if (Objects.isNull(customerCardInfoVersioning)) {
            customerCardInfoVersioning = new CustomerCardInfoVersioning();
            customerCardInfoVersioning.setCreatedAt(new Date());
            customerCardInfoVersioning.setVersionDate(versionDate);
        }

        customerCardInfoVersioning.setBusinessCode(businessId);
        customerCardInfoVersioning.setProgramCode(programId);
        customerCardInfoVersioning.setLoyaltyCusId(customerCardInfo.getLoyaltyCusId());
        customerCardInfoVersioning.setArrangementId(customerCardInfo.getArrangementId());
        customerCardInfoVersioning.setCardId(customerCardInfo.getCardId());
        customerCardInfoVersioning.setCardInd(customerCardInfo.getCardInd());
        customerCardInfoVersioning.setProductCode(customerCardInfo.getProductCode());
        customerCardInfoVersioning.setProductLv1(customerCardInfo.getProductLv1());
        customerCardInfoVersioning.setProductLv2(customerCardInfo.getProductLv2());
        customerCardInfoVersioning.setProductLv3(customerCardInfo.getProductLv3());
        customerCardInfoVersioning.setProductLv4(customerCardInfo.getProductLv4());
        customerCardInfoVersioning.setProductLv5(customerCardInfo.getProductLv5());
        customerCardInfoVersioning.setProductLv6(customerCardInfo.getProductLv6());
        customerCardInfoVersioning.setCardExpireDate(customerCardInfo.getCardExpireDate());
        customerCardInfoVersioning.setCardStatus(customerCardInfo.getCardStatus());
        customerCardInfoVersioning.setCardOpenDate(customerCardInfo.getCardOpenDate());
        customerCardInfoVersioning.setCardActivatedDate(customerCardInfo.getCardActivatedDate());
        customerCardInfoVersioning.setCardTerm(customerCardInfo.getCardTerm());
        customerCardInfoVersioning.setCardLimit(customerCardInfo.getCardLimit());
        customerCardInfoVersioning.setCardAnnualFeePayment(customerCardInfo.getCardAnnualFeePayment());
        customerCardInfoVersioning.setCardBillCycleDate(customerCardInfo.getCardBillCycleDate());
        customerCardInfoVersioning.setCardPaymentDate(customerCardInfo.getCardPaymentDate());
        customerCardInfoVersioning.setCardPan(customerCardInfo.getCardPan());
        customerCardInfoVersioning.setCardType(customerCardInfo.getCardType());
        customerCardInfoVersioning.setCardIssueType(customerCardInfo.getCardIssueType());
        customerCardInfoVersioning.setCardNewAcquisitionInd(customerCardInfo.getCardNewAcquisitionInd());
        customerCardInfoVersioning.setUpdatedAt(new Date());

        return customerCardInfoVersioning;
    }

    private Long extractTimestampFromFilename(String filename) {
        if (filename == null || filename.isBlank()) {
            return null;
        }

        // Regular expression to match the ID before ".csv"
        String regex = ".*-(\\d+)\\.txt$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(filename);

        if (matcher.matches()) {
            try {
                return Long.parseLong(matcher.group(1));
            } catch (NumberFormatException e) {
                return null;
            }
        } else {
            return null;
        }
    }

    private Long getCobDate(String fileName) {
        if (fileName == null || fileName.isBlank()) {
            return null;
        }

        String regex = "[a-zA-Z]+-(\\d{8})-\\d+-\\d+-\\d+\\.txt$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(fileName);

        if (matcher.matches()) {
            try {
                return Long.parseLong(matcher.group(1));
            } catch (NumberFormatException e) {
                return null;
            }
        } else {
            return null;
        }
    }

    private String getVersionCode(Date versionDate) {
        return "ver" + DateTimes.format(VERSION_FORMATTER, versionDate, null);
    }

    private Date getVersionDate(String fileName) {
        Long dateLong = getCobDate(fileName);
        if (dateLong == null) {
            return null;
        }

        // Convert YYYYMMDD Long to String and parse to Date using DateConverter
        String dateStr = String.format("%08d", dateLong); // Ensure 8 digits, e.g., "20250716"
        Date versionDate = DateConverter.toDateFormatterYYYYMMDD(
                dateStr.substring(0, 4) + "-" + dateStr.substring(4, 6) + "-" + dateStr.substring(6, 8)
        );
        return versionDate;
    }

    private CustomerProductInfoVersioning toCustomerProductInfoVersioning(
            CustomerProfileData.ProductInfo info,
            Date cobDate,
            String loyaltyCusId
    ) {
        Integer versionDate = DateConverter.toYearMonthDay(cobDate);

        CustomerProductInfoVersioning cusProductInfoVersioning =
                customerProductInfoVersioningRepository.find(loyaltyCusId, info.getArrangementId(), versionDate)
                        .orElse(null);

        if (Objects.isNull(cusProductInfoVersioning)) {
            cusProductInfoVersioning = new CustomerProductInfoVersioning();
            cusProductInfoVersioning.setCreatedAt(new Date());
            cusProductInfoVersioning.setVersionDate(versionDate);
        }
        cusProductInfoVersioning.setBusinessCode(businessId);
        cusProductInfoVersioning.setProgramCode(programId);
        cusProductInfoVersioning.setLoyaltyCusId(loyaltyCusId);
        cusProductInfoVersioning.setArrangementId(info.getArrangementId());
        cusProductInfoVersioning.setProductCode(info.getProductCode());
        cusProductInfoVersioning.setProductLv1(info.getProductLv1());
        cusProductInfoVersioning.setProductLv2(info.getProductLv2());
        cusProductInfoVersioning.setProductLv3(info.getProductLv3());
        cusProductInfoVersioning.setProductLv4(info.getProductLv4());
        cusProductInfoVersioning.setProductLv5(info.getProductLv5());
        cusProductInfoVersioning.setPrdBranchCode(info.getPrdBranchCode());
        cusProductInfoVersioning.setProdOpenDate(info.getProdOpenDate());
        cusProductInfoVersioning.setProdExpireDate(info.getProdExpireDate());
        cusProductInfoVersioning.setPrdBillCycle(info.getPrdBillCycle());
        cusProductInfoVersioning.setPrdStatus(info.getPrdStatus());
        cusProductInfoVersioning.setPrdCcyCode(info.getPrdCcyCode());
        cusProductInfoVersioning.setPrdRolloverDate(info.getPrdRolloverDate());
        cusProductInfoVersioning.setProductLv6(info.getProductLv6());
        cusProductInfoVersioning.setPrdTenor(info.getPrdTenor());
        cusProductInfoVersioning.setPrdCombo(info.getPrdCombo());
        cusProductInfoVersioning.setPrdAccArrId(info.getPrdAccArrId());
        cusProductInfoVersioning.setPrdJointHolderInd(info.getPrdJointHolderInd());
        cusProductInfoVersioning.setPrdAutoBillInd(info.getPrdAutoBillInd());
        cusProductInfoVersioning.setPrdSalesChannel(info.getPrdSalesChannel());
        cusProductInfoVersioning.setPrdPaymentDate(info.getPrdPaymentDate());
        cusProductInfoVersioning.setPrdIssueDate(info.getPrdIssueDate());
        cusProductInfoVersioning.setPrdInsuranceRenew(info.getPrdInsuranceRenew());
        cusProductInfoVersioning.setPrdRoll(info.getPrdRoll());
        cusProductInfoVersioning.setPrdTdAmount(info.getPrdTdAmount());
        cusProductInfoVersioning.setPrdAutoRollInd(info.getPrdAutoRollInd());
        cusProductInfoVersioning.setUpdatedAt(new Date());

        return cusProductInfoVersioning;
    }
}