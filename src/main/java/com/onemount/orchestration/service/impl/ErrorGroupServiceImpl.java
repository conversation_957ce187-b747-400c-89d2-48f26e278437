package com.onemount.orchestration.service.impl;

import com.onemount.orchestration.constant.EFileType;
import com.onemount.orchestration.entity.Ps2EarningErrorGroup;
import com.onemount.orchestration.repository.Ps2EarningErrorGroupRepository;
import com.onemount.orchestration.service.ErrorGroupService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ErrorGroupServiceImpl implements ErrorGroupService, InitializingBean {

    private final Ps2EarningErrorGroupRepository ps2EarningErrorGroupRepository;

    private Map<EFileType, List<Ps2EarningErrorGroup>> errorGroupMap;
    private Ps2EarningErrorGroup defaultErrorGroup;

    @Override
    public Ps2EarningErrorGroup mapping(EFileType type, String serviceErrorCode, String errorCode) {
        List<Ps2EarningErrorGroup> errorGroups = errorGroupMap.get(type);
        if (errorGroups != null && !errorGroups.isEmpty()) {
            return errorGroups.stream()
                    .filter(e -> match(e, serviceErrorCode, errorCode))
                    .findFirst()
                    .orElse(defaultErrorGroup);
        }

        return defaultErrorGroup;
    }

    private boolean match(Ps2EarningErrorGroup e, String serviceErrorCode, String errorCode) {
        return (serviceErrorCode != null && serviceErrorCode.equals(e.getServiceErrorCode()))
                || (errorCode != null && errorCode.equals(e.getErrorCode()));
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        defaultErrorGroup = new Ps2EarningErrorGroup();
        defaultErrorGroup.setCode("E007");
        defaultErrorGroup.setMessage("Undefined");

        errorGroupMap = ps2EarningErrorGroupRepository.findAll()
                .stream()
                .collect(Collectors.groupingBy(Ps2EarningErrorGroup::getFileType));
    }
}