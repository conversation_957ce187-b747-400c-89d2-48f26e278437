package com.onemount.orchestration.service.impl;

import com.oneid.oneloyalty.common.entity.KafkaAuditTrailEarnTransactionRetry;
import com.oneid.oneloyalty.common.repository.KafkaAuditTrailEarnTransactionRetryRepository;
import com.onemount.orchestration.constant.EProcessServiceId;
import com.onemount.orchestration.entity.RetryCheckpoint;
import com.onemount.orchestration.model.ProcessData;
import com.onemount.orchestration.model.TransactionData;
import com.onemount.orchestration.repository.RetryCheckpointRepository;
import com.onemount.orchestration.support.DateConverter;
import com.onemount.orchestration.support.utils.Log;
import com.onemount.orchestration.support.utils.LogData;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Service
@AllArgsConstructor
public class TransactionRetryService {

    private static final String RETRY_NAME = "ps2-file-transaction-retry";
    private static final Integer RETRY_COUNT = 2;
    private static final int PAGE_SIZE = 1000;

    private final RetryCheckpointRepository checkpointRepo;
    private final TransactionProcessService transactionService;
    private final KafkaAuditTrailEarnTransactionRetryRepository retryRepo;

    public void process() {
        String requestId = UUID.randomUUID().toString();

        Long offsetStart = getOffset();
        Long offsetEnd = retryRepo.findEnd(offsetStart, RETRY_COUNT);

        // Have other process
        if (offsetStart == null || Objects.equals(offsetStart, offsetEnd)) {
            Log.info(LogData.createLogData()
                    .append("msg", "[TransactionRetryService] -- Does not retry earn transactions")
                    .append("offset_start", offsetStart)
                    .append("offset_end", offsetEnd)
                    .append("request_id", requestId));
            return;
        }

        Log.info(LogData.createLogData()
                .append("msg", "[TransactionRetryService] -- Start retry earn transactions")
                .append("from_offset", offsetStart)
                .append("request_id", requestId));

        int pageNumber = 0;

        while (true) {
            Pageable pageable = PageRequest.of(pageNumber, PAGE_SIZE, Sort.by("id").ascending());
            Page<KafkaAuditTrailEarnTransactionRetry> page = retryRepo.find(offsetStart, offsetEnd, RETRY_COUNT, pageable);

            if (page.isEmpty()) {
                Log.info(LogData.createLogData()
                        .append("msg", "[TransactionRetryService] -- Processed retry page")
                        .append("size", 0)
                        .append("request_id", requestId));
                break;
            }

            List<KafkaAuditTrailEarnTransactionRetry> records = page.getContent();
            offsetStart = records.get(records.size() - 1).getId();

            Log.info(LogData.createLogData()
                    .append("msg", "[TransactionRetryService] -- Start process retry page")
                    .append("request_id", requestId)
                    .append("page", pageNumber)
                    .append("size", records.size())
            );

            records.forEach(record -> processRetryRecord(record, requestId));

            Log.info(LogData.createLogData()
                    .append("msg", "[TransactionRetryService] -- End process retry page")
                    .append("request_id", requestId)
                    .append("page", pageNumber)
                    .append("size", records.size())
            );

            if (!page.hasNext()) {
                break;
            }

            pageNumber++;
        }

        updateOffset(offsetStart);

        Log.info(LogData.createLogData()
                .append("msg", "[TransactionRetryService] -- End retry earn transactions")
                .append("end_offset", offsetStart)
                .append("request_id", requestId));
    }

    private Long getOffset() {
        RetryCheckpoint checkpoint = checkpointRepo.findByName(RETRY_NAME);

        // Init if null
        if (checkpoint == null) {
            checkpoint = RetryCheckpoint.builder()
                    .name(RETRY_NAME)
                    .currentOffset(0L)
                    .createdAt(new Date())
                    .updatedAt(new Date())
                    .build();
            checkpoint = checkpointRepo.save(checkpoint);
        }

        return checkpoint.getCurrentOffset() != null ? checkpoint.getCurrentOffset() : 0;
    }

    private void updateOffset(Long offset) {
        RetryCheckpoint checkpoint = checkpointRepo.findByName(RETRY_NAME);
        if (checkpoint == null) {
            checkpoint = RetryCheckpoint.builder()
                    .name(RETRY_NAME)
                    .createdAt(new Date())
                    .build();
        }

        checkpoint.setCurrentOffset(offset);
        checkpoint.setUpdatedAt(new Date());
        checkpointRepo.save(checkpoint);
    }

    private void processRetryRecord(KafkaAuditTrailEarnTransactionRetry record, String requestId) {
        TransactionData data = convertToTransactionData(record);
        ProcessData<TransactionData> processData = new ProcessData<>(
                EProcessServiceId.TRANSACTION.getName(), null, data,
                record.getFileName(), record.getLoyaltyCusId(), 0 // todo update index of retry line
        );
        try {
            Log.info(LogData.createLogData()
                    .append("msg", "[TransactionRetryService] -- Retrying earn transaction")
                    .append("request_id", requestId)
                    .append("transaction_id", record.getTransactionId())
                    .append("loyalty_cus_id", record.getLoyaltyCusId())
                    .append("record_id", record.getId()));

            transactionService.process(processData);
        } catch (Exception e) {
            Log.error(LogData.createLogData()
                    .append("msg", "[TransactionRetryService] -- Exception while retry")
                    .append("request_id", requestId)
                    .append("transaction_id", record.getTransactionId())
                    .append("loyalty_cus_id", record.getLoyaltyCusId())
                    .append("record_id", record.getId())
                    .append("error", e.getMessage()));
        }
    }

    public TransactionData convertToTransactionData(KafkaAuditTrailEarnTransactionRetry entity) {
        TransactionData data = new TransactionData();

        data.setLoyaltyCusId(entity.getLoyaltyCusId());
        data.setTransactionId(entity.getTransactionId());
        data.setTxnCode(entity.getTxnCode());
        data.setTxnServiceType(entity.getTxnServiceType());
        data.setArrangementId(entity.getArrangementId());
        data.setCardId(entity.getCardId());
        data.setTxnProductFamily(entity.getTxnProductFamily());
        data.setTxnProductLine(entity.getTxnProductLine());
        data.setTxnInOut(entity.getTxnInOut());
        data.setTxnChannelLv1(entity.getTxnChannelLv1());
        data.setTxnChannelLv2(entity.getTxnChannelLv2());
        data.setTxnChannelLv3(entity.getTxnChannelLv3());
        data.setTxnMotherCategory(entity.getTxnMotherCategory());
        data.setTxnMcc(entity.getTxnMcc());
        data.setTxnServiceLv1(entity.getTxnServiceLv1());
        data.setTxnServiceLv2(entity.getTxnServiceLv2());
        data.setTxnCategoryGroup(entity.getTxnCategoryGroup());
        data.setTxnPurpose(entity.getTxnPurpose());
        data.setTxnMerchantPurpose(entity.getTxnMerchantPurpose());
        data.setTxnSupplier(entity.getTxnSupplier());
        data.setTxnCcyCode(entity.getTxnCcyCode());
        data.setTxnAdjReason(entity.getTxnAdjReason());
        data.setTxnSlrInd(entity.getTxnSlrInd());
        data.setTxnDate(entity.getTxnDate());
        data.setTxnTime(entity.getTxnTime());
        data.setTxnGrossAmt(entity.getTxnGrossAmt());
        data.setTxnNetAmt(entity.getTxnNetAmt());
        data.setTxnTerminalname(entity.getTxnTerminalname());
        data.setTxnBrandname(entity.getTxnBrandname());
        data.setTxnTid(entity.getTxnTid());
        data.setTxnMid(entity.getTxnMid());
        data.setTxnTermlocation(entity.getTxnTermlocation());
        data.setTxnForeignTxnAmt(entity.getTxnForeignTxnAmt());
        data.setTxnForeignExcRate(entity.getTxnForeignExcRate());
        data.setTxnStatus(entity.getTxnStatus());
        data.setTxnAdjSign(entity.getTxnAdjSign());
        data.setTxnOriginalId(entity.getTxnOriginalId());
        data.setTxnAppcode(entity.getTxnAppcode());
        data.setTxnToken(entity.getTxnToken());
        data.setTxnBin(entity.getTxnBin());
        data.setTxnType(entity.getTxnType());
        data.setTxnTermowner(entity.getTxnTermowner());
        data.setTxnSettlementDate(entity.getTxnSettlementDate());
        data.setTxnPostDate(entity.getTxnPostDate());
        data.setTxnQrMerchantInd(entity.getTxnQrMerchantInd());
        data.setTxnForeignCcyCode(entity.getTxnForeignCcyCode());
        data.setTxnBoundary(entity.getTxnBoundary());
        data.setTxnSubType(entity.getTxnSubType());
        data.setTxnChannelLv4(entity.getTxnChannelLv4());
        data.setTxnRecurringInd(entity.getTxnRecurringInd());
        data.setTxnCardCombo(entity.getTxnCardCombo());
        data.setRetryCount(entity.getRetryCount() != null ? entity.getRetryCount() + 1 : 1);
        data.setTxnRankCheck(entity.getTxnRankCheck());

        if (entity.getDsPartitionDate() != null) {
            data.setDsPartitionDate(DateConverter.toYYYYMMDD(entity.getDsPartitionDate()));
        }

        return data;
    }
}
