package com.onemount.orchestration.service.impl;

import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.entity.ControlFileHistory;
import com.oneid.oneloyalty.common.entity.DataFileHistory;
import com.oneid.oneloyalty.common.repository.ControlFileHistoryRepository;
import com.oneid.oneloyalty.common.repository.DataFileHistoryRepository;
import com.onemount.orchestration.model.UpdateHistoryRequest;
import com.onemount.orchestration.service.HistoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class HistoryServiceImpl implements HistoryService {

    private final ControlFileHistoryRepository controlFileHistoryRepo;
    private final DataFileHistoryRepository dataFileHistoryRepository;

    @Override
    public Page<ControlFileHistory> getList(Pageable page) {
        return controlFileHistoryRepo.findAll(page);
    }

    @Override
    public ControlFileHistory detail(String controlFileName) {
        return controlFileHistoryRepo.findByFileName(controlFileName, EFileType.EARNING_FLOW);
    }

    @Override
    public Page<DataFileHistory> getListDataFiles(String controlFileName, Pageable page) {
        ControlFileHistory controlFileHistory = controlFileHistoryRepo.findByFileName(controlFileName, EFileType.EARNING_FLOW);
        if (controlFileHistory != null) {
            return dataFileHistoryRepository.findByControlFileName(controlFileName, page);
        }
        return Page.empty();
    }

    @Override
    public void updateStatus(UpdateHistoryRequest updateHistory) {
        ControlFileHistory controlFileHistory = controlFileHistoryRepo.findByFileName(updateHistory.getControlFileName(), EFileType.EARNING_FLOW);
        if (controlFileHistory != null) {
            if (updateHistory.getStatus() != null) {
                controlFileHistory.setStatus(updateHistory.getStatus());
            }
            if (updateHistory.getIgnore() != null) {
                controlFileHistory.setIsIgnored(updateHistory.getIgnore());
            }
            controlFileHistoryRepo.save(controlFileHistory);
        }
    }
}
