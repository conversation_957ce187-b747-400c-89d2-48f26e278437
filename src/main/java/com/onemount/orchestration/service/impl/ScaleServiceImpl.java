package com.onemount.orchestration.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import com.onemount.orchestration.constant.ErrorCode;
import com.onemount.orchestration.constant.ScaleErrorCode;
import com.onemount.orchestration.model.ScaleInfo;
import com.onemount.orchestration.model.ScaleResponse;
import com.onemount.orchestration.service.OlConfigParam;
import com.onemount.orchestration.service.ScaleService;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class ScaleServiceImpl implements ScaleService {

    private static final String SCALE_PATH = "/scale/";
    private static final Map<Integer, ScaleErrorCode> ERROR_CODE_MAP = new HashMap<>();
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        for (ScaleErrorCode errorCode : ScaleErrorCode.values()) {
            ERROR_CODE_MAP.put(errorCode.getCode(), errorCode);
        }
    }

    private final RestTemplate restTemplate;
    private final OlConfigParam olConfigParam;

    public ScaleServiceImpl(RestTemplate restTemplate, OlConfigParam olConfigParam) {
        this.restTemplate = restTemplate;
        this.olConfigParam = olConfigParam;
    }

    @Override
    public List<ScaleInfo> scale(String businessName) {
        String requestId = generateRequestId();
        validateBusinessName(businessName, requestId);
        String url = buildScaleUrl(businessName);

        try {
            ResponseEntity<ScaleResponse> response = executeHttpRequest(url, HttpMethod.POST, requestId);
            return extractScaleData(response, businessName, requestId);
        } catch (Exception e) {
            throw handleScaleException(e, requestId, businessName, "Scale service call failed");
        }
    }

    @Override
    public ScaleResponse getScaleStatus(String businessName) {
        String requestId = generateRequestId();
        validateBusinessName(businessName, requestId);
        String url = buildScaleUrl(businessName);

        try {
            ResponseEntity<ScaleResponse> response = executeHttpRequest(url, HttpMethod.GET, requestId);
            ScaleResponse scaleResponse = enrichResponseWithStatus(response, requestId, businessName);
            logScaleStatusSuccess(requestId, businessName, response.getStatusCode().value());
            return scaleResponse;
        } catch (Exception e) {
            throw handleScaleException(e, requestId, businessName, "Failed to retrieve scale status");
        }
    }

    private String generateRequestId() {
        return UUID.randomUUID().toString();
    }

    private void validateBusinessName(String businessName, String requestId) {
        if (businessName == null || businessName.trim().isEmpty()) {
            Log.error(createLogData(requestId)
                    .append("msg", "Business name validation failed")
                    .append("error_code", ScaleErrorCode.RESIZE_HPA_MISSING_BUSINESS_FIELD.getCode()));
            throw new BusinessException(
                    null,
                    ErrorCode.INVALID.getCode(),
                    String.format("Field `business` is required (code: %d)", ScaleErrorCode.RESIZE_HPA_MISSING_BUSINESS_FIELD.getCode()),
                    createLogData(requestId).append("error_code", ScaleErrorCode.RESIZE_HPA_MISSING_BUSINESS_FIELD.getCode())
            );
        }
    }

    private String buildScaleUrl(String businessName) {
        return UriComponentsBuilder
                .fromUriString(olConfigParam.olScaleUrl)
                .path(SCALE_PATH)
                .pathSegment(businessName)
                .toUriString();
    }

    private HttpEntity<Void> createHttpEntity(String requestId) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-Request-ID", requestId);
        return new HttpEntity<>(headers);
    }

    private ResponseEntity<ScaleResponse> executeHttpRequest(String url, HttpMethod method, String requestId) {
        return restTemplate.exchange(url, method, createHttpEntity(requestId), ScaleResponse.class);
    }

    private List<ScaleInfo> extractScaleData(ResponseEntity<ScaleResponse> response, String businessName, String requestId) {
        ScaleResponse scaleResponse = response.getBody();
        if (scaleResponse == null || scaleResponse.getPayload() == null || scaleResponse.getPayload().getData() == null) {
            Log.error(createLogData(requestId)
                    .append("msg", "ScaleProcess -- Invalid scale response")
                    .append("business_name", businessName)
                    .append("error_code", ScaleErrorCode.FAIL_TO_PARSE_BODY.getCode()));
            throw new BusinessException(
                    null,
                    ErrorCode.INVALID.getCode(),
                    String.format("Failed to get business `%s`: Empty response (code: %d)", businessName, ScaleErrorCode.FAIL_TO_PARSE_BODY.getCode()),
                    createLogData(requestId).append("error_code", ScaleErrorCode.FAIL_TO_PARSE_BODY.getCode())
            );
        }
        return scaleResponse.getPayload().getData();
    }

    private ScaleResponse enrichResponseWithStatus(ResponseEntity<ScaleResponse> response, String requestId, String businessName) {
        ScaleResponse scaleResponse = response.getBody();
        if (scaleResponse != null) {
            scaleResponse.setHttpStatus(HttpStatus.valueOf(response.getStatusCode().value()));
        } else {
            Log.warn(createLogData(requestId)
                    .append("msg", "ScaleProcess -- Null scale response received")
                    .append("business_name", businessName));
        }
        return scaleResponse;
    }

    private void logScaleStatusSuccess(String requestId, String businessName, int httpStatus) {
        Log.info(createLogData(requestId)
                .append("msg", "ScaleProcess -- Scale status check")
                .append("business_name", businessName)
                .append("http_status", httpStatus));
    }

    private BusinessException handleScaleException(Exception e, String requestId, String businessName, String message) {
        LogData logData = createLogData(requestId)
                .append("msg", "ScaleProcess -- " + message)
                .append("business_name", businessName)
                .append("err_msg", e.getMessage());
        Log.error(logData);

        if (e instanceof HttpClientErrorException.NotFound) {
            logData.append("error_code", ScaleErrorCode.K8S_NOT_FOUND_WORKLOAD.getCode());
            return new BusinessException(
                    null,
                    ErrorCode.INVALID.getCode(),
                    ScaleErrorCode.K8S_NOT_FOUND_WORKLOAD.getMessage(businessName),
                    logData
            );
        }

        if (e instanceof HttpServerErrorException.InternalServerError) {
            return handleServerError((HttpServerErrorException) e, requestId, businessName, logData);
        }

        int errorCode = determineErrorCode(e.getMessage());
        logData.append("error_code", errorCode)
                .append("req_url", olConfigParam.olScaleUrl);
        return new BusinessException(
                null,
                ErrorCode.UNKNOWN.getCode(),
                ScaleErrorCode.FAIL_TO_PARSE_BODY.getMessage(businessName),
                logData
        );
    }

    private BusinessException handleServerError(HttpServerErrorException e, String requestId, String businessName, LogData logData) {
        try {
            String responseBody = e.getResponseBodyAsString();
            if (responseBody == null || responseBody.trim().isEmpty()) {
                logData.append("error_code", ScaleErrorCode.FAIL_TO_PARSE_BODY.getCode());
                return new BusinessException(
                        null,
                        ErrorCode.UNKNOWN.getCode(),
                        ScaleErrorCode.FAIL_TO_PARSE_BODY.getMessage(businessName),
                        logData
                );
            }

            // Parse the error response body as a Map
            Map<String, Object> errorResponse = OBJECT_MAPPER.readValue(responseBody, Map.class);
            Integer errorCode = (Integer) errorResponse.get("code");
            String payload = (String) errorResponse.get("payload");

            if (errorCode == null || payload == null) {
                logData.append("error_code", ScaleErrorCode.FAIL_TO_PARSE_BODY.getCode());
                return new BusinessException(
                        null,
                        ErrorCode.UNKNOWN.getCode(),
                        ScaleErrorCode.FAIL_TO_PARSE_BODY.getMessage(businessName),
                        logData
                );
            }

            logData.append("error_code", errorCode);
            ScaleErrorCode scaleErrorCode = mapApiErrorCode(errorCode);
            return new BusinessException(
                    null,
                    scaleErrorCode == ScaleErrorCode.RESIZE_HPA_MISSING_BUSINESS_FIELD ? ErrorCode.INVALID.getCode() : ErrorCode.UNKNOWN.getCode(),
                    payload,
                    logData
            );
        } catch (Exception parseEx) {
            logData.append("error_code", ScaleErrorCode.FAIL_TO_PARSE_BODY.getCode())
                    .append("parse_error", parseEx.getMessage());
            return new BusinessException(
                    null,
                    ErrorCode.UNKNOWN.getCode(),
                    ScaleErrorCode.FAIL_TO_PARSE_BODY.getMessage(businessName),
                    logData
            );
        }
    }

    private LogData createLogData(String requestId) {
        return LogData.createLogData().append("request_id", requestId);
    }

    private ScaleErrorCode mapApiErrorCode(int apiErrorCode) {
        return ERROR_CODE_MAP.getOrDefault(apiErrorCode, ScaleErrorCode.FAIL_TO_PARSE_BODY);
    }

    private int determineErrorCode(String errorMessage) {
        if (errorMessage == null) {
            return ScaleErrorCode.FAIL_TO_PARSE_BODY.getCode();
        }
        if (errorMessage.contains("lowerscale")) {
            return ScaleErrorCode.K8S_UPDATE_LOWERSCALE.getCode();
        }
        if (errorMessage.contains("upperscale")) {
            return ScaleErrorCode.K8S_UPDATE_UPPERSCALE.getCode();
        }
        return ScaleErrorCode.FAIL_TO_PARSE_BODY.getCode();
    }
}
