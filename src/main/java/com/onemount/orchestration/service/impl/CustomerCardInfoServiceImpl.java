package com.onemount.orchestration.service.impl;

import com.oneid.oneloyalty.common.entity.CustomerCardInfo;
import com.oneid.oneloyalty.common.entity.CustomerCardInfoVersioning;
import com.oneid.oneloyalty.common.mapper.CustomerCardInfoMapper;
import com.oneid.oneloyalty.common.repository.CustomerCardInfoRepository;
import com.oneid.oneloyalty.common.repository.CustomerCardInfoVersioningRepository;
import com.onemount.orchestration.service.CustomerCardInfoService;
import com.onemount.orchestration.support.DateConverter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class CustomerCardInfoServiceImpl implements CustomerCardInfoService {

    private final CustomerCardInfoRepository customerCardInfoRepository;
    private final CustomerCardInfoVersioningRepository customerCardInfoVersioningRepository;
    private final CustomerCardInfoMapper customerCardInfoMapper;

    @Value("${app.enable-earning-versioning:false}")
    private Boolean enableEarningByVersioning;

    @Override
    public CustomerCardInfo getCustomerCardInfo(String loyCusId, String cardId, Date txnTime) {
        if (txnTime != null && enableEarningByVersioning) {
            Integer txnDate = DateConverter.toYearMonthDay(txnTime);

            CustomerCardInfoVersioning CardInfoVersioning = getLessThan(loyCusId, cardId, txnDate);
            if (CardInfoVersioning == null) {
                CardInfoVersioning = getGreaterThan(loyCusId, cardId, txnDate);
            }

            if (CardInfoVersioning != null) {
                return customerCardInfoMapper.toCustomerCardInfo(CardInfoVersioning);
            }
        }

        return getCurrentCardInfo(loyCusId, cardId);
    }


    private CustomerCardInfo getCurrentCardInfo(String loyCusId, String cardId) {
        List<CustomerCardInfo> customerCardInfos = customerCardInfoRepository.find(loyCusId, cardId);
        if (CollectionUtils.isNotEmpty(customerCardInfos)) {
            return customerCardInfos.get(0);
        }
        return null;
    }

    private CustomerCardInfoVersioning getLessThan(String loyCusId, String arrangementId, Integer txnDate) {
        PageRequest pageRequest =
                PageRequest.of(0, 1, Sort.by("versionDate").descending());

        List<CustomerCardInfoVersioning> Card =
                customerCardInfoVersioningRepository.findByVersionDateLTE(loyCusId, arrangementId, txnDate, pageRequest);
        return Card.isEmpty() ? null : Card.get(0);
    }

    private CustomerCardInfoVersioning getGreaterThan(String loyCusId, String arrangementId, Integer txnDate) {
        PageRequest pageRequest = PageRequest.of(0, 1, Sort.by("versionDate"));

        List<CustomerCardInfoVersioning> Card =
                customerCardInfoVersioningRepository.findByVersionDateGTE(loyCusId, arrangementId, txnDate, pageRequest);
        return Card.isEmpty() ? null : Card.get(0);
    }
}
