package com.onemount.orchestration.service.impl;

import com.oneid.oneloyalty.client.escrowpoint.EscrowPointClient;
import com.oneid.oneloyalty.client.model.RevokeEscrowPointReq;
import com.oneid.oneloyalty.client.model.RevokeEscrowPointV2Res;
import com.oneid.oneloyalty.client.model.TransactionAdjustmentSchemeV2Req;
import com.oneid.oneloyalty.client.model.TransactionAdjustmentSchemeV2Res;
import com.oneid.oneloyalty.client.model.TransactionEarnRes;
import com.oneid.oneloyalty.client.model.TransactionEarnServiceReq;
import com.oneid.oneloyalty.client.model.TransactionReq;
import com.oneid.oneloyalty.client.model.TransactionReverseV2Req;
import com.oneid.oneloyalty.client.model.TransactionReverseV2Res;
import com.oneid.oneloyalty.client.model.attributes.CheckFraudRes;
import com.oneid.oneloyalty.client.model.attributes.TransactionAttributeReq;
import com.oneid.oneloyalty.client.service.ServiceClient;
import com.oneid.oneloyalty.client.transaction.TransactionClient;
import com.oneid.oneloyalty.common.constant.EAdjustmentType;
import com.oneid.oneloyalty.common.constant.EBoolean;
import com.oneid.oneloyalty.common.constant.EIdType;
import com.oneid.oneloyalty.common.entity.CustomerCardInfo;
import com.oneid.oneloyalty.common.entity.CustomerProductInfo;
import com.oneid.oneloyalty.common.entity.KafkaAuditTrailEarnTransaction;
import com.oneid.oneloyalty.common.entity.KafkaAuditTrailEarnTransactionRetry;
import com.oneid.oneloyalty.common.entity.ProductCode;
import com.oneid.oneloyalty.common.exception.BusinessException;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import com.oneid.oneloyalty.common.repository.KafkaAuditTrailEarnTransactionRepository;
import com.oneid.oneloyalty.common.repository.KafkaAuditTrailEarnTransactionRetryRepository;
import com.oneid.oneloyalty.common.repository.ProductCodeRepository;
import com.onemount.orchestration.constant.EAttributeType;
import com.onemount.orchestration.constant.EErrorCode;
import com.onemount.orchestration.constant.EFileType;
import com.onemount.orchestration.constant.EProcessServiceId;
import com.onemount.orchestration.constant.ETxnErrorCode;
import com.onemount.orchestration.constant.MessageType;
import com.onemount.orchestration.constant.ServiceErrorCode;
import com.onemount.orchestration.constant.TxnStatus;
import com.onemount.orchestration.dto.ServiceError;
import com.onemount.orchestration.entity.Ps2EarningErrorGroup;
import com.onemount.orchestration.kafka.event.produce.GamiEarnTransactionProduce;
import com.onemount.orchestration.kafka.service.ProduceService;
import com.onemount.orchestration.model.ProcessData;
import com.onemount.orchestration.model.TransactionData;
import com.onemount.orchestration.service.ContentProcessService;
import com.onemount.orchestration.service.CustomerCardInfoService;
import com.onemount.orchestration.service.CustomerProductInfoService;
import com.onemount.orchestration.service.DeserializeService;
import com.onemount.orchestration.service.ErrorGroupService;
import com.onemount.orchestration.support.DateConverter;
import com.onemount.orchestration.support.retry.RetryHelper;
import com.onemount.orchestration.support.utils.AttributeConverter;
import com.onemount.orchestration.support.utils.DateTimeConverter;
import com.onemount.orchestration.support.utils.LengthValidationUtil;
import com.onemount.orchestration.support.utils.Log;
import com.onemount.orchestration.support.utils.LogData;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class TransactionProcessService implements ContentProcessService<TransactionData> {
    private static final int LENGTH_1000 = 1000;
    private static final int LENGTH_255 = 255;
    private static final int ACTION_EARN = 1;
    private static final List<String> VALID_TXN_STATUSES = List.of(TxnStatus.POSTED, TxnStatus.REFUND, TxnStatus.REVERT);
    private static final String[] FILE_OUTPUT_HEADER = {
            "loyalty_id",
            "transaction_id",
            "txn_original_id",
            "txn_ref_no",
            "ds_partition_date",
            "error_group",
            "error_message",
            "error_message_detail",
            "retry"
    };
    private final String FRAUD_MID_TERMOWNER_KEY = "mid_termowner";
    private final String FRAUD_MCC_KEY = "mcc";
    private final List<String> FRAUD_MID_TERMOWNER_ERROR_CODES = List.of("800001", "800003", "800004");
    private final List<String> FRAUD_MCC_ERROR_CODES = List.of("700001", "700003", "700004");
    private final ServiceClient serviceClient;
    private final EscrowPointClient escrowPointClient;
    private final TransactionClient transactionClient;
    private final ProduceService produceService;
    private final DeserializeService deserializeService;
    private final ProductCodeRepository productCodeRepository;
    private final KafkaAuditTrailEarnTransactionRepository kafkaAuditTrailTransEarnRepository;
    private final KafkaAuditTrailEarnTransactionRetryRepository kafkaAuditTrailTransEarnRetryRepository;
    private final CustomerCardInfoService customerCardInfoService;
    private final CustomerProductInfoService customerProductInfoService;
    private final RetryTemplate retryTemplate;
    private final ErrorGroupService errorGroupService;

    @Value("${app.program-code}")
    private String programCode;
    @Value("${app.business-code}")
    private String businessCode;
    @Value("${app.corporation-code}")
    private String corporationCode;
    @Value("${app.store-code.earn-txn}")
    private String storeCode;
    @Value("${app.pos-code.earn-txn}")
    private String posCode;
    @Value("${app.currency-code}")
    private String currencyCode;
    @Value("${app.reason-code}")
    private String reasonCode;
    @Value("${app.service-code.earn-txn}")
    private String serviceCodeEarnTxn;
    @Value("${app.service-code.revoke-txn}")
    private String serviceCodeRevokeTxn;
    @Value("${app.service-code.refund}")
    private String serviceCodeRefund;
    @Value("${app.channel-code}")
    private String channelCode;
    @Value("${app.product-casa-code}")
    private String productCasaCode;
    @Value("${app.end-of-month-dates}")
    private List<String> endOfMonthDates;
    @Value("${kafka.topic.trans-gami-produce}")
    private String gamiTxnTopic;
    @Value("${app.enable-event-gami:}")
    private Boolean enableEventGami;

    @Override
    public EProcessServiceId getProcessId() {
        return EProcessServiceId.TRANSACTION;
    }

    @Override
    public String getOrderKey(TransactionData data) {
        if (Objects.nonNull(data)) {
            return data.getLoyaltyCusId();
        }
        return null;
    }

    @Override
    public TransactionData deserialize(String data) {
        return deserializeService.deserialize(data, TransactionData.class);
    }

    @Override
    public String[] getHeader() {
        return FILE_OUTPUT_HEADER;
    }

    @Override
    public String[] generateOutput(ProcessData<TransactionData> processData) {
        TransactionData data = processData.getDataObj();

        Ps2EarningErrorGroup errorGroup = errorGroupService.mapping(
                EFileType.TRANSACTION,
                data.getServiceError() != null ? data.getServiceError().getCode() : null,
                data.getErrorCode() != null ? data.getErrorCode().getCode() : null
        );

        return new String[]{
                data.getLoyaltyCusId(),
                data.getTransactionId(),
                data.getTxnOriginalId(),
                data.getLoyTxnRef(),
                data.getDsPartitionDate(),
                errorGroup.getCode(),
                errorGroup.getMessage(),
                errorGroup.getMessageDetail(),
                null
        };
    }

    @Override
    public void process(ProcessData<TransactionData> processData) {
        String requestId = UUID.randomUUID().toString();
        TransactionData transaction = processData.getDataObj();

        verifyMessage(transaction);

        if (Objects.isNull(transaction.getErrorCode())) {
            Integer serviceErrorCode = null;
            String serviceErrorMessage = null;
            try {
                String loyaltyCusId = transaction.getLoyaltyCusId();
                /*
                 * Process POSTED transaction
                 */
                if (TxnStatus.POSTED.equals(transaction.getTxnStatus())) {

                    List<TransactionAttributeReq> attributes =
                            buildRequestAttributes(loyaltyCusId, transaction)
                                    .stream()
                                    .filter(att -> !StringUtils.isEmpty(att.getValue()))
                                    .collect(Collectors.toList());

                    String txnFraudMerchantValue = null;
                    String mccCodeFraudValue = null;
                    if (StringUtils.isNotEmpty(transaction.getTxnMid()) &&
                            StringUtils.isNotEmpty(transaction.getTxnTermowner())) {
                        CheckFraudRes res =
                                transactionClient.checkFraud(ACTION_EARN,
                                        loyaltyCusId,
                                        String.format("%s_%s", transaction.getTxnMid(), transaction.getTxnTermowner()),
                                        transaction.getTxnMcc(),
                                        requestId
                                );
                        Map<String, String> fraudErrorCodes = extractFraudErrorCode(res);
                        txnFraudMerchantValue = fraudErrorCodes.get(FRAUD_MID_TERMOWNER_KEY);
                        mccCodeFraudValue = fraudErrorCodes.get(FRAUD_MCC_KEY);
                    }
                    if (StringUtils.isNotBlank(txnFraudMerchantValue)) {
                        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_FRAUD_MERCHANT", txnFraudMerchantValue));
                    }

                    if (StringUtils.isNotEmpty(mccCodeFraudValue)) {
                        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_FRAUD_MCC", mccCodeFraudValue));
                    }

                    produceEventForGami(transaction.getLoyaltyCusId(), transaction, attributes);

                    TransactionEarnServiceReq earnReq = buildEarnRequest(loyaltyCusId, transaction, attributes);

                    TransactionEarnRes earnRes = callApiEarnV2(earnReq, requestId);
                    serviceErrorCode = earnRes.getMeta().getCode();
                    serviceErrorMessage = earnRes.getMeta().getMessage();
                    transaction.setEarnRes(earnRes);

                    Log.info(LogData.createLogData()
                            .append("msg", "EarnTransactionHandlerImpl -- Process earning was successful.")
                            .append("txn_type", "POSTED")
                            .append("transaction_id", transaction.getTransactionId())
                            .append("request_id", requestId)
                            .append("loyalty_cus_id", loyaltyCusId)
                    );
                }
                /*
                 *  Process Refund transaction
                 */
                else if (TxnStatus.REFUND.equals(transaction.getTxnStatus())) {
                    if (StringUtils.isEmpty(transaction.getTxnOriginalId())) {
                        TransactionAdjustmentSchemeV2Req refundReq = buildRefundRequest(transaction.getLoyaltyCusId(), transaction);
                        TransactionAdjustmentSchemeV2Res refundRes = callApiRefundV2(refundReq, requestId);
                        serviceErrorCode = refundRes.getMeta().getCode();
                        serviceErrorMessage = refundRes.getMeta().getMessage();
                        transaction.setRefundRes(refundRes);
                        Log.info(LogData.createLogData()
                                .append("msg", "EarnTransactionHandlerImpl -- Process refund was successful.")
                                .append("txn_type", "REFUND_WITHOUT_ORIGINAL_TRANSACTION")
                                .append("transaction_id", transaction.getTransactionId())
                                .append("original_transaction_id", transaction.getTxnOriginalId())
                                .append("request_id", requestId)
                                .append("loyalty_cus_id", loyaltyCusId)
                        );
                    } else {
                        TransactionReverseV2Req reverseReq = buildReverseRequest(transaction.getLoyaltyCusId(), transaction);
                        TransactionReverseV2Res reverseRes = callApiReverseV2(reverseReq, requestId);
                        serviceErrorCode = reverseRes.getMeta().getCode();
                        serviceErrorMessage = reverseRes.getMeta().getMessage();
                        transaction.setReverseRes(reverseRes);
                        Log.info(LogData.createLogData()
                                .append("msg", "EarnTransactionHandlerImpl -- Process refund was successful.")
                                .append("txn_type", "REFUND_WITH_ORIGINAL_TRANSACTION")
                                .append("transaction_id", transaction.getTransactionId())
                                .append("original_transaction_id", transaction.getTxnOriginalId())
                                .append("request_id", requestId)
                                .append("loyalty_cus_id", loyaltyCusId)
                        );
                    }
                }
                /*
                 * Process revoke escrow point
                 */
                else if (TxnStatus.REVERT.equals(transaction.getTxnStatus())) {
                    RevokeEscrowPointReq revokeReq = buildRevokeEscrowPointRequest(transaction.getLoyaltyCusId(), transaction);
                    RevokeEscrowPointV2Res revokeRes = callApiRevokeEscrowPoint(revokeReq, requestId);
                    serviceErrorCode = revokeRes.getMeta().getCode();
                    serviceErrorMessage = revokeRes.getMeta().getMessage();
                    transaction.setRevokeRes(revokeRes);

                    Log.info(LogData.createLogData()
                            .append("msg", "EarnTransactionHandlerImpl -- Process revoke escrow point was successful.")
                            .append("txn_type", "REVOKE_ESCROW_POINT")
                            .append("transaction_id", transaction.getTransactionId())
                            .append("request_id", requestId)
                            .append("loyalty_cus_id", loyaltyCusId)
                    );
                }
            } catch (BusinessException e) {
                serviceErrorCode = e.getErrCode();
                serviceErrorMessage = e.getMessage();
                Log.warn(LogData.createLogData()
                        .append("msg", "EarnTransactionHandlerImpl – Transaction processing was unsuccessful.")
                        .append("err", e.getMessage())
                        .append("transaction_id", transaction.getTransactionId())
                        .append("request_id", requestId)
                        .append("loyalty_cus_id", transaction.getLoyaltyCusId())
                );
            } catch (Exception e) {
                serviceErrorCode = ServiceErrorCode.SERVER_ERROR.getValue();
                serviceErrorMessage = e.getMessage();
                Log.error(LogData.createLogData()
                        .append("msg", "EarnTransactionHandlerImpl -- Exception while processing transaction.")
                        .append("err", e.getMessage())
                        .append("transaction_id", transaction.getTransactionId())
                        .append("request_id", requestId)
                        .append("loyalty_cus_id", transaction.getLoyaltyCusId())
                );
            } finally {
                transaction.setServiceError(ServiceError.builder()
                        .code(String.valueOf(serviceErrorCode))
                        .message(serviceErrorMessage)
                        .build());
                transaction.setErrorCode(EErrorCode.convert(serviceErrorCode));
            }
        } else {
            Log.error(LogData.createLogData()
                    .append("msg", "EarnTransactionHandlerImpl -- Transaction payload is invalid format.")
                    .append("error", transaction.getErrorCode().getMessage())
                    .append("transaction_id", transaction.getTransactionId())
                    .append("request_id", requestId)
                    .append("loyalty_cus_id", transaction.getLoyaltyCusId())
            );
        }


        KafkaAuditTrailEarnTransaction trailEarnTransaction = insertAuditTrail(processData, transaction, requestId);

        insertAuditTrailRetry(trailEarnTransaction);
    }

    private RevokeEscrowPointV2Res callApiRevokeEscrowPoint(RevokeEscrowPointReq revokeReq, String requestId) {
        return RetryHelper.executeWithRetry(
                retryTemplate,
                () -> escrowPointClient.revoke(revokeReq, requestId),
                requestId,
                "CallApiRevokeEscrowPoint"
        );
    }

    private TransactionReverseV2Res callApiReverseV2(TransactionReverseV2Req reverseReq, String requestId) {
        return RetryHelper.executeWithRetry(
                retryTemplate,
                () -> serviceClient.reverseV2(reverseReq, requestId),
                requestId,
                "CallApiReverseV2"
        );
    }

    private TransactionAdjustmentSchemeV2Res callApiRefundV2(TransactionAdjustmentSchemeV2Req refundReq, String requestId) {
        return RetryHelper.executeWithRetry(
                retryTemplate,
                () -> serviceClient.adjustmentSchemeV2(refundReq, requestId),
                requestId,
                "CallApiRefundV2"
        );
    }

    private TransactionEarnRes callApiEarnV2(TransactionEarnServiceReq earnReq, String requestId) {
        return RetryHelper.executeWithRetry(
                retryTemplate,
                () -> serviceClient.earnV2(earnReq, requestId),
                requestId,
                "CallApiEarnV2"
        );
    }

    @Override
    public boolean isProcessed(ProcessData<TransactionData> processData) {
        TransactionData txnData = processData.getDataObj();
        Date dsPartitionDate =
                StringUtils.isNotBlank(txnData.getDsPartitionDate()) ?
                        DateConverter.toDateFormatterYYYYMMDD(txnData.getDsPartitionDate()) : null;
        List<KafkaAuditTrailEarnTransaction> trailEarnTransactionList =
                kafkaAuditTrailTransEarnRepository.findByLoyaltyCusIdAndTransactionIdAndFileNameAndDsPartitionDate(txnData.getLoyaltyCusId(), txnData.getTransactionId(),
                        processData.getFileName(), dsPartitionDate);

        if (!trailEarnTransactionList.isEmpty()) {
            txnData.setErrorCode(EErrorCode.of(trailEarnTransactionList.get(0).getErrorCode()));
        }

        return !trailEarnTransactionList.isEmpty();
    }

    private void verifyMessage(TransactionData txnData) {

        EErrorCode maxLengthError = validMaxLength(txnData);

        if (maxLengthError != null) {
            txnData.setErrorCode(maxLengthError);
            return;
        }

        if (StringUtils.isEmpty(txnData.getLoyaltyCusId())) {
            txnData.setErrorCode(EErrorCode.LOYALTY_CUS_ID_CANNOT_EMPTY);
            return;
        }

        EErrorCode errorCode = verifyTxn(txnData);
        txnData.setErrorCode(errorCode);
    }

    private EErrorCode validMaxLength(TransactionData txnData) {
        EErrorCode errorCode = null;

        if (Objects.nonNull(txnData)) {
            // LOYALTY_CUS_ID
            if (!LengthValidationUtil.isValidString(txnData.getLoyaltyCusId(), LENGTH_255)) {
                txnData.setLoyaltyCusId(LengthValidationUtil.trimString(txnData.getLoyaltyCusId(), LENGTH_255));
                errorCode = EErrorCode.LOYALTY_CUS_ID_LENGTH_INVALID;
            }

            // TRANSACTION_ID
            if (!LengthValidationUtil.isValidString(txnData.getTransactionId(), LENGTH_1000)) {
                txnData.setTransactionId(LengthValidationUtil.trimString(txnData.getTransactionId(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TRANSACTION_ID_LENGTH_INVALID;
                }
            }

            // TXN_CODE
            if (!LengthValidationUtil.isValidString(txnData.getTxnCode(), LENGTH_1000)) {
                txnData.setTxnCode(LengthValidationUtil.trimString(txnData.getTxnCode(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_CODE_LENGTH_INVALID;
                }
            }

            // ARRANGEMENT_ID
            if (!LengthValidationUtil.isValidString(txnData.getArrangementId(), LENGTH_1000)) {
                txnData.setArrangementId(LengthValidationUtil.trimString(txnData.getArrangementId(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.ARRANGEMENT_ID_LENGTH_INVALID;
                }
            }


            // TXN_PRODUCT_FAMILY
            if (!LengthValidationUtil.isValidString(txnData.getTxnProductFamily(), LENGTH_1000)) {
                txnData.setTxnProductFamily(LengthValidationUtil.trimString(txnData.getTxnProductFamily(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_PRODUCT_FAMILY_LENGTH_INVALID;
                }
            }

            // TXN_PRODUCT_LINE
            if (!LengthValidationUtil.isValidString(txnData.getTxnProductLine(), LENGTH_1000)) {
                txnData.setTxnProductLine(LengthValidationUtil.trimString(txnData.getTxnProductLine(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_PRODUCT_LINE_LENGTH_INVALID;
                }
            }

            // TXN_IN_OUT
            if (!LengthValidationUtil.isValidString(txnData.getTxnInOut(), LENGTH_1000)) {
                txnData.setTxnInOut(LengthValidationUtil.trimString(txnData.getTxnInOut(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_IN_OUT_LENGTH_INVALID;
                }
            }

            // TXN_CHANNEL_LV1
            if (!LengthValidationUtil.isValidString(txnData.getTxnChannelLv1(), LENGTH_1000)) {
                txnData.setTxnChannelLv1(LengthValidationUtil.trimString(txnData.getTxnChannelLv1(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_CHANNEL_LV1_LENGTH_INVALID;
                }
            }

            // TXN_CHANNEL_LV2
            if (!LengthValidationUtil.isValidString(txnData.getTxnChannelLv2(), LENGTH_1000)) {
                txnData.setTxnChannelLv2(LengthValidationUtil.trimString(txnData.getTxnChannelLv2(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_CHANNEL_LV2_LENGTH_INVALID;
                }
            }

            // TXN_CHANNEL_LV3
            if (!LengthValidationUtil.isValidString(txnData.getTxnChannelLv3(), LENGTH_1000)) {
                txnData.setTxnChannelLv3(LengthValidationUtil.trimString(txnData.getTxnChannelLv3(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_CHANNEL_LV3_LENGTH_INVALID;
                }
            }

            // TXN_MOTHER_CATEGORY
            if (!LengthValidationUtil.isValidString(txnData.getTxnMotherCategory(), LENGTH_1000)) {
                txnData.setTxnMotherCategory(LengthValidationUtil.trimString(txnData.getTxnMotherCategory(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_MOTHER_CATEGORY_LENGTH_INVALID;
                }
            }

            // TXN_MCC
            if (!LengthValidationUtil.isValidString(txnData.getTxnMcc(), LENGTH_1000)) {
                txnData.setTxnMcc(LengthValidationUtil.trimString(txnData.getTxnMcc(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_MCC_LENGTH_INVALID;
                }
            }

            // TXN_SERVICE_LV1
            if (!LengthValidationUtil.isValidString(txnData.getTxnServiceLv1(), LENGTH_1000)) {
                txnData.setTxnServiceLv1(LengthValidationUtil.trimString(txnData.getTxnServiceLv1(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_SERVICE_LV1_LENGTH_INVALID;
                }
            }

            // TXN_SERVICE_LV2
            if (!LengthValidationUtil.isValidString(txnData.getTxnServiceLv2(), LENGTH_1000)) {
                txnData.setTxnServiceLv2(LengthValidationUtil.trimString(txnData.getTxnServiceLv2(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_SERVICE_LV2_LENGTH_INVALID;
                }
            }

            // TXN_CATEGORY_GROUP
            if (!LengthValidationUtil.isValidString(txnData.getTxnCategoryGroup(), LENGTH_1000)) {
                txnData.setTxnCategoryGroup(LengthValidationUtil.trimString(txnData.getTxnCategoryGroup(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_CATEGORY_GROUP_LENGTH_INVALID;
                }
            }

            // TXN_PURPOSE
            if (!LengthValidationUtil.isValidString(txnData.getTxnPurpose(), LENGTH_1000)) {
                txnData.setTxnPurpose(LengthValidationUtil.trimString(txnData.getTxnPurpose(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_PURPOSE_LENGTH_INVALID;
                }
            }

            // TXN_MERCHANT_PURPOSE
            if (!LengthValidationUtil.isValidString(txnData.getTxnMerchantPurpose(), LENGTH_1000)) {
                txnData.setTxnMerchantPurpose(LengthValidationUtil.trimString(txnData.getTxnMerchantPurpose(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_MERCHANT_PURPOSE_LENGTH_INVALID;
                }
            }

            // TXN_SUPPLIER
            if (!LengthValidationUtil.isValidString(txnData.getTxnSupplier(), LENGTH_1000)) {
                txnData.setTxnSupplier(LengthValidationUtil.trimString(txnData.getTxnSupplier(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_SUPPLIER_LENGTH_INVALID;
                }
            }

            // TXN_CCY_CODE
            if (!LengthValidationUtil.isValidString(txnData.getTxnCcyCode(), LENGTH_1000)) {
                txnData.setTxnCcyCode(LengthValidationUtil.trimString(txnData.getTxnCcyCode(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_CCY_CODE_LENGTH_INVALID;
                }
            }

            // TXN_ADJ_REASON
            if (!LengthValidationUtil.isValidString(txnData.getTxnAdjReason(), LENGTH_1000)) {
                txnData.setTxnAdjReason(LengthValidationUtil.trimString(txnData.getTxnAdjReason(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_ADJ_REASON_LENGTH_INVALID;
                }
            }

            // TXN_SLR_IND
            if (!LengthValidationUtil.isValidString(txnData.getTxnSlrInd(), LENGTH_1000)) {
                txnData.setTxnSlrInd(LengthValidationUtil.trimString(txnData.getTxnSlrInd(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_SLR_IND_LENGTH_INVALID;
                }
            }


            // TXN_GROSS_AMT
            if (!LengthValidationUtil.isValidString(txnData.getTxnGrossAmt(), LENGTH_1000)) {
                txnData.setTxnGrossAmt(LengthValidationUtil.trimString(txnData.getTxnGrossAmt(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_GROSS_AMT_LENGTH_INVALID;
                }
            }

            // TXN_NET_AMT
            if (!LengthValidationUtil.isValidString(txnData.getTxnNetAmt(), LENGTH_1000)) {
                txnData.setTxnNetAmt(LengthValidationUtil.trimString(txnData.getTxnNetAmt(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_NET_AMT_LENGTH_INVALID;
                }
            }

            // TXN_TERMINALNAME
            if (!LengthValidationUtil.isValidString(txnData.getTxnTerminalname(), LENGTH_1000)) {
                txnData.setTxnTerminalname(LengthValidationUtil.trimString(txnData.getTxnTerminalname(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_TERMINALNAME_LENGTH_INVALID;
                }
            }

            // TXN_BRANDNAME
            if (!LengthValidationUtil.isValidString(txnData.getTxnBrandname(), LENGTH_1000)) {
                txnData.setTxnBrandname(LengthValidationUtil.trimString(txnData.getTxnBrandname(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_BRANDNAME_LENGTH_INVALID;
                }
            }

            // TXN_TID
            if (!LengthValidationUtil.isValidString(txnData.getTxnTid(), LENGTH_1000)) {
                txnData.setTxnTid(LengthValidationUtil.trimString(txnData.getTxnTid(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_TID_LENGTH_INVALID;
                }
            }

            // TXN_MID
            if (!LengthValidationUtil.isValidString(txnData.getTxnMid(), LENGTH_1000)) {
                txnData.setTxnMid(LengthValidationUtil.trimString(txnData.getTxnMid(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_MID_LENGTH_INVALID;
                }
            }

            // TXN_TERMLOCATION
            if (!LengthValidationUtil.isValidString(txnData.getTxnTermlocation(), LENGTH_1000)) {
                txnData.setTxnTermlocation(LengthValidationUtil.trimString(txnData.getTxnTermlocation(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_TERMLOCATION_LENGTH_INVALID;
                }
            }

            // TXN_FOREIGN_TXN_AMT
            if (!LengthValidationUtil.isValidString(txnData.getTxnForeignTxnAmt(), LENGTH_1000)) {
                txnData.setTxnForeignTxnAmt(LengthValidationUtil.trimString(txnData.getTxnForeignTxnAmt(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_FOREIGN_AMT_LENGTH_INVALID;
                }
            }

            // TXN_FOREIGN_EXC_RATE
            if (!LengthValidationUtil.isValidString(txnData.getTxnForeignExcRate(), LENGTH_1000)) {
                txnData.setTxnForeignExcRate(LengthValidationUtil.trimString(txnData.getTxnForeignExcRate(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_FOREIGN_EXC_RATE_LENGTH_INVALID;
                }
            }

            // TXN_STATUS
            if (!LengthValidationUtil.isValidString(txnData.getTxnStatus(), LENGTH_1000)) {
                txnData.setTxnStatus(LengthValidationUtil.trimString(txnData.getTxnStatus(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_STATUS_CANNOT_EMPTY;
                }
            }

            // TXN_ADJ_SIGN
            if (!LengthValidationUtil.isValidString(txnData.getTxnAdjSign(), LENGTH_1000)) {
                txnData.setTxnAdjSign(LengthValidationUtil.trimString(txnData.getTxnAdjSign(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_ADJ_SIGN_LENGTH_INVALID;
                }
            }

            // TXN_ORIGINAL_ID
            if (!LengthValidationUtil.isValidString(txnData.getTxnOriginalId(), LENGTH_1000)) {
                txnData.setTxnOriginalId(LengthValidationUtil.trimString(txnData.getTxnOriginalId(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_ORIGINAL_ID_LENGTH_INVALID;
                }
            }

            // TXN_APPCODE
            if (!LengthValidationUtil.isValidString(txnData.getTxnAppcode(), LENGTH_1000)) {
                txnData.setTxnAppcode(LengthValidationUtil.trimString(txnData.getTxnAppcode(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_APPCODE_LENGTH_INVALID;
                }
            }

            // TXN_TOKEN
            if (!LengthValidationUtil.isValidString(txnData.getTxnToken(), LENGTH_1000)) {
                txnData.setTxnToken(LengthValidationUtil.trimString(txnData.getTxnToken(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_TOKEN_LENGTH_INVALID;
                }
            }

            // TXN_BIN
            if (!LengthValidationUtil.isValidString(txnData.getTxnBin(), LENGTH_1000)) {
                txnData.setTxnBin(LengthValidationUtil.trimString(txnData.getTxnBin(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_BIN_LENGTH_INVALID;
                }
            }

            // TXN_DATE
            if (!LengthValidationUtil.isValidString(txnData.getTxnDate(), LENGTH_1000)) {
                txnData.setTxnDate(LengthValidationUtil.trimString(txnData.getTxnDate(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_DATE_LENGTH_INVALID;
                }
            }

            // TXN_TYPE
            if (!LengthValidationUtil.isValidString(txnData.getTxnType(), LENGTH_1000)) {
                txnData.setTxnType(LengthValidationUtil.trimString(txnData.getTxnType(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_TYPE_LENGTH_INVALID;
                }
            }

            // TXN_TERMOWNER
            if (!LengthValidationUtil.isValidString(txnData.getTxnTermowner(), LENGTH_1000)) {
                txnData.setTxnTermowner(LengthValidationUtil.trimString(txnData.getTxnTermowner(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_TERMOWNER_LENGTH_INVALID;
                }
            }

            // TXN_SETTLEMENT_DATE
            if (!LengthValidationUtil.isValidString(txnData.getTxnSettlementDate(), LENGTH_1000)) {
                txnData.setTxnSettlementDate(LengthValidationUtil.trimString(txnData.getTxnSettlementDate(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_SETTLEMENT_DATE_LENGTH_INVALID;
                }
            }

            // TXN_POST_DATE
            if (!LengthValidationUtil.isValidString(txnData.getTxnPostDate(), LENGTH_1000)) {
                txnData.setTxnPostDate(LengthValidationUtil.trimString(txnData.getTxnPostDate(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_POST_DATE_LENGTH_INVALID;
                }
            }

            // TXN_QR_MERCHANT_IND
            if (!LengthValidationUtil.isValidString(txnData.getTxnQrMerchantInd(), LENGTH_1000)) {
                txnData.setTxnQrMerchantInd(LengthValidationUtil.trimString(txnData.getTxnQrMerchantInd(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_QR_MERCHANT_IND_LENGTH_INVALID;
                }
            }

            // TXN_FOREIGN_CCY_CODE
            if (!LengthValidationUtil.isValidString(txnData.getTxnForeignCcyCode(), LENGTH_1000)) {
                txnData.setTxnForeignCcyCode(LengthValidationUtil.trimString(txnData.getTxnForeignCcyCode(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_FOREIGN_CCY_CODE_LENGTH_INVALID;
                }
            }

            // TXN_BOUNDARY
            if (!LengthValidationUtil.isValidString(txnData.getTxnBoundary(), LENGTH_1000)) {
                txnData.setTxnBoundary(LengthValidationUtil.trimString(txnData.getTxnBoundary(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_BOUNDARY_LENGTH_INVALID;
                }
            }

            // TXN_SUB_TYPE
            if (!LengthValidationUtil.isValidString(txnData.getTxnSubType(), LENGTH_1000)) {
                txnData.setTxnSubType(LengthValidationUtil.trimString(txnData.getTxnSubType(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_SUB_TYPE_LENGTH_INVALID;
                }
            }

            // TXN_CHANNEL_LV4
            if (!LengthValidationUtil.isValidString(txnData.getTxnChannelLv4(), LENGTH_1000)) {
                txnData.setTxnChannelLv4(LengthValidationUtil.trimString(txnData.getTxnChannelLv4(), LENGTH_1000));
                if (errorCode == null) {
                    errorCode = EErrorCode.TXN_CHANNEL_LV4_LENGTH_INVALID;
                }
            }
        }

        return errorCode;
    }

    private EErrorCode verifyTxn(TransactionData txnData) {

        if (StringUtils.isEmpty(txnData.getTransactionId())) {
            return EErrorCode.TRANSACTION_ID_CANNOT_EMPTY;
        }

        if (StringUtils.isEmpty(txnData.getArrangementId())) {
            return EErrorCode.ARRANGEMENT_ID_CANNOT_EMPTY;
        }

        if (StringUtils.isEmpty(txnData.getTxnCode())) {
            return EErrorCode.TXN_CODE_CANNOT_EMPTY;
        }

        if (StringUtils.isEmpty(txnData.getTxnType())) {
            return EErrorCode.TXN_TYPE_CANNOT_EMPTY;
        }

        if (StringUtils.isEmpty(txnData.getTxnCcyCode())) {
            return EErrorCode.TXN_CCY_CODE_CANNOT_EMPTY;
        }

        if (StringUtils.isEmpty(txnData.getTxnDate())) {
            return EErrorCode.TXN_DATE_CANNOT_EMPTY;
        }

        if (!DateConverter.isValidFormat(txnData.getTxnDate(), "dd-MMM-yyyy")) {
            return EErrorCode.TXN_DATE_INVALID;
        }

        if (!StringUtils.isEmpty(txnData.getTxnSlrInd()) &&
                Objects.isNull(EBoolean.of(txnData.getTxnSlrInd()))) {
            return EErrorCode.TXN_SLR_IND_INVALID;
        }

        if (StringUtils.isEmpty(txnData.getTxnTime())) {
            return EErrorCode.TXN_TIME_CANNOT_EMPTY;
        }

        if (!DateTimeConverter.isValidFormat(txnData.getTxnTime(), "HH:mm:ss")) {
            return EErrorCode.TXN_TIME_INVALID;
        }

        if (StringUtils.isEmpty(txnData.getTxnGrossAmt())) {
            return EErrorCode.TXN_GROSS_AMT_CANNOT_EMPTY;
        }

        if (!LengthValidationUtil.isValidBigDecimal(txnData.getTxnGrossAmt())) {
            return EErrorCode.TXN_GROSS_AMT_INVALID;
        }

        if (StringUtils.isEmpty(txnData.getTxnNetAmt())) {
            return EErrorCode.TXN_NET_AMT_CANNOT_EMPTY;
        }

        if (!LengthValidationUtil.isValidBigDecimal(txnData.getTxnNetAmt())) {
            return EErrorCode.TXN_NET_AMT_INVALID;
        }

        if (StringUtils.isEmpty(txnData.getTxnInOut())) {
            return EErrorCode.TXN_IN_OUT_CANNOT_EMPTY;
        }

        if (StringUtils.isEmpty(txnData.getTxnSettlementDate())) {
            return EErrorCode.TXN_SETTLEMENT_DATE_CANNOT_EMPTY;
        }

        if (StringUtils.isEmpty(txnData.getTxnPostDate())) {
            return EErrorCode.TXN_POST_DATE_CANNOT_EMPTY;
        }


        if (StringUtils.isEmpty(txnData.getTxnStatus())) {
            return EErrorCode.TXN_STATUS_CANNOT_EMPTY;
        }

        if (!VALID_TXN_STATUSES.contains(txnData.getTxnStatus())) {
            return EErrorCode.TXN_STATUS_INVALID;
        }

        if (!DateConverter.isValidFormat(txnData.getTxnSettlementDate(), "dd-MMM-yyyy")) {
            return EErrorCode.TXN_SETTLEMENT_DATE_INVALID;
        }

        if (!DateConverter.isValidFormat(txnData.getTxnPostDate(), "dd-MMM-yyyy")) {
            return EErrorCode.TXN_POST_DATE_INVALID;
        }

        if (StringUtils.isEmpty(txnData.getTxnQrMerchantInd())) {
            return EErrorCode.TXN_QR_MERCHANT_IND_CANNOT_EMPTY;
        }

        if (Objects.isNull(EBoolean.of(txnData.getTxnQrMerchantInd()))) {
            return EErrorCode.TXN_QR_MERCHANT_IND_INVALID;
        }

        return null;
    }

    private List<TransactionAttributeReq> buildRequestAttributes(
            String loyaltyCusId,
            TransactionData payload
    ) {
        List<TransactionAttributeReq> attributes = new ArrayList<>();
        CustomerProductInfo product = null;
        CustomerCardInfo card = null;
        Date txnTime = payload.getTxnDateTime();

        /*
            Customer Product info
         */
        if (!StringUtils.isEmpty(payload.getArrangementId())) {
            product = customerProductInfoService.getCustomerProductInfo(loyaltyCusId, payload.getArrangementId(), txnTime);
        }

        /*
            Customer card info
         */
        if (!StringUtils.isEmpty(payload.getCardId())) {
            card = customerCardInfoService.getCustomerCardInfo(loyaltyCusId, payload.getCardId(), txnTime);
        }

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CODE", payload.getTxnCode()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_ARRANGEMENT_ID", payload.getArrangementId()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CARD_ID", payload.getCardId()));

        if (Objects.nonNull(product)) {
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRODUCT_CODE", product.getProductCode()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRODUCT_LV1", product.getProductLv1()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRODUCT_LV2", product.getProductLv2()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRODUCT_LV3", product.getProductLv3()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRODUCT_LV4", product.getProductLv4()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRODUCT_LV5", product.getProductLv5()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRODUCT_LV6", product.getProductLv6()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRODUCT_BRANCH_CODE", product.getPrdBranchCode()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.NUMBER, "TXN_PRODUCT_HOLDING_NBR", nullSaferValueOf(product.getPrdHoldingNbr())));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRD_STATUS", product.getPrdStatus()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRD_TENOR", product.getPrdTenor()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_PRD_BILL_CYCLE", product.getPrdBillCycle()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_PRD_ROLLOVER_DATE", product.getPrdRolloverDate()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRD_COMBO", product.getPrdCombo()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_PROD_OPEN_DATE", product.getProdOpenDate()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_PROD_EXPIRE_DATE", product.getProdExpireDate()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRD_BRANCH_CODE", product.getPrdBranchCode()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRD_CCY_CODE", product.getPrdCcyCode()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRD_ACC_ARR_ID", product.getPrdAccArrId()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRD_JOINT_HOLDER_IND", product.getPrdJointHolderInd()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRD_AUTO_BILL_IND", product.getPrdAutoBillInd()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRD_SALES_CHANNEL", product.getPrdSalesChannel()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRD_INSURANCE_RENEW", product.getPrdInsuranceRenew()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRD_AUTO_ROLL_IND", product.getPrdAutoRollInd()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PRD_ROLL", product.getPrdRoll()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.NUMBER, "TXN_PRD_TD_AMOUNT", nullSaferValueOf(product.getPrdTdAmount())));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_PRD_ISSUE_DATE", product.getPrdIssueDate()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_PRD_PAYMENT_DATE", product.getPrdPaymentDate()));

            // Customize for mobile app
            ProductCode productCode = productCodeRepository.findFirstByCode(product.getProductCode());
            if (Objects.nonNull(productCode)) {
                attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "PRODUCT_CODE", product.getProductCode()));
                attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "PRODUCT_NAME", productCode.getProductName()));
                attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "PRODUCT_NAME_VI", productCode.getProductNameVi()));
            }
        }

        if (Objects.nonNull(card)) {
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CARD_IDENTIFIER", card.getCardIdentifier()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CARD_IND", card.getCardInd()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_CARD_EXPIRE_DATE", card.getCardExpireDate()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_CARD_OPEN_DATE", card.getCardOpenDate()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_CARD_ACTIVATED_DATE", card.getCardActivatedDate()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CARD_STATUS", card.getCardStatus()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CARD_PAN", card.getCardPan()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_CARD_ANNUAL_FEE_PAYMENT", card.getCardAnnualFeePayment()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_CARD_BILL_CYCLE_DATE", card.getCardBillCycleDate()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_CARD_PAYMENT_DATE", card.getCardPaymentDate()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.NUMBER, "TXN_CARD_TERM", nullSaferValueOf(card.getCardTerm())));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.NUMBER, "TXN_CARD_LIMIT", nullSaferValueOf(card.getCardLimit())));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CARD_TYPE", card.getCardType()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CARD_ISSUE_TYPE", card.getCardIssueType()));
            attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CARD_NEW_ACQUISITION_IND", card.getCardNewAcquisitionInd()));
        }

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_IN_OUT", payload.getTxnInOut()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CHANNEL_LV1", payload.getTxnChannelLv1()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CHANNEL_LV2", payload.getTxnChannelLv2()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CHANNEL_LV3", payload.getTxnChannelLv3()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_MOTHER_CATEGORY", payload.getTxnMotherCategory()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_MCC", payload.getTxnMcc()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_SERVICE_LV1", payload.getTxnServiceLv1()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_SERVICE_LV2", payload.getTxnServiceLv2()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CATEGORY_GROUP", payload.getTxnCategoryGroup()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CCY_CODE", payload.getTxnCcyCode()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_ADJ_REASON", payload.getTxnAdjReason()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_PURPOSE", payload.getTxnPurpose()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_MERCHANT_PURPOSE", payload.getTxnMerchantPurpose()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_SUPPLIER", payload.getTxnSupplier()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_SLR_IND", payload.getTxnSlrInd()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_RECURRING_IND", payload.getTxnRecurringInd()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CARD_COMBO", payload.getTxnCardCombo()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.NUMBER, "TXN_GROSS_AMOUNT", payload.getTxnGrossAmt()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.NUMBER, "TXN_NET_AMOUNT", payload.getTxnNetAmt()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_RANK_CHECK", payload.getTxnRankCheck()));

        String txnBrandName = payload.getTxnBrandname();
        if (StringUtils.isBlank(txnBrandName)) {
            txnBrandName = null;
        }
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_BRANDNAME", txnBrandName));

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_TID", payload.getTxnTid()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_MID", payload.getTxnMid()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_TERMLOCATION", payload.getTxnTermlocation()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.NUMBER, "TXN_FOREIGN_TXN_AMT", payload.getTxnForeignTxnAmt()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.NUMBER, "TXN_FOREIGN_EXC_RATE", payload.getTxnForeignExcRate()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_STATUS", payload.getTxnStatus()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_ADJ_SIGN", payload.getTxnAdjSign()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_ORIGINAL_ID", payload.getTxnOriginalId()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_APPCODE", payload.getTxnAppcode()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_TOKEN", payload.getTxnToken()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_BIN", payload.getTxnBin()));

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_TERMOWNER", payload.getTxnTermowner()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_SETTLEMENT_DATE", payload.getTxnSettlementDate()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.DATE, "TXN_POST_DATE", payload.getTxnPostDate()));

        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_QR_MERCHANT_IND", payload.getTxnQrMerchantInd()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_FOREIGN_CCY_CODE", payload.getTxnForeignCcyCode()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_BOUNDARY", payload.getTxnBoundary()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_SUB_TYPE", payload.getTxnSubType()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_CHANNEL_LV4", payload.getTxnChannelLv4()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_SERVICE_TYPE", payload.getTxnServiceType()));
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "TXN_TYPE", payload.getTxnType()));

        EBoolean isEndOfMonth = isEndOfMonth(payload.getTxnDate());
        attributes.add(AttributeConverter.toTxnAttribute(EAttributeType.TEXT, "IS_END_OF_MONTH", isEndOfMonth.getValue()));

        return attributes;
    }

    private String nullSaferValueOf(BigDecimal bigDecimal) {
        return Objects.nonNull(bigDecimal) ? bigDecimal.toString() : null;
    }

    /*
        Produce event for Gami
    */
    public void produceEventForGami(
            String loyaltyCusId,
            TransactionData txn,
            List<TransactionAttributeReq> attributes
    ) {
        if (enableEventGami) {
            GamiEarnTransactionProduce.EventGami eventGami = new GamiEarnTransactionProduce.EventGami();
            eventGami.setId();
            eventGami.setUserId(loyaltyCusId);
            eventGami.setLoyaltyCusId(loyaltyCusId);
            eventGami.setTransactionId(txn.getTransactionId());
            eventGami.setBusinessCode(this.businessCode);
            eventGami.setProgramCode(this.programCode);
            eventGami.getPayloadGami().setCustomerIdentifier(toCustomerIdentify(loyaltyCusId));
            eventGami.getPayloadGami().setAttributes(attributes);
            eventGami.getPayloadGami().setTransactionDatetime(txn.getTxnDate() + " " + txn.getTxnTime());

            ProduceService.ProduceMessage produceMessage = new ProduceService.ProduceMessage();
            produceMessage.setKey(loyaltyCusId);
            produceMessage.setValue(eventGami);
            produceService.produceAsync(produceMessage, gamiTxnTopic, "");
            Log.info(LogData.createLogData()
                    .append("msg", "EarnTransactionHandlerImpl -- Producing event to Gami was successful.")
                    .append("transaction_id", txn.getTransactionId())
                    .append("loyalty_cus_id", eventGami.getLoyaltyCusId())
            );
        }
    }

    private CustomerIdentify toCustomerIdentify(String loyaltyCusId) {
        CustomerIdentify identify = new CustomerIdentify();
        identify.setId(loyaltyCusId);
        identify.setIdType(EIdType.PARTNER_CUSTOMER_ID);
        return identify;
    }

    private TransactionReverseV2Req buildReverseRequest(String loyaltyCusId, TransactionData txnData) {
        BigDecimal grossAmount = new BigDecimal(txnData.getTxnGrossAmt());
        Long transactionTime = txnData.getTxnDateTimeEpoch();
        CustomerIdentify customerIdentify = toCustomerIdentify(loyaltyCusId);
        List<TransactionAttributeReq> attributes = buildRequestAttributes(loyaltyCusId, txnData)
                .stream().filter(att -> !StringUtils.isEmpty(att.getValue()))
                .collect(Collectors.toList());

        return TransactionReverseV2Req.builder()
                .customerIdentifier(customerIdentify)
                .originalInvoiceNo(txnData.getTxnOriginalId())
                .businessCode(businessCode)
                .programCode(programCode)
                .transactionCode(txnData.getTxnCode())
                .serviceCode(serviceCodeRefund)
                .currencyCode(currencyCode)
                .channelCode(channelCode)
                .reasonCode(null)
                .functionCode(null)
                .createdBy(null)
                .newTxnInfo(
                        TransactionReverseV2Req.NewTxnInfo.builder()
                                .corporationCode(corporationCode)
                                .posCode(posCode)
                                .invoiceNo(txnData.getTransactionId())
                                .transactionTime(transactionTime)
                                .description(null)
                                .refundAmount(grossAmount)
                                .redeemPoint(BigDecimal.ZERO)
                                .attributes(attributes)
                                .build())
                .build();
    }

    private TransactionAdjustmentSchemeV2Req buildRefundRequest(String loyaltyCusId, TransactionData txnData) {
        CustomerIdentify customerIdentify = toCustomerIdentify(loyaltyCusId);
        Long transactionTime = txnData.getTxnDateTimeEpoch();
        BigDecimal grossAmount = new BigDecimal(txnData.getTxnGrossAmt());
        List<TransactionAttributeReq> attributes = buildRequestAttributes(loyaltyCusId, txnData)
                .stream().filter(att -> !StringUtils.isEmpty(att.getValue()))
                .collect(Collectors.toList());

        return TransactionAdjustmentSchemeV2Req.builder()
                .customerIdentifier(customerIdentify)
                .businessCode(businessCode)
                .programCode(programCode)
                .currencyCode(currencyCode)
                .serviceCode(serviceCodeRefund)
                .channelCode(channelCode)
                .createdBy(null)
                .transaction(
                        TransactionAdjustmentSchemeV2Req.Transaction.builder()
                                .corporationCode(corporationCode)
                                .posCode(posCode)
                                .invoiceNo(txnData.getTransactionId())
                                .transactionTime(transactionTime)
                                .adjustType(EAdjustmentType.REDEEM)
                                .reasonCode(reasonCode)
                                .description(null)
                                .gmv(grossAmount)
                                .grossAmount(grossAmount)
                                .transactionCode(txnData.getTxnCode())
                                .attributes(attributes)
                                .build()
                )
                .build();
    }

    private TransactionEarnServiceReq buildEarnRequest(
            String loyaltyCusId,
            TransactionData txnData,
            List<TransactionAttributeReq> attributes
    ) {
        BigDecimal grossAmount = new BigDecimal(txnData.getTxnGrossAmt());
        Long transactionTime = txnData.getTxnDateTimeEpoch();
        CustomerIdentify customerIdentify = toCustomerIdentify(loyaltyCusId);

        return TransactionEarnServiceReq.builder()
                .customerIdentifier(customerIdentify)
                .businessCode(businessCode)
                .programCode(programCode)
                .currencyCode(currencyCode)
                .channelCode(channelCode)
                .serviceCode(serviceCodeEarnTxn)
                .enablePwpScheme(true)
                .transaction(
                        TransactionReq.builder()
                                .invoiceNo(txnData.getTransactionId())
                                .transactionCode(txnData.getTxnCode())
                                .gmv(grossAmount)
                                .grossAmount(grossAmount)
                                .transactionTime(transactionTime)
                                .description(txnData.getTxnCode())
                                .corporationCode(corporationCode)
                                .storeId(storeCode)
                                .posCode(posCode)
                                .attributes(attributes)
                                .build()
                )
                .build();
    }

    private RevokeEscrowPointReq buildRevokeEscrowPointRequest(String loyaltyCusId, TransactionData txnData) {
        BigDecimal grossAmount = new BigDecimal(txnData.getTxnGrossAmt());
        Long transactionTime = txnData.getTxnDateTimeEpoch();
        CustomerIdentify customerIdentify = toCustomerIdentify(loyaltyCusId);
        List<TransactionAttributeReq> attributes = buildRequestAttributes(loyaltyCusId, txnData)
                .stream().filter(att -> !StringUtils.isEmpty(att.getValue()))
                .collect(Collectors.toList());

        return RevokeEscrowPointReq.builder()
                .businessCode(businessCode)
                .programCode(programCode)
                .serviceCode(serviceCodeRevokeTxn)
                .channelCode(channelCode)
                .currencyCode(null)
                .reasonCode(null)
                .customerIdentifier(customerIdentify)
                .transaction(
                        RevokeEscrowPointReq.Transaction.builder()
                                .corporationCode(corporationCode)
                                .posCode(posCode)
                                .invoiceNo(txnData.getTransactionId())
                                .transactionTime(transactionTime)
                                .arrangementId(txnData.getArrangementId())
                                .description(null)
                                .transactionCode(txnData.getTxnCode())
                                .gmv(grossAmount)
                                .grossAmount(grossAmount)
                                .attributes(attributes)
                                .build()
                )
                .build();
    }

    // https://1squad.atlassian.net/wiki/spaces/TCBLOYAL/pages/620363824/API+Check+Fraud#
    private Map<String, String> extractFraudErrorCode(CheckFraudRes checkFraudRes) {
        Map<String, String> errCodesMap = new HashMap<>();
        if (checkFraudRes != null && checkFraudRes.getData() != null) {
            CheckFraudRes.DataDto data = checkFraudRes.getData();
            if (CollectionUtils.isNotEmpty(data.getReasons())) {
                List<CheckFraudRes.DataDto.Reason> reasons = data.getReasons();
                reasons.forEach(e -> {

                    // Term owner
                    if (FRAUD_MID_TERMOWNER_ERROR_CODES.contains(e.getReasonCode())) {
                        errCodesMap.put(FRAUD_MID_TERMOWNER_KEY, e.getReasonCode());
                    }

                    // MCC
                    if (FRAUD_MCC_ERROR_CODES.contains(e.getReasonCode())) {
                        errCodesMap.put(FRAUD_MCC_KEY, e.getReasonCode());
                    }
                });
            }
        }

        return errCodesMap;
    }

    private EBoolean isEndOfMonth(String txnDate) {
        String ddmmyyyy = DateConverter.toDDMMYYYY(txnDate);

        if (Objects.isNull(ddmmyyyy)) {
            return EBoolean.NO;
        }

        return endOfMonthDates.contains(ddmmyyyy) ? EBoolean.YES : EBoolean.NO;
    }

    private KafkaAuditTrailEarnTransaction insertAuditTrail(ProcessData<?> processData,
                                                            TransactionData transactionData,
                                                            String requestId
    ) {
        try {
            KafkaAuditTrailEarnTransaction auditTrail = new KafkaAuditTrailEarnTransaction();
            auditTrail.setCreatedAt(new Date());
            auditTrail.setMessageId(requestId);
            auditTrail.setMessageType(MessageType.TRANS_EARN_POINT_REQ.getValue());
            auditTrail.setBusinessCode(businessCode);
            auditTrail.setProgramCode(programCode);
            auditTrail.setCurrencyCode(currencyCode);
            auditTrail.setCorporationCode(corporationCode);
            auditTrail.setStoreCode(storeCode);
            auditTrail.setPosCode(posCode);
            auditTrail.setDsPartitionDate(StringUtils.isNotBlank(transactionData.getDsPartitionDate()) ? DateConverter.toDateFormatterYYYYMMDD(transactionData.getDsPartitionDate()) : null);
            auditTrail.setFileName(processData.getFileName());

            TransactionEarnRes earnRes = transactionData.getEarnRes();
            if (Objects.nonNull(earnRes) && Objects.nonNull(earnRes.getData()) && Objects.nonNull(earnRes.getData().getTransaction())) {
                auditTrail.setTxnRefNo(earnRes.getData().getTransaction().getTxnRefNo());
                auditTrail.setInvoiceNo(earnRes.getData().getTransaction().getInvoiceNo());
                auditTrail.setAwardPoint(earnRes.getData().getTransaction().getAwardedPoint());
                auditTrail.setTransactionTime(DateTimeConverter.toString(earnRes.getData().getTransaction().getTransactionTime()));
            }

            TransactionAdjustmentSchemeV2Res refundRes = transactionData.getRefundRes();
            if (Objects.nonNull(refundRes) && Objects.nonNull(refundRes.getData()) && Objects.nonNull(refundRes.getData().getTransaction())) {
                auditTrail.setTxnRefNo(refundRes.getData().getTransaction().getTxnRefNo());
                auditTrail.setInvoiceNo(refundRes.getData().getTransaction().getInvoiceNo());
                auditTrail.setTransactionTime(DateTimeConverter.toString(refundRes.getData().getTransaction().getTransactionTime()));
            }

            TransactionReverseV2Res reverseV2Res = transactionData.getReverseRes();
            if (Objects.nonNull(reverseV2Res) && Objects.nonNull(reverseV2Res.getData())) {
                if (Objects.nonNull(reverseV2Res.getData().getNewTxnInfo())) {
                    auditTrail.setTxnRefNo(reverseV2Res.getData().getNewTxnInfo().getTxnRefNo());
                    auditTrail.setInvoiceNo(reverseV2Res.getData().getNewTxnInfo().getInvoiceNo());
                    auditTrail.setTransactionTime(DateTimeConverter.toString(reverseV2Res.getData().getNewTxnInfo().getTransactionTime()));
                } else {
                    auditTrail.setTxnRefNo(reverseV2Res.getData().getOriginalTxnRef());
                    auditTrail.setInvoiceNo(transactionData.getTxnOriginalId());
                }
            }

            RevokeEscrowPointV2Res revokeRes = transactionData.getRevokeRes();
            if (Objects.nonNull(revokeRes) && Objects.nonNull(revokeRes.getData())) {
                auditTrail.setTxnRefNo(revokeRes.getData().getTransactionRef());
                auditTrail.setInvoiceNo(transactionData.getTransactionId());
                auditTrail.setTransactionTime(DateTimeConverter.toString(revokeRes.getData().getTransactionTime()));
            }

            ServiceError serviceError = transactionData.getServiceError();
            if (Objects.nonNull(serviceError)) {
                if (Objects.nonNull(serviceError.getCode())) {
                    auditTrail.setServiceErrorCode(serviceError.getCode());
                }
                if (Objects.nonNull(serviceError.getMessage())) {
                    auditTrail.setServiceErrorMessage(serviceError.getMessage());
                }
            }

            EErrorCode errorCode = null;
            if (Objects.nonNull(transactionData.getErrorCode())) {
                errorCode = transactionData.getErrorCode();
            }

            if (Objects.nonNull(errorCode)) {
                auditTrail.setErrorCode(errorCode.getCode());
                auditTrail.setErrorMessage(errorCode.getMessage());

                ETxnErrorCode tcbErrorCode = ETxnErrorCode.convert(errorCode);
                if (Objects.nonNull(tcbErrorCode)) {
                    auditTrail.setTcbErrorCode(tcbErrorCode.getCode());
                    auditTrail.setTcbErrorMessage(tcbErrorCode.getMessage());
                }

                auditTrail.setTransactionStatus(EErrorCode.SUCCESS.equals(errorCode) ? TxnStatus.SUCCESS : TxnStatus.FAILED);
            }

            auditTrail.setLoyaltyCusId(transactionData.getLoyaltyCusId());
            auditTrail.setTransactionId(transactionData.getTransactionId());
            auditTrail.setTxnCode(transactionData.getTxnCode());
            auditTrail.setTxnServiceType(transactionData.getTxnServiceType());
            auditTrail.setArrangementId(transactionData.getArrangementId());
            auditTrail.setCardId(transactionData.getCardId());
            auditTrail.setTxnProductFamily(transactionData.getTxnProductFamily());
            auditTrail.setTxnProductLine(transactionData.getTxnProductLine());
            auditTrail.setTxnInOut(transactionData.getTxnInOut());
            auditTrail.setTxnChannelLv1(transactionData.getTxnChannelLv1());
            auditTrail.setTxnChannelLv2(transactionData.getTxnChannelLv2());
            auditTrail.setTxnChannelLv3(transactionData.getTxnChannelLv3());
            auditTrail.setTxnMotherCategory(transactionData.getTxnMotherCategory());
            auditTrail.setTxnMcc(transactionData.getTxnMcc());
            auditTrail.setTxnServiceLv1(transactionData.getTxnServiceLv1());
            auditTrail.setTxnServiceLv2(transactionData.getTxnServiceLv2());
            auditTrail.setTxnCategoryGroup(transactionData.getTxnCategoryGroup());
            auditTrail.setTxnPurpose(transactionData.getTxnPurpose());
            auditTrail.setTxnMerchantPurpose(transactionData.getTxnMerchantPurpose());
            auditTrail.setTxnSupplier(transactionData.getTxnSupplier());
            auditTrail.setTxnCcyCode(transactionData.getTxnCcyCode());
            auditTrail.setTxnAdjReason(transactionData.getTxnAdjReason());
            auditTrail.setTxnSlrInd(transactionData.getTxnSlrInd());
            auditTrail.setTxnDate(transactionData.getTxnDate());
            auditTrail.setTxnTime(transactionData.getTxnTime());
            auditTrail.setTxnGrossAmt(transactionData.getTxnGrossAmt());
            auditTrail.setTxnNetAmt(transactionData.getTxnNetAmt());
            auditTrail.setTxnTerminalname(transactionData.getTxnTerminalname());
            auditTrail.setTxnBrandname(transactionData.getTxnBrandname());
            auditTrail.setTxnTid(transactionData.getTxnTid());
            auditTrail.setTxnMid(transactionData.getTxnMid());
            auditTrail.setTxnTermlocation(transactionData.getTxnTermlocation());
            auditTrail.setTxnForeignTxnAmt(transactionData.getTxnForeignTxnAmt());
            auditTrail.setTxnForeignExcRate(transactionData.getTxnForeignExcRate());
            auditTrail.setTxnStatus(transactionData.getTxnStatus());
            auditTrail.setTxnAdjSign(transactionData.getTxnAdjSign());
            auditTrail.setTxnOriginalId(transactionData.getTxnOriginalId());
            auditTrail.setTxnAppcode(transactionData.getTxnAppcode());
            auditTrail.setTxnToken(transactionData.getTxnToken());
            auditTrail.setTxnBin(transactionData.getTxnBin());
            auditTrail.setTxnType(transactionData.getTxnType());
            auditTrail.setTxnTermowner(transactionData.getTxnTermowner());
            auditTrail.setTxnSettlementDate(transactionData.getTxnSettlementDate());
            auditTrail.setTxnPostDate(transactionData.getTxnPostDate());
            auditTrail.setTxnQrMerchantInd(transactionData.getTxnQrMerchantInd());
            auditTrail.setTxnForeignCcyCode(transactionData.getTxnForeignCcyCode());
            auditTrail.setTxnBoundary(transactionData.getTxnBoundary());
            auditTrail.setTxnSubType(transactionData.getTxnSubType());
            auditTrail.setTxnChannelLv4(transactionData.getTxnChannelLv4());
            auditTrail.setTxnRecurringInd(transactionData.getTxnRecurringInd());
            auditTrail.setRetryCount(transactionData.getRetryCount() != null ? transactionData.getRetryCount() : 0);
            auditTrail.setTxnRankCheck(transactionData.getTxnRankCheck());

            KafkaAuditTrailEarnTransaction earnTransaction = kafkaAuditTrailTransEarnRepository.save(auditTrail);

            Log.info(LogData.createLogData()
                    .append("msg", "EarnTransactionHandlerImpl –- Inserting audit trail was successful.")
                    .append("transaction_id", auditTrail.getTransactionId())
                    .append("loyalty_cus_id", transactionData.getLoyaltyCusId())
                    .append("request_id", requestId)
                    .append("error_message", auditTrail.getErrorMessage())
            );

            return earnTransaction;
        } catch (Exception ex) {
            Log.error(LogData.createLogData()
                    .append("msg", "EarnTransactionHandlerImpl -– Exception while inserting transaction audit trail.")
                    .append("transaction_id", transactionData.getTransactionId())
                    .append("loyalty_cus_id", transactionData.getLoyaltyCusId())
                    .append("request_id", requestId)
                    .append("error", ex.getMessage())
            );
        }

        return null;
    }

    private void insertAuditTrailRetry(KafkaAuditTrailEarnTransaction trailEarnTransaction) {
        if (needToRetry(trailEarnTransaction)) {
            KafkaAuditTrailEarnTransactionRetry transactionRetry = new KafkaAuditTrailEarnTransactionRetry();
            BeanUtils.copyProperties(trailEarnTransaction, transactionRetry);
            kafkaAuditTrailTransEarnRetryRepository.save(transactionRetry);
            Log.info(LogData.createLogData()
                    .append("msg", "EarnTransactionHandlerImpl –- Inserting audit trail retry was successful.")
                    .append("transaction_id", transactionRetry.getTransactionId())
                    .append("loyalty_cus_id", transactionRetry.getLoyaltyCusId())
                    .append("request_id", transactionRetry.getMessageId())
                    .append("error_message", transactionRetry.getErrorMessage())
            );
        }
    }

    private boolean needToRetry(KafkaAuditTrailEarnTransaction trailEarnTransaction) {
        return "500".equals(trailEarnTransaction.getServiceErrorCode());
    }
}
