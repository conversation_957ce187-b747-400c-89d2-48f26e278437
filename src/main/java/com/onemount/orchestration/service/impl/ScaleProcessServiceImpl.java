package com.onemount.orchestration.service.impl;

import com.oneid.oneloyalty.common.constant.EFileType;
import com.oneid.oneloyalty.common.constant.EProcessingStatus;
import com.oneid.oneloyalty.common.constant.EScaleType;
import com.oneid.oneloyalty.common.entity.ControlFileHistory;
import com.oneid.oneloyalty.common.entity.ScaleHistory;
import com.oneid.oneloyalty.common.repository.ControlFileHistoryRepository;
import com.oneid.oneloyalty.common.repository.ScaleHistoryRepository;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import com.onemount.orchestration.model.ScaleInfo;
import com.onemount.orchestration.model.ScaleResponse;
import com.onemount.orchestration.service.ScaleProcessService;
import com.onemount.orchestration.service.ScaleService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class ScaleProcessServiceImpl implements ScaleProcessService {
    private final ControlFileHistoryRepository controlFileRepo;

    private final ScaleService scaleService;

    private final ScaleHistoryRepository scaleHistoryRepository;

    @Value("${app.scale.poll-interval-ms}")
    private Long pollIntervalMs;

    @Value("${app.scale.wait-duration-ms}")
    private Long waitDurationMs;

    @Value("${app.scale.time-out}")
    private Long timeOut;

    @Value("${app.scale.service-scale-up}")
    private String serviceScaleUp;

    @Value("${app.scale.service-scale-down}")
    private String serviceScaleDown;

    public ScaleProcessServiceImpl(ControlFileHistoryRepository controlFileRepo, ScaleService scaleService, ScaleHistoryRepository scaleHistoryRepository) {
        this.controlFileRepo = controlFileRepo;
        this.scaleService = scaleService;
        this.scaleHistoryRepository = scaleHistoryRepository;
    }

    @Override
    public void scaleUp() {
        recordScaleHistory(EFileType.EARNING_FLOW.getValue(), EScaleType.UP, EProcessingStatus.INIT);
        try {
            executeScaling(serviceScaleUp, EScaleType.UP);
        } catch (Exception e) {
            recordScaleHistory(EFileType.EARNING_FLOW.getValue(), EScaleType.UP, EProcessingStatus.FAILED);
            throw e;
        }
    }

    @Override
    public void scaleDown() {
        // Stop scaling down when has processing file or after latest control file or already scale down successfully
        boolean isScaleDown = false;
        ScaleHistory scaleHistory = latestScaleHistory();
        if (scaleHistory != null
                && EScaleType.DOWN.equals(scaleHistory.getScaleType())
                && EProcessingStatus.SUCCESS.equals(scaleHistory.getStatus())) {
            isScaleDown = true;
        }

        if (!hasProcessingFile() && isAfterLatestControlFile() && !isScaleDown) {
            Log.info(LogData.createLogData()
                    .append("msg", "ScaleProcess -- Start scaling down")
            );
            recordScaleHistory(EFileType.EARNING_FLOW.getValue(), EScaleType.DOWN, EProcessingStatus.INIT);
            try {
                executeScaling(serviceScaleDown, EScaleType.DOWN);
            } catch (Exception e) {
                recordScaleHistory(EFileType.EARNING_FLOW.getValue(), EScaleType.DOWN, EProcessingStatus.FAILED);
                throw e;
            }
        }
    }

    private void executeScaling(String serviceId, EScaleType scaleType) {
        long startTime = System.currentTimeMillis();
        Log.info(LogData.createLogData()
                .append("msg", "ScaleProcess -- Start scaling")
                .append("service", serviceId));
        try {
            initiateScaling(serviceId);
            waitForScalingCompletion(serviceId, startTime, scaleType);
            recordScaleHistory(EFileType.EARNING_FLOW.getValue(), scaleType, EProcessingStatus.SUCCESS);
        } catch (RuntimeException e) {
            recordScaleHistory(EFileType.EARNING_FLOW.getValue(), scaleType, EProcessingStatus.FAILED);
            throw e;
        }
    }

    private void initiateScaling(String serviceId) {
        List<ScaleInfo> scaleInfoList = scaleService.scale(serviceId);
        if (Objects.nonNull(scaleInfoList)) {
            Log.info(LogData.createLogData()
                    .append("msg", "ScaleProcess -- Process scaling")
                    .append("service", serviceId)
                    .append("scaleInfoList", scaleInfoList));
        }
    }

    private void waitForScalingCompletion(String serviceId, long startTime, EScaleType scaleType) {
        while (isScalingInProgress(serviceId)) {
            if (hasTimedOut(startTime)) {
                Log.info(LogData.createLogData()
                        .append("msg", "ScaleProcess -- Scaling timed out")
                        .append("service", serviceId));
                recordScaleHistory(EFileType.EARNING_FLOW.getValue(), scaleType, EProcessingStatus.FAILED);
                throw new RuntimeException("ScaleProcess -- Scaling timed out for service: " + serviceId);
            }
            sleepForPolling();
        }
    }

    private boolean isScalingInProgress(String serviceId) {
        ScaleResponse scaleResponse = scaleService.getScaleStatus(serviceId);
        switch (scaleResponse.getHttpStatus()) {
            case OK:
                Log.info(LogData.createLogData()
                        .append("msg", "ScaleProcess -- Scaling completed")
                        .append("service", serviceId)
                        .append("scaleResponse", scaleResponse.getPayload().getData()));
                return false;
            case ACCEPTED:
//                Log.info(LogData.createLogData()
//                        .append("msg", "ScaleProcess -- Scaling in progress")
//                        .append("service", serviceId));
                return true;
            default:
                Log.error(LogData.createLogData()
                        .append("msg", "ScaleProcess -- Scaling failed")
                        .append("service", serviceId));
                throw new RuntimeException("ScaleProcess -- Unexpected scaling status for service: " + serviceId);
        }
    }

    private boolean hasTimedOut(long startTime) {
        return (System.currentTimeMillis() - startTime) >= timeOut;
    }

    private boolean hasProcessingFile() {
        Long pendingFileNum = controlFileRepo.countPendingFiles(EFileType.EARNING_FLOW);
        return pendingFileNum != null && pendingFileNum > 0;
    }

    private boolean isAfterLatestControlFile() {
        Optional<ControlFileHistory> latestByUpdatedAt = controlFileRepo.findFirstByFileTypeOrderByUpdatedAtDesc(EFileType.EARNING_FLOW);
        if (latestByUpdatedAt.isEmpty()) {
            return true;
        }
        long diff = System.currentTimeMillis() - latestByUpdatedAt.get().getUpdatedAt().getTime();
        return diff > waitDurationMs;
    }

    private ScaleHistory latestScaleHistory() {
        Optional<ScaleHistory> latestScaleHistory = scaleHistoryRepository.findFirstByServiceNameOrderByUpdatedAtDesc(EFileType.EARNING_FLOW.getValue());

        return latestScaleHistory.orElse(null);
    }

    private void recordScaleHistory(String serviceName, EScaleType scaleType, EProcessingStatus status) {
        ScaleHistory history = ScaleHistory.builder()
                .serviceName(serviceName)
                .scaleType(scaleType)
                .status(status)
                .build();
        scaleHistoryRepository.save(history);
    }

    private void sleepForPolling() {
        try {
            Thread.sleep(pollIntervalMs);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("ScaleProcess -- Interrupted while polling for scaling", e);
        }
    }
}
