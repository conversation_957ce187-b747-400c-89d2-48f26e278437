package com.onemount.orchestration.service.impl;

import com.onemount.orchestration.service.FileService;
import com.onemount.orchestration.support.utils.FileNameGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FileServiceImpl implements FileService {

    private final FileNameGenerator fileNameGenerator;

    @Override
    public String createCsvResultFileName(String originalFileName, String cobDate, String dataFileType, long totalRecords) {
        FileNameGenerator.FileNameParams params =
                new FileNameGenerator.FileNameParams(totalRecords, FileNameGenerator.FileType.CSV);
        return fileNameGenerator.generateFileName(originalFileName, cobDate, dataFileType, params);
    }

    @Override
    public String createJsonControlFileName(String cobDate, String dataFileType) {
        FileNameGenerator.FileNameParams params =
                new FileNameGenerator.FileNameParams(0, FileNameGenerator.FileType.JSON);
        return fileNameGenerator.generateFileName(null, cobDate, dataFileType, params);
    }
}
