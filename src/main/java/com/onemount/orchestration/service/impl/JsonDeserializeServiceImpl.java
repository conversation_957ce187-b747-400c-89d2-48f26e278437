package com.onemount.orchestration.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.onemount.orchestration.exception.DeserializeException;
import com.onemount.orchestration.service.DeserializeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class JsonDeserializeServiceImpl implements DeserializeService {

    private final ObjectMapper objectMapper;

    @Override
    public <T> T deserialize(String data, Class<T> tClass) {
        try {
            return objectMapper.readValue(data, tClass);
        } catch (JsonProcessingException e) {
            throw new DeserializeException("Exception while deserialize data", data, e);
        }
    }
}
