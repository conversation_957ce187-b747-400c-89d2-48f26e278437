package com.onemount.orchestration.service;

import com.onemount.orchestration.constant.EProcessServiceId;
import com.onemount.orchestration.exception.DeserializeException;
import com.onemount.orchestration.model.ProcessData;

/**
 * <AUTHOR>
 */
public interface ContentProcessService<T> {

    void process(ProcessData<T> processData);

    boolean isProcessed(ProcessData<T> processData);

    EProcessServiceId getProcessId();

    T deserialize(String data) throws DeserializeException;

    String getOrderKey(T parsedData);

    String[] getHeader();

    String[] generateOutput(ProcessData<T> processData);
}
