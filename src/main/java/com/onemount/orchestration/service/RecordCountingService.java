package com.onemount.orchestration.service;

import java.util.List;

/**
 * Service for counting total records and failed records from data objects.
 * Handles different data types and provides thread-safe operations for concurrent processing.
 */
public interface RecordCountingService {
    /**
     * Counts the number of failed records in the provided list.
     * A record is considered failed if its error code is null or not equal to "00" (SUCCESS).
     *
     * @param dataList the list of data objects to check
     * @return the number of failed records
     */
    long countFailedRecords(List<?> dataList);

    /**
     * Determines if a single record is failed based on its error code.
     *
     * @param dataItem the data item to check
     * @return true if the record is failed, false otherwise
     */
    boolean isRecordFailed(Object dataItem);
}
