package com.onemount.orchestration.support;

import com.onemount.orchestration.support.utils.DateTimeConverter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;

public class DateConverter {
    private static final String FORMATTER = "dd-MMM-yyyy";
    private static final String FORMATTER_OUT = "yyyyMMdd";
    private static final String FORMATTER_DDMMYYYY = "dd/MM/yyyy";
    private static final String FORMATTER_YYYYMMDD = "yyyy-MM-dd";

    // Convert dd-MMM-yyyy to toTimestamp
    public static Long toTimestamp(String src) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat format = new SimpleDateFormat(FORMATTER);
            Date date = format.parse(src);
            return date.toInstant().toEpochMilli();
        } catch (ParseException e) {
            return null;
        }
    }

    // Convert dd-MMM-yyyy to yyyyMMdd
    public static String toYearMonthDay(String src) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat format = new SimpleDateFormat(FORMATTER);
            SimpleDateFormat formatOut = new SimpleDateFormat(FORMATTER_OUT);
            Date date = format.parse(src);
            return formatOut.format(date);
        } catch (ParseException e) {
            return null;
        }
    }

    // Convert dd-MMM-yyyy to yyyyMMdd
    public static Integer toYearMonthDay(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat formatOut = new SimpleDateFormat(FORMATTER_OUT);
        return Integer.valueOf(formatOut.format(date));
    }

    // Convert dd-MMM-yyyy to Date
    public static Date toDate(String src) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat format = new SimpleDateFormat(FORMATTER);
            return format.parse(src);
        } catch (ParseException e) {
            return null;
        }
    }

    // Convert yyyyMMdd to dd-MMM-yyyy
    public static String toDDMMMYYYY(String src) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat format = new SimpleDateFormat(FORMATTER);
            SimpleDateFormat formatOut = new SimpleDateFormat(FORMATTER_OUT);
            Date date = formatOut.parse(src);
            return format.format(date);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String toDDMMYYYY(String src) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(FORMATTER);
            SimpleDateFormat formatterDDMMYYYY = new SimpleDateFormat(FORMATTER_DDMMYYYY);
            Date date = formatter.parse(src);
            return formatterDDMMYYYY.format(date);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String toDateAttributeValue(String src) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat format = new SimpleDateFormat(FORMATTER);
            SimpleDateFormat formatOut = new SimpleDateFormat(FORMATTER_OUT);
            Date date = format.parse(src);
            return formatOut.format(date);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String toDateTimeAttributeValue(String src) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat format = new SimpleDateFormat(FORMATTER);
            Long timestamp = format.parse(src).toInstant().getEpochSecond();
            return String.valueOf(timestamp);
        } catch (ParseException e) {
            return null;
        }
    }

    //Convert Date to dd-MMM-yyyy
    public static String dateToString(Date src) {
        SimpleDateFormat format = new SimpleDateFormat(FORMATTER);
        return format.format(src);
    }

    //Convert dd-MMM-yyyy to EpochSecond
    public static Long toEpochSecond(String src) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat format = new SimpleDateFormat(FORMATTER);
            return format.parse(src).toInstant().getEpochSecond();
        } catch (ParseException e) {
            return null;
        }
    }

    public static Long toEpochSecond(String src, String strFormat) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat format = new SimpleDateFormat(strFormat);
            return format.parse(src).toInstant().getEpochSecond();
        } catch (ParseException e) {
            return null;
        }
    }

    //Convert EpochSecond to dd-MMM-yyyy
    public static String toString(Long src) {
        if (src == null) {
            return null;
        }
        SimpleDateFormat formatOut = new SimpleDateFormat(FORMATTER_OUT);
        return formatOut.format(Date.from(Instant.ofEpochSecond(src)));
    }

    //Convert String EpochMilisecond to dd-MMM-yyyy
    public static String toString(String src) {
        if (src == null) {
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat(FORMATTER);
        return format.format(Date.from(Instant.ofEpochMilli(Long.parseLong(src))));
    }

    //Is valid format dd-MMM-yyyy
    public static boolean isValidDate(String date) {
        if (date != null) {
            return DateConverter.toDate(date) != null;
        }
        return true;
    }

    // Convert YYYY-MM-dd to Date
    public static Date toDateFormatterYYYYMMDD(String src) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(FORMATTER_YYYYMMDD);
            return formatter.parse(src);
        } catch (ParseException e) {
            return null;
        }
    }

    //Convert Date to dd-MMM-yyyy
    public static String toYYYYMMDD(Date src) {
        SimpleDateFormat format = new SimpleDateFormat(FORMATTER_YYYYMMDD);
        return format.format(src);
    }

    public static boolean isValidFormat(String value, String format) {
        return DateTimeConverter.isValidFormat(value, format);
    }

    public static void main(String[] args) {
        boolean validFormat = DateTimeConverter.isValidFormat("11:04:54", "HH:mm:ss");
        boolean validFormat3 = DateTimeConverter.isValidFormat("18-MAY-2025", "dd-MMM-yyyy");
        boolean validFormat4 = DateTimeConverter.isValidFormat("18-May-2025", "dd-MMM-yyyy");
        boolean validFormat5 = DateTimeConverter.isValidFormat("18-may-2025", "dd-MMM-yyyy");
        boolean validFormat6 = DateTimeConverter.isValidFormat("18-maY-2025", "dd-MMM-yyyy");
        System.out.println(validFormat);
        System.out.println(validFormat3);
        System.out.println(validFormat4);
        System.out.println(validFormat5);
        System.out.println(validFormat6);
    }

    private static void test(String input, String pattern) {
        boolean result = isValidFormat(input, pattern);
        System.out.printf("Input: %-20s Pattern: %-20s Result: %s%n", input, pattern, result);
    }
}
