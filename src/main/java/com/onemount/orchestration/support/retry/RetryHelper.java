package com.onemount.orchestration.support.retry;

import com.onemount.orchestration.support.utils.Log;
import com.onemount.orchestration.support.utils.LogData;
import org.springframework.retry.support.RetryTemplate;

import java.util.function.Supplier;

public class RetryHelper {

    public static <T> T executeWithRetry(
            RetryTemplate retryTemplate,
            Supplier<T> operation,
            String requestId,
            String operationName
    ) {
        return retryTemplate.execute(
                context -> {
                    int attempt = context.getRetryCount();

                    if (attempt > 0) {
                        logAttempt(requestId, operationName, attempt);
                    }

                    return operation.get();
                }
        );
    }

    private static void logAttempt(String requestId, String operationName, int attempt) {
        Log.info(LogData.createLogData()
                .append("msg", "[RetryHelper] - Retry request")
                .append("operation_name", operationName)
                .append("request_id", requestId)
                .append("attempt", attempt)
        );
    }
}