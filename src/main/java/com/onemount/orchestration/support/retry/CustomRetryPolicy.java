package com.onemount.orchestration.support.retry;

import com.oneid.oneloyalty.common.exception.BusinessException;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.retry.RetryContext;
import org.springframework.retry.RetryPolicy;
import org.springframework.retry.context.RetryContextSupport;
import org.springframework.util.ClassUtils;

import java.util.Map;
import java.util.Set;

public class CustomRetryPolicy implements RetryPolicy {

    private final Set<Integer> businessExceptionRetryCodes = Set.of(500);
    private final int maxAttempts;
    private BinaryExceptionClassifier retryableClassifier;

    public CustomRetryPolicy(int maxAttempts,
                             Map<Class<? extends Throwable>, Boolean> retryableExceptions
    ) {
        this.retryableClassifier = new BinaryExceptionClassifier(false);
        this.maxAttempts = maxAttempts;
        this.retryableClassifier = new BinaryExceptionClassifier(retryableExceptions, false);
        this.retryableClassifier.setTraverseCauses(false);
    }

    @Override
    public boolean canRetry(RetryContext context) {
        Throwable throwable = context.getLastThrowable();

        if (throwable instanceof BusinessException) {
            BusinessException ex = (BusinessException) throwable;
            return businessExceptionRetryCodes.contains(ex.getErrCode()) &&
                    context.getRetryCount() < this.maxAttempts;
        }

        return (throwable == null || this.retryForException(throwable)) &&
                context.getRetryCount() < this.maxAttempts;
    }

    @Override
    public void close(RetryContext status) {
    }

    @Override
    public void registerThrowable(RetryContext context, Throwable throwable) {
        CustomRetryContext simpleContext = (CustomRetryContext) context;
        simpleContext.registerThrowable(throwable);
    }

    @Override
    public RetryContext open(RetryContext parent) {
        return new CustomRetryContext(parent);
    }

    private boolean retryForException(Throwable ex) {
        return this.retryableClassifier.classify(ex);
    }

    public String toString() {
        return ClassUtils.getShortName(this.getClass()) + "[maxAttempts=" + this.maxAttempts + "]";
    }

    private static class CustomRetryContext extends RetryContextSupport {
        public CustomRetryContext(RetryContext parent) {
            super(parent);
        }
    }
}