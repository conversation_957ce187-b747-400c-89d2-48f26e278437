package com.onemount.orchestration.support.remote;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.onemount.orchestration.exception.FileNotFoundException;
import com.onemount.orchestration.exception.ReadFileException;
import com.onemount.orchestration.support.utils.PGPUtil;
import com.onemount.orchestration.support.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Properties;

@Slf4j
public class SFTPFileSupport {

    private final SFTPConfig sftpConfig;

    public SFTPFileSupport(SFTPConfig sftpConfig) {
        this.sftpConfig = sftpConfig;
    }

    public byte[] readFile(String filePath, boolean isRetry) {
        try (SFTPConnection sftp = new SFTPConnection(this.sftpConfig, isRetry)) {
            if (!sftp.fileExists(filePath)) {
                log.warn("File not found on SFTP: {}", filePath);
                throw new FileNotFoundException("File not found on SFTP");
            }
            return sftp.readFileContent(filePath);
        } catch (Exception e) {
            throw new ReadFileException("Read file error");
        }
    }

    public boolean doesFileExist(String filePath, boolean isRetry) {
        try (SFTPConnection sftp = new SFTPConnection(this.sftpConfig, isRetry)) {
            return sftp.fileExists(filePath);
        } catch (Exception e) {
            log.error("Error checking file existence on SFTP", e);
            return false;
        }
    }

    public String uploadFile(byte[] data, String remoteFilePath, String publishPath, boolean isEncrypted) {
        try {
            InputStream inputStream;
            String finalRemotePath = remoteFilePath;

            // Check if PGP encryption is enabled
            if (isEncrypted) {
                log.info("PGP encryption enabled. Encrypting data before upload.");
                // Encrypt the data
                byte[] encryptedData = PGPUtil.encryptAndSign(
                        data,
                        publishPath,
                        sftpConfig.getPgpPrivateKeyOutPath(),
                        sftpConfig.getPgpPrivateKeyOutPassphrase()
                );
                inputStream = new ByteArrayInputStream(encryptedData);
                // Append .pgp extension to remote file path
                finalRemotePath = remoteFilePath.endsWith(".pgp") ? remoteFilePath : remoteFilePath + ".pgp";
            } else {
                log.info("PGP encryption disabled. Uploading unencrypted data.");
                inputStream = new ByteArrayInputStream(data);
            }

            // Upload the file
            try (SFTPConnection sftp = new SFTPConnection(this.sftpConfig, false)) {
                // Ensure the directory exists
//                String directoryPath = finalRemotePath.substring(0, finalRemotePath.lastIndexOf('/'));
//                if (!directoryPath.isEmpty()) {
//                    sftp.ensureDirectoryExists(directoryPath);
//                }
                sftp.uploadFileContent(inputStream, finalRemotePath);
                log.info("File successfully uploaded to SFTP: {}", finalRemotePath);
            }
            return finalRemotePath.substring(finalRemotePath.lastIndexOf('/') + 1);
        } catch (Exception e) {
            log.error("Error uploading file to SFTP: {}", remoteFilePath, e);
            throw new RuntimeException("Failed to upload file to SFTP", e);
        }
    }

    private static class SFTPConnection implements AutoCloseable {
        private final Session session;
        private final ChannelSftp channelSftp;

        public SFTPConnection(SFTPConfig sftpConfig, boolean isRetry) throws Exception {
            this.session = createSession(sftpConfig, isRetry);
            this.channelSftp = openSftpChannel(session);
        }

        private Session createSession(SFTPConfig sftpConfig, boolean isRetry) throws Exception {
            JSch jsch = new JSch();

            // Load Private Key
            if (StringUtil.isNotEmpty(sftpConfig.getPrivateKeyPath())) {
                if (StringUtil.isNotEmpty(sftpConfig.getPrivateKeyPassphrase())) {
                    jsch.addIdentity(sftpConfig.getPrivateKeyPath(), sftpConfig.getPrivateKeyPassphrase());
                } else {
                    jsch.addIdentity(sftpConfig.getPrivateKeyPath());
                }
            }

            Session session = jsch.getSession(
                    sftpConfig.getUsername(),
                    sftpConfig.getHost(),
                    Integer.parseInt(sftpConfig.getPort())
            );
            if (StringUtil.isNotEmpty(sftpConfig.getPassword())) {
                session.setPassword(sftpConfig.getPassword());
            }

            Properties config = new Properties();
            config.put("StrictHostKeyChecking", sftpConfig.getStrictHostKeyChecking());
            session.setConfig(config);

            session.connect();
//            log.info("Connected to SFTP server using private key authentication.");
            return session;
        }

        private ChannelSftp openSftpChannel(Session session) throws Exception {
            ChannelSftp channel = (ChannelSftp) session.openChannel("sftp");
            channel.connect();
//            log.info("SFTP channel opened.");
            return channel;
        }

        public boolean fileExists(String filePath) {
            try {
                channelSftp.ls(filePath);
                return true;
            } catch (SftpException e) {
                return e.id != ChannelSftp.SSH_FX_NO_SUCH_FILE;
            }
        }

        public byte[] readFileContent(String filePath) throws Exception {
            try (InputStream inputStream = channelSftp.get(filePath)) {
                return inputStream.readAllBytes();
            }
        }

        public void uploadFileContent(InputStream inputStream, String remoteFilePath) throws SftpException {
            channelSftp.put(inputStream, remoteFilePath);
        }

        @Override
        public void close() {
            if (channelSftp != null && channelSftp.isConnected()) {
                channelSftp.disconnect();
//                log.info("SFTP channel disconnected.");
            }
            if (session != null && session.isConnected()) {
                session.disconnect();
//                log.info("SFTP session disconnected.");
            }
        }
    }
}
