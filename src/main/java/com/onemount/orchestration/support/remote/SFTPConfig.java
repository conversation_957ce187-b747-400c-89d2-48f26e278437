package com.onemount.orchestration.support.remote;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@@ConfigurationProperties
public class SFTPConfig {

    private String protocol;

    private String host;

    private String port;

    private String username;

    private String password;

    private String baseFolder;

    private String knownHostsPath;

    private String strictHostKeyChecking;

    private String privateKeyPassphrase;

    private String privateKeyPath;

    private Boolean pgpEnable;

    private String pgpPrivateKeyPath;

    private String pgpPublicKeyPath;

    private String pgpPrivateKeyPassphrase;

    private String pgpPrivateKeyOutPath;

    private String pgpPublicKeyOutPath;

    private String pgpPrivateKeyOutPassphrase;

    private String outputFolder;

    private Boolean pgpDataOutEnable;

    private Boolean pgpControlOutEnable;

    private String pgpDelayTime;

    private String delay = "10000";

    private String maxDepth = "0";

    private String recursive = "true";

    private String idempotent = "false";

    private String streamDownload = "true";

    private String preSort = "true";

    private String noop = "true";

    private String stepwise = "false";
}
