package com.onemount.orchestration.support.utils;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

public class BigDecimalConverter {

    public static final int MAX_PRECISION = 19;
    public static final int MAX_SCALE = 0;

    public static boolean isValid(String src) {
        BigDecimal convert = toBigDecimal(src);
        return StringUtils.isEmpty(src) ||
                isValidLength(convert);
    }

    public static boolean isValidLength(BigDecimal value) {
        if (value == null)
            return false;

        int precision = value.precision();
        int scale = value.scale();

        return precision <= MAX_PRECISION && scale <= MAX_SCALE;
    }

    public static BigDecimal toBigDecimal(String src) {
        if (StringUtils.isEmpty(src)) {
            return null;
        }
        try {
            return new BigDecimal(src);
        } catch (Exception e) {
            return null;
        }
    }
}
