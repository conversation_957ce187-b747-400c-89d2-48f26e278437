package com.onemount.orchestration.support.utils;

import com.oneid.oneloyalty.client.model.AttributeUpdateReq;
import com.oneid.oneloyalty.common.constant.ECommonStatus;
import com.oneid.oneloyalty.client.model.attributes.TransactionAttributeReq;
import com.onemount.orchestration.constant.EAttributeType;
import com.onemount.orchestration.constant.MemberAttributeStatus;
import com.onemount.orchestration.support.DateConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AttributeConverter {
    private static final String FORMATTER = "yyyy-MM-dd";

    public static AttributeUpdateReq toAttribute(EAttributeType type, String code, String src) {
        String value;

        switch (type) {
            case DATE:
                value = DateConverter.toDateAttributeValue(src);
                break;
            case DATE_TIME:
                value = DateConverter.toDateTimeAttributeValue(src);
                break;
            default: // TEXT, NUMBER
                value = src;
        }

        AttributeUpdateReq attribute = new AttributeUpdateReq();
        attribute.setCode(code);
        attribute.setValue(value);
        attribute.setStatus(MemberAttributeStatus.ACTIVE.getValue());

        return attribute;
    }

    public static AttributeUpdateReq toAttribute(EAttributeType type, String code, String src, String status, String startDate, String endDate) {
        AttributeUpdateReq attribute = toAttribute(type, code, src);

        attribute.setStatus(status);
        attribute.setStartDate(DateConverter.toEpochSecond(startDate, FORMATTER));
        attribute.setEndDate(DateConverter.toEpochSecond(endDate, FORMATTER));

        return attribute;
    }

    public static TransactionAttributeReq toTxnAttribute(EAttributeType type, String code, String src) {
        String value;

        switch (type) {
            case DATE:
                value = DateConverter.toDateAttributeValue(src);
                break;
            case DATE_TIME:
                value = DateConverter.toDateTimeAttributeValue(src);
                break;
            default: // TEXT, NUMBER
                value = src;
        }

        TransactionAttributeReq attribute = new TransactionAttributeReq();
        attribute.setCode(code);
        attribute.setValue(value);

        return attribute;
    }

    public static List<AttributeUpdateReq> toFinalAttributes(List<AttributeUpdateReq> oldAttributes, List<AttributeUpdateReq> newAttributes) {
        Map<Pair<String, String>, AttributeUpdateReq> finalMap = new HashMap<>();
        for (AttributeUpdateReq newAttribute : newAttributes) {
            if (StringUtils.isNotEmpty(newAttribute.getValue())) {
                Pair<String, String> key = Pair.of(newAttribute.getCode(), newAttribute.getValue());
                finalMap.put(key, newAttribute);
            }
        }
        for (AttributeUpdateReq oldAttribute : oldAttributes) {
            if (StringUtils.isNotEmpty(oldAttribute.getValue())) {
                Pair<String, String> key = Pair.of(oldAttribute.getCode(), oldAttribute.getValue());
                if (!finalMap.containsKey(key)) {
                    oldAttribute.setStatus(MemberAttributeStatus.INACTIVE.getValue());
                    finalMap.put(key, oldAttribute);
                } else {
                    AttributeUpdateReq updateReq = finalMap.get(key);
                    if (updateReq.getValue().equals(oldAttribute.getValue())) {
                        finalMap.remove(key);
                    }
                }
            }
        }
        return new ArrayList<>(finalMap.values());
    }

    public static List<AttributeUpdateReq> toFinalAttributesFilterStatus(List<AttributeUpdateReq> oldAttributes, List<AttributeUpdateReq> newAttributes) {
        Map<String, AttributeUpdateReq> finalMap = new HashMap<>();
        for (AttributeUpdateReq newAttribute : newAttributes) {
            if (StringUtils.isNotEmpty(newAttribute.getValue())) {
                finalMap.put(newAttribute.getValue(), newAttribute);
            }
        }
        for (AttributeUpdateReq oldAttribute : oldAttributes) {
            if (StringUtils.isNotEmpty(oldAttribute.getValue())) {
                AttributeUpdateReq updateReq = finalMap.get(oldAttribute.getValue());
                if (updateReq != null && ECommonStatus.INACTIVE.getValue().equals(updateReq.getStatus())
                        && updateReq.getStatus().equals(oldAttribute.getStatus())) {
                    finalMap.remove(oldAttribute.getValue());
                }
            }
        }
        return new ArrayList<>(finalMap.values());
    }

    public static List<AttributeUpdateReq> toFinalAttributesUniqueLevel0(
            List<AttributeUpdateReq> oldAttributes,
            List<AttributeUpdateReq> newAttributes
    ) {
        Map<String, AttributeUpdateReq> finalMap = new HashMap<>();
        for (AttributeUpdateReq newAttribute : newAttributes) {
            if (StringUtils.isNotEmpty(newAttribute.getValue())) {
                finalMap.put(newAttribute.getCode(), newAttribute);
            }
        }
        for (AttributeUpdateReq oldAttribute : oldAttributes) {
            if (StringUtils.isNotEmpty(oldAttribute.getValue())) {
                if (!finalMap.containsKey(oldAttribute.getCode())) {
                    oldAttribute.setStatus(MemberAttributeStatus.INACTIVE.getValue());
                    finalMap.put(oldAttribute.getCode(), oldAttribute);
                } else {
                    AttributeUpdateReq updateReq = finalMap.get(oldAttribute.getCode());
                    if (updateReq.getValue().equals(oldAttribute.getValue())) {
                        finalMap.remove(oldAttribute.getCode());
                    }
                }
            }
        }
        return new ArrayList<>(finalMap.values());
    }
}
