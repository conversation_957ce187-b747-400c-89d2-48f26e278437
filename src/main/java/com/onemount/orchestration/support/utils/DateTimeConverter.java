package com.onemount.orchestration.support.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;

public class DateTimeConverter {
    private static final String FORMATTER = "dd-MMM-yyyy HH:mm:ss";
    private static final String FORMATTER_TIME = "HH:mm:ss";
    private static final String FORMATTER_DDMMYYYY = "dd/MM/yyyy";
    private static final String FORMATTER_DDMMMYYYY = "dd-MMM-yyyy";


    //Convert dd-MMM-yyyy HH:mm:ss to EpochSecond
    public static Long toEpochSecond(String src) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(FORMATTER);
            return formatter.parse(src).toInstant().getEpochSecond();
        } catch (Exception e) {
            return null;
        }
    }

    //Convert EpochSecond to dd-MMM-yyyy HH:mm:ss
    public static String toString(Long src) {
        if (src == null) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat(FORMATTER);
        return formatter.format(Date.from(Instant.ofEpochSecond(src)));
    }

    public static String toDDMMYYYY(String src) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(FORMATTER);
            SimpleDateFormat formatterDDMMYYYY = new SimpleDateFormat(FORMATTER_DDMMYYYY);
            Date date = formatter.parse(src);
            return formatterDDMMYYYY.format(date);
        } catch (ParseException e) {
            return null;
        }
    }

    //Convert Date to dd-MMM-yyyy HH:mm:ss
    public static String dateToString(Date src) {
        SimpleDateFormat formatter = new SimpleDateFormat(FORMATTER);
        return formatter.format(src);
    }

    // Convert dd-MMM-yyyy HH:mm:ss to Date
    public static Date toDate(String src) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(FORMATTER);
            return formatter.parse(src);
        } catch (ParseException e) {
            return null;
        }
    }

    public static Date toTime(String src) {
        if (src == null) {
            return null;
        }
        try {
            SimpleDateFormat formatterTime = new SimpleDateFormat(FORMATTER_TIME);
            return formatterTime.parse(src);
        } catch (ParseException e) {
            return null;
        }
    }

    public static boolean isValidDateTime(String datetime) {
        return Objects.nonNull(DateTimeConverter.toEpochSecond(datetime));
    }

    public static boolean isValidTime(String time) {
        return Objects.nonNull(DateTimeConverter.toTime(time));
    }

    public static String toDateString(Long src) {
        if (src == null) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat(FORMATTER_DDMMMYYYY);
        return formatter.format(Date.from(Instant.ofEpochSecond(src)));
    }

    public static String toTimeString(Long src) {
        if (src == null) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat(FORMATTER_TIME);
        return formatter.format(Date.from(Instant.ofEpochSecond(src)));
    }

    public static boolean isValidFormat(String value, String format) {
       if(value == null){
           return true;
       }
        DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                .parseCaseInsensitive()
                .appendPattern(format)
                .toFormatter(Locale.ENGLISH);

        try {
            LocalDateTime ldt = LocalDateTime.parse(value, formatter);
            String result = ldt.format(formatter);
            return result.equalsIgnoreCase(value);
        } catch (DateTimeParseException e) {
            try {
                LocalDate ld = LocalDate.parse(value, formatter);
                String result = ld.format(formatter);
                return result.equalsIgnoreCase(value);
            } catch (DateTimeParseException exp) {
                try {
                    LocalTime lt = LocalTime.parse(value, formatter);
                    String result = lt.format(formatter);
                    return result.equalsIgnoreCase(value);
                } catch (DateTimeParseException e2) {
                    return false;
                }
            }
        }
    }

}
