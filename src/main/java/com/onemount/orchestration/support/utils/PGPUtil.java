package com.onemount.orchestration.support.utils;

import org.bouncycastle.bcpg.HashAlgorithmTags;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openpgp.PGPEncryptedData;
import org.bouncycastle.openpgp.PGPEncryptedDataGenerator;
import org.bouncycastle.openpgp.PGPLiteralData;
import org.bouncycastle.openpgp.PGPLiteralDataGenerator;
import org.bouncycastle.openpgp.PGPObjectFactory;
import org.bouncycastle.openpgp.PGPPrivateKey;
import org.bouncycastle.openpgp.PGPPublicKey;
import org.bouncycastle.openpgp.PGPPublicKeyRing;
import org.bouncycastle.openpgp.PGPSecretKey;
import org.bouncycastle.openpgp.PGPSecretKeyRing;
import org.bouncycastle.openpgp.PGPSignature;
import org.bouncycastle.openpgp.PGPSignatureGenerator;
import org.bouncycastle.openpgp.operator.bc.BcPGPContentSignerBuilder;
import org.bouncycastle.openpgp.operator.bc.BcPGPDataEncryptorBuilder;
import org.bouncycastle.openpgp.operator.bc.BcPublicKeyKeyEncryptionMethodGenerator;
import org.bouncycastle.openpgp.operator.jcajce.JcaKeyFingerprintCalculator;
import org.bouncycastle.openpgp.operator.jcajce.JcePBESecretKeyDecryptorBuilder;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Iterator;

public class PGPUtil {
    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    public static byte[] encryptAndSign(byte[] data, String publicKeyPath, String privateKeyPath, String keyPassphrase) throws Exception {
        // Read public key
        PGPPublicKey publicKey = readPublicKey(publicKeyPath);

        // Read private key
        PGPPrivateKey privateKey = readPrivateKey(privateKeyPath, keyPassphrase);

        ByteArrayOutputStream encryptedOut = new ByteArrayOutputStream();

        // Sign the data
        ByteArrayOutputStream signedOut = new ByteArrayOutputStream();
        PGPSignatureGenerator sigGen = new PGPSignatureGenerator(
                new BcPGPContentSignerBuilder(privateKey.getPublicKeyPacket().getAlgorithm(), HashAlgorithmTags.SHA256));
        sigGen.init(PGPSignature.BINARY_DOCUMENT, privateKey);
        sigGen.generateOnePassVersion(false).encode(signedOut);

        PGPLiteralDataGenerator litGen = new PGPLiteralDataGenerator();
        try (var litOut = litGen.open(signedOut, PGPLiteralData.BINARY, "", data.length, new java.util.Date())) {
            litOut.write(data);
            sigGen.update(data);
        }
        sigGen.generate().encode(signedOut);

        // Encrypt the signed data
        byte[] signedData = signedOut.toByteArray();
        PGPEncryptedDataGenerator encGen = new PGPEncryptedDataGenerator(
                new BcPGPDataEncryptorBuilder(PGPEncryptedData.AES_256)
                        .setWithIntegrityPacket(true)
                        .setSecureRandom(new SecureRandom()));
        encGen.addMethod(new BcPublicKeyKeyEncryptionMethodGenerator(publicKey));

        try (var encOut = encGen.open(encryptedOut, new byte[1 << 16])) {
            encOut.write(signedData);
        }

        return encryptedOut.toByteArray();
    }

    private static PGPPublicKey readPublicKey(String publicKeyPath) throws Exception {
        try (InputStream keyIn = new FileInputStream(publicKeyPath)) {
            PGPObjectFactory pgpFact = new PGPObjectFactory(org.bouncycastle.openpgp.PGPUtil.getDecoderStream(keyIn), new JcaKeyFingerprintCalculator());
            Object obj;
            while ((obj = pgpFact.nextObject()) != null) {
                if (obj instanceof PGPPublicKeyRing) {
                    PGPPublicKeyRing keyRing = (PGPPublicKeyRing) obj;
                    Iterator<PGPPublicKey> keyIter = keyRing.getPublicKeys();
                    while (keyIter.hasNext()) {
                        PGPPublicKey key = keyIter.next();
                        if (key.isEncryptionKey()) {
                            return key;
                        }
                    }
                }
            }
        }
        throw new IllegalArgumentException("No valid public key found in " + publicKeyPath);
    }

    private static PGPPrivateKey readPrivateKey(String privateKeyPath, String passphrase) throws Exception {
        try (InputStream keyIn = new FileInputStream(privateKeyPath)) {
            PGPObjectFactory pgpFact = new PGPObjectFactory(org.bouncycastle.openpgp.PGPUtil.getDecoderStream(keyIn), new JcaKeyFingerprintCalculator());
            Object obj;
            while ((obj = pgpFact.nextObject()) != null) {
                if (obj instanceof PGPSecretKeyRing) {
                    PGPSecretKeyRing keyRing = (PGPSecretKeyRing) obj;
                    Iterator<PGPSecretKey> keyIter = keyRing.getSecretKeys();
                    while (keyIter.hasNext()) {
                        PGPSecretKey secretKey = keyIter.next();
                        if (secretKey.isSigningKey()) {
                            return secretKey.extractPrivateKey(
                                    new JcePBESecretKeyDecryptorBuilder().setProvider("BC").build(passphrase.toCharArray())
                            );
                        }
                    }
                }
            }
        }
        throw new IllegalArgumentException("No valid private key found in " + privateKeyPath);
    }
}
