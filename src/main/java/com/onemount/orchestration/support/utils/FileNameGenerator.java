package com.onemount.orchestration.support.utils;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.time.Instant;

@Component
public class FileNameGenerator {

    private static final String CSV_RESULT_FILE_FORMAT = "response-%s_%s_%d_%s.csv";
    private static final String JSON_CONTROL_FILE_FORMAT = "response-%s_manifest_%s_%d_%s.json";

    public String generateFileName(String originalFileName, String cobDate, String dataFileType, FileNameParams params) {
        if (params.getFileType() == FileType.CSV) {
            return generateCsvResultFileName(originalFileName, cobDate, dataFileType);
        } else if (params.getFileType() == FileType.JSON) {
            return generateJsonControlFileName(cobDate, dataFileType);
        }
        throw new IllegalArgumentException("Unsupported file type: " + params.getFileType());
    }

    private String generateCsvResultFileName(String originalFileName, String cobDate, String dataFileType) {
        String orderOfFile = extractOrderOfFile(originalFileName);
        long timestamp = Instant.now().toEpochMilli();

        return String.format(
                CSV_RESULT_FILE_FORMAT,
                dataFileType.toLowerCase(),
                cobDate,
                timestamp/1000,
                String.format("%04d", Integer.parseInt(orderOfFile))
        );
    }

    private String generateJsonControlFileName(String cobDate, String dataFileType) {
        long timestamp = Instant.now().toEpochMilli();

        return String.format(
                JSON_CONTROL_FILE_FORMAT,
                dataFileType.toLowerCase(),
                cobDate,
                timestamp / 1000,
                String.format("%04d", 1)
        );
    }

    private String extractOrderOfFile(String fileName) {
        String[] parts = fileName.split("-");
        if (parts.length >= 3) {
            return parts[2]; // order_of_file is the third part
        }
        throw new IllegalArgumentException("Cannot extract order from filename: " + fileName);
    }

    public static String extractCobDate(String fileName) {
        String[] parts = fileName.split("-");
        if (parts.length >= 2) {
            return parts[1]; // cob_date is the second part
        }
        throw new IllegalArgumentException("Cannot extract cob_date from filename: " + fileName);
    }

    public enum FileType {
        CSV,
        JSON
    }

    @Data
    @AllArgsConstructor
    public static class FileNameParams {
        private long totalRecords;
        private FileType fileType;
    }
}
