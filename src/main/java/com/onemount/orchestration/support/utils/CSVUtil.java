package com.onemount.orchestration.support.utils;

import com.opencsv.CSVWriter;

import java.io.StringWriter;

public final class CSVUtil {
    public static byte[] generateOutputCSV(String[] headers, String[][] dataRows) throws Exception {
        StringWriter stringWriter = new StringWriter();
        try (CSVWriter writer = new CSVWriter(stringWriter, 
                                             CSVWriter.DEFAULT_SEPARATOR, 
                                             CSVWriter.NO_QUOTE_CHARACTER,
                                             CSVWriter.DEFAULT_ESCAPE_CHARACTER, 
                                             CSVWriter.DEFAULT_LINE_END)) {

            // Write headers
            writer.writeNext(headers);
            
            // Write all data rows
            for (String[] row : dataRows) {
                writer.writeNext(row);
            }
        }
        return stringWriter.toString().getBytes();
    }
}
