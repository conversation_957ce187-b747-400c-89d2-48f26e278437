package com.onemount.orchestration.support.utils;

public final class StringUtil {

    private StringUtil() {
    }

    public static String formatWithZeroPrefix(Object val, int length) {
        String template = "%0" + length + "d";
        return String.format(template, val);
    }

    public static boolean isNotEmpty(String val) {
        return (val != null && val.length() > 0);
    }

    public static String randomWithSeq(int length, long seqNo) {
        int seqLen = String.valueOf(seqNo).length();
        if (seqLen >= length) {
            return null;
        }
        StringBuilder builder = new StringBuilder();
        int maxRandLength = length - String.valueOf(seqNo).length() - 1;
        for (int i = 0; i < maxRandLength; i++) {
            int randomInteger = getRandomInteger(8, 0);
            builder.append(randomInteger);
        }
        builder.append(9).append(seqNo);
        return builder.toString();
    }

    public static int getRandomInteger(int maximum, int minimum) {
        return ((int) (Math.random() * (maximum - minimum))) + minimum;
    }

    public static String searchLikeForAutoCompleteAndLowerCase(String s) {

        if (s == null) {
            return "%";
        }
        s = s.trim();
        StringBuilder patternForSearch = new StringBuilder("%");
        for (int i = 0, len = s.length(); i < len; ++i) {
            while ((s.charAt(i) == ' ' || s.charAt(i) == '\n')
                    && (i != len - 1 && (s.charAt(i + 1) == ' ' || s.charAt(i + 1) == '\n'))) {
                ++i;
            }
            patternForSearch.append(Character.toLowerCase(s.charAt(i)));
        }
        patternForSearch.append("%");
        return patternForSearch.toString();
    }

    public static boolean isAlphanumeric(String input) {
        return input.matches("^[a-zA-Z0-9]+$");
    }
}