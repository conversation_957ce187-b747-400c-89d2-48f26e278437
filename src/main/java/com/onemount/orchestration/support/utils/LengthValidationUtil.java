package com.onemount.orchestration.support.utils;

import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

public class LengthValidationUtil {
    public static final int MAX_LENGTH_DATE = 11;
    public static final int MAX_LENGTH_TIME = 8;
    public static final int MAX_LENGTH_DATE_TIME = 20;
    public static final int MAX_DIGITS_BIG_DECIMAL = 38;
    public static final int MAX_FRACTION_DIGITS_BIG_DECIMAL = 6;

    public static boolean isValidString(String src, int maxLength) {
        if (Objects.isNull(src)) {
            return true;
        }

        return StringUtils.length(src) <= maxLength;
    }

    public static String trimString(String src, int maxLength) {
        if (Objects.isNull(src)) {
            return null;
        }

        return src.substring(0, maxLength);
    }

    public static boolean isValidInteger(Integer src, int maxInteger) {
        if (Objects.isNull(src)) {
            return true;
        }

        return src <= maxInteger;
    }

    public static boolean isValidBigDecimal(BigDecimal src) {
        if (Objects.isNull(src)) {
            return true;
        }

        int digits = src.precision();
        int fractionDigits = src.scale();

        return digits <= MAX_DIGITS_BIG_DECIMAL && fractionDigits <= MAX_FRACTION_DIGITS_BIG_DECIMAL;
    }

    public static boolean isValidBigDecimal(BigDecimal src, int maxDigits, int maxFractionDigits) {
        if (Objects.isNull(src)) {
            return true;
        }

        int digits = src.precision();
        int fractionDigits = src.scale();

        BigDecimal intValue = src.setScale(0, RoundingMode.DOWN);
        if (BigDecimal.ZERO.equals(intValue)) {
            digits = fractionDigits + 1;
        }

        return digits >= 0
                && digits <= maxDigits
                && fractionDigits >= 0
                && fractionDigits <= maxFractionDigits
                && digits - fractionDigits >= 0
                && digits - fractionDigits <= maxDigits - maxFractionDigits;
    }

    public static boolean isValidBigDecimal(String src) {
        if (Objects.isNull(src)) {
            return true;
        }

        BigDecimal bigDecimal = BigDecimalConverter.toBigDecimal(src);
        if (Objects.isNull(bigDecimal)) {
            return false;
        }

        int digits = bigDecimal.precision();
        int fractionDigits = bigDecimal.scale();

        return digits >= 0
                && digits <= MAX_DIGITS_BIG_DECIMAL
                && fractionDigits >= 0
                && fractionDigits <= MAX_FRACTION_DIGITS_BIG_DECIMAL
                && digits - fractionDigits >= 0
                && digits - fractionDigits <= MAX_DIGITS_BIG_DECIMAL - MAX_FRACTION_DIGITS_BIG_DECIMAL;
    }

    public static BigDecimal trimBigDecimal(BigDecimal src, int maxDigits, int maxFractionDigits) {
        if (Objects.isNull(src)) {
            return null;
        }

        try {
            src = src.setScale(0, RoundingMode.DOWN);
            String srcString = src.toString();
            if (srcString.length() > maxDigits - maxFractionDigits) {
                srcString = srcString.substring(0, maxDigits - maxFractionDigits);
            }
            src = new BigDecimal(srcString);
        } catch (Exception ex) {
            Log.error(LogData.createLogData()
                    .append("msg", "Trim big decimal error")
                    .append("error", ex.getMessage()));
            return null;
        }

        return src;
    }
}
