package com.onemount.orchestration.config;

import com.oneid.oneloyalty.common.exception.BusinessException;
import com.onemount.orchestration.support.retry.CustomRetryPolicy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.server.ResponseStatusException;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class RetryConfig {

    @Value("${http.client.max-retries-execution:3}")
    private Integer maxRetries;

    @Value("${http.client.initial-retry-interval:5000}")
    private Integer initialInterval;

    @Value("${http.client.max-retry-interval:10000}")
    private Integer maxInterval;

    @Value("${http.client.retry-multiplier:2.0}")
    private Double multiplier;

    @Bean
    public RetryTemplate retryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();

        Map<Class<? extends Throwable>, Boolean> retryableExceptions = new HashMap<>();
        retryableExceptions.put(Exception.class, false);
        retryableExceptions.put(HttpServerErrorException.class, true);
        retryableExceptions.put(ResponseStatusException.class, true);
        retryTemplate.setRetryPolicy(new CustomRetryPolicy(maxRetries, retryableExceptions));


        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(initialInterval);
        backOffPolicy.setMultiplier(multiplier);
        backOffPolicy.setMaxInterval(maxInterval);
        retryTemplate.setBackOffPolicy(backOffPolicy);

        return retryTemplate;
    }
}
