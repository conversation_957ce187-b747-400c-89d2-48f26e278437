package com.onemount.orchestration.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.onemount.orchestration.support.remote.SFTPConfig;
import com.onemount.orchestration.support.remote.SFTPFileSupport;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ApplicationConfig {

    @Bean
    @ConfigurationProperties(prefix = "sftp-server")
    public SFTPConfig sftpConfig() {
        return new SFTPConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "internal-sftp-server")
    public SFTPConfig internalSftpConfig() {
        return new SFTPConfig();
    }

    @Bean
    public SFTPFileSupport sftpFileSupport() {
        return new SFTPFileSupport(sftpConfig());
    }

    @Bean
    ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);

        SimpleModule module = new SimpleModule();
        mapper.registerModule(module);
        return mapper;
    }
}
