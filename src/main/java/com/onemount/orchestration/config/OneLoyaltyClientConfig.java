package com.onemount.orchestration.config;

import com.oneid.oneloyalty.client.escrowpoint.EscrowPointClient;
import com.oneid.oneloyalty.client.escrowpoint.EscrowPointConfigParams;
import com.oneid.oneloyalty.client.service.ServiceClient;
import com.oneid.oneloyalty.client.service.ServiceConfigParams;
import com.oneid.oneloyalty.client.transaction.TransactionClient;
import com.oneid.oneloyalty.client.transaction.TransactionConfigParams;
import com.onemount.orchestration.service.OlConfigParam;
import com.onemount.orchestration.service.ScaleService;
import com.onemount.orchestration.service.impl.ScaleServiceImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class OneLoyaltyClientConfig {

    @Value("${app.oneloyalty.service.base-url:}")
    public String serviceUrl;

    @Value("${app.oneloyalty.escrow-point.base-url:}")
    public String escrowPointBaseUrl;

    @Value("${app.oneloyalty.check-fraud.base-url:}")
    public String checkFraudBaseUrl;

    @Value("${app.oneloyalty.check-fraud.basic-auth:}")
    public String checkFraudBasicAuth;

    @Value("${app.oneloyalty.scale.base-url:}")
    public String scaleBaseUrl;

    @Bean
    public ServiceClient serviceClient() {
        ServiceConfigParams configParams = new ServiceConfigParams(serviceUrl);
        return new ServiceClient(new RestTemplate(), configParams);
    }

    @Bean
    public EscrowPointClient escrowPointClient() {
        EscrowPointConfigParams configParams = new EscrowPointConfigParams(escrowPointBaseUrl);
        return new EscrowPointClient(new RestTemplate(), configParams);
    }

    @Bean
    public TransactionClient transactionClient() {
        TransactionConfigParams configParams = new TransactionConfigParams(checkFraudBaseUrl);
        return new TransactionClient(new RestTemplate(), configParams, checkFraudBasicAuth);
    }
}
