package com.onemount.orchestration.config;

import com.onemount.orchestration.constant.CKafkaFactory;
import com.onemount.orchestration.kafka.config.KafkaConfluentConfigParam;
import com.onemount.orchestration.kafka.config.KafkaExternalConfigParam;
import com.onemount.orchestration.kafka.config.KafkaOLServiceConfigParam;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;
@Configuration
@EnableKafka
public class KafkaConfig {

    // ---------------------- Kafka Confluent -----------------------------
    public Map<String, Object> confluentConfigs(KafkaConfluentConfigParam kafkaConfig) {
        Map<String, Object> propsMap = new HashMap<>();
        propsMap.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfig.kafkaBootstrapServers);

        if (kafkaConfig.sslEnabled) {
            propsMap.put("security.protocol", "SSL");
            propsMap.put("ssl.truststore.location", kafkaConfig.truststoreLocation);
            propsMap.put("ssl.truststore.password", kafkaConfig.truststorePassword);
            propsMap.put("ssl.keystore.location", kafkaConfig.keystoreLocation);
            propsMap.put("ssl.keystore.password", kafkaConfig.keystorePassword);
            propsMap.put("ssl.key.password", kafkaConfig.keyPassword);
        }

        return propsMap;
    }

//    @Bean
//    public ConsumerFactory<String, String> confluentConsumerFactory(KafkaConfluentConfigParam kafkaConfig) {
//        Map<String, Object> confluentConsumerConfigs = confluentConfigs(kafkaConfig);
//        confluentConsumerConfigs.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);
//        confluentConsumerConfigs.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
//        confluentConsumerConfigs.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
//
//        return new DefaultKafkaConsumerFactory<>(confluentConsumerConfigs);
//    }
//
//    @Bean(CKafkaFactory.CONFLUENT_KAFKA_CONSUMER)
//    public ConcurrentKafkaListenerContainerFactory<String, String> confluentKafkaListenerContainerFactory(KafkaConfluentConfigParam kafkaConfig) {
//        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
//        factory.setConsumerFactory(confluentConsumerFactory(kafkaConfig));
//        return factory;
//    }

    @Bean
    public ProducerFactory<String, String> confluentProducerFactory(KafkaConfluentConfigParam kafkaConfig) {
        Map<String, Object> confluentProduceConfigs = confluentConfigs(kafkaConfig);
        confluentProduceConfigs.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        confluentProduceConfigs.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        // For Kafka client version >= 2.7.x
        // To avoid issue cluster authorization failed
        confluentProduceConfigs.put("enable.idempotence", "false");

        return new DefaultKafkaProducerFactory<>(confluentProduceConfigs);
    }

    @Bean(CKafkaFactory.CONFLUENT_KAFKA_PRODUCER)
    public KafkaTemplate<String, String> confluentKafkaTemplate(KafkaConfluentConfigParam kafkaConfig) {
        return new KafkaTemplate<>(confluentProducerFactory(kafkaConfig));
    }

    // ---------------------- Kafka Confluent One loyalty service -----------------------------
    @Bean
    public Map<String, Object> confluentConsumerConfigs(KafkaOLServiceConfigParam kafkaOLServiceConfigParam) {
        Map<String, Object> propsMap = new HashMap<>();
        propsMap.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaOLServiceConfigParam.kafkaBootstrapServers);
        propsMap.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);
        propsMap.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        propsMap.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        if (kafkaOLServiceConfigParam.sslEnabled) {
            propsMap.put("security.protocol", "SSL");
            propsMap.put("ssl.truststore.location", kafkaOLServiceConfigParam.truststoreLocation);
            propsMap.put("ssl.truststore.password", kafkaOLServiceConfigParam.truststorePassword);
            propsMap.put("ssl.keystore.location", kafkaOLServiceConfigParam.keystoreLocation);
            propsMap.put("ssl.keystore.password", kafkaOLServiceConfigParam.keystorePassword);
            propsMap.put("ssl.key.password", kafkaOLServiceConfigParam.keyPassword);
        }
        return propsMap;
    }

    @Bean
    public ConsumerFactory<String, String> confluentConsumerFactory(KafkaOLServiceConfigParam kafkaOLServiceConfigParam) {
        return new DefaultKafkaConsumerFactory<>(confluentConsumerConfigs(kafkaOLServiceConfigParam));
    }

    @Bean(CKafkaFactory.OL_SERVICE_KAFKA_CONSUMER)
    public ConcurrentKafkaListenerContainerFactory<String, String> confluentKafkaListenerContainerFactory(KafkaOLServiceConfigParam kafkaConfig) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(confluentConsumerFactory(kafkaConfig));
        return factory;
    }

    @Bean(CKafkaFactory.KAFKA_EXTERNAL_CONSUMER)
    public ConcurrentKafkaListenerContainerFactory<String, String> kafkaExternalListenerContainerFactory(KafkaExternalConfigParam kafkaConfig) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(externalConsumerFactory(kafkaConfig));
        return factory;
    }

    @Bean
    @Qualifier("externalConsumerFactory")
    public ConsumerFactory<String, String> externalConsumerFactory(KafkaExternalConfigParam kafkaConfig) {
        Map<String, Object> consumerConfigs = externalConfigs(kafkaConfig);
        consumerConfigs.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfigs.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfigs.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        return new DefaultKafkaConsumerFactory<>(consumerConfigs);
    }

    @Bean(CKafkaFactory.KAFKA_EXTERNAL_PRODUCER)
    public KafkaTemplate<String, String> kafkaExternalTemplate(KafkaExternalConfigParam kafkaConfig) {
        return new KafkaTemplate<>(externalProducerFactory(kafkaConfig));
    }

    @Bean
    public ProducerFactory<String, String> externalProducerFactory(KafkaExternalConfigParam kafkaConfig) {
        Map<String, Object> consumerConfigs = externalConfigs(kafkaConfig);
        consumerConfigs.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        consumerConfigs.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        return new DefaultKafkaProducerFactory<>(consumerConfigs);
    }

    private Map<String, Object> externalConfigs(KafkaExternalConfigParam kafkaConfig) {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfig.kafkaBootstrapServers);

        if (kafkaConfig.securityEnable) {
            String jaasCfg = String.format(kafkaConfig.jaasTemplate, kafkaConfig.jaasUsername, kafkaConfig.jaasPassword);
            props.put("sasl.mechanism", kafkaConfig.saslMechanism);
            props.put("sasl.jaas.config", jaasCfg);
            props.put("security.protocol", kafkaConfig.securityProtocol);
            props.put("ssl.client.auth", kafkaConfig.sslClientAuth);
            props.put("ssl.endpoint.identification.algorithm", "");
            if (kafkaConfig.sslEnable) {
                props.put("ssl.truststore.location", kafkaConfig.truststoreLocation);
                props.put("ssl.truststore.password", kafkaConfig.truststorePassword);
                props.put("ssl.keystore.location", kafkaConfig.keystoreLocation);
                props.put("ssl.keystore.password", kafkaConfig.keystorePassword);
                props.put("ssl.key.password", kafkaConfig.keyPassword);
            }
        }
        return props;
    }
}
