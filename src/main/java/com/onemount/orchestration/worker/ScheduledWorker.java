package com.onemount.orchestration.worker;

import com.onemount.orchestration.service.ScaleProcessService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@Configuration
@EnableScheduling
@RequiredArgsConstructor
public class ScheduledWorker {

//    private final TransactionRetryService earnTransactionRetryProcessorService;

    private final ScaleProcessService scaleProcessService;

    @Value("${app.scale.enable:}")
    private Boolean enableScalePod;

//    @Scheduled(fixedDelay = 3600000, initialDelay = 20000)
//    public void scheduleAsync() {
//        earnTransactionRetryProcessorService.process();
//    }

    @Scheduled(fixedDelay = 1800000, initialDelay = 1800000)
    public void scheduleScaleDown() {
        if (enableScalePod) {
            scaleProcessService.scaleDown();
        }
    }
}
