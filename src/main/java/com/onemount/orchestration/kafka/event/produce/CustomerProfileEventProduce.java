package com.onemount.orchestration.kafka.event.produce;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.onemount.orchestration.model.CustomerProfileData;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CustomerProfileEventProduce extends BaseEventProduce {

    private static final long serialVersionUID = 3989446618492674622L;

    public CustomerProfileData.Payload payload;
}
