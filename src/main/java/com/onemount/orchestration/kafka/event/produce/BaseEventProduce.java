package com.onemount.orchestration.kafka.event.produce;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
public class BaseEventProduce implements Serializable {
    private static final long serialVersionUID = -1573809874614705884L;

    private String messageId;

    private String messageRefId;

    private Long timestamp;

    private String messageType;

    private String errorCode;

    private String errorMessage;
}
