package com.onemount.orchestration.kafka.event.produce;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CusInfoAttribute implements Serializable {
    private static final long serialVersionUID = 4659771393049522966L;

    private String code;

    private String value;

    private String startDate;

    private String endDate;
}