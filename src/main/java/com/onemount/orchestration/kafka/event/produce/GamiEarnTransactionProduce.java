package com.onemount.orchestration.kafka.event.produce;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.client.model.attributes.TransactionAttributeReq;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import com.onemount.orchestration.kafka.event.consume.BaseEventConsume;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.sql.Time;
import java.util.List;
import java.util.UUID;

import static com.onemount.orchestration.constant.EventName.EVENT_TRANS_GAMI;
import static com.onemount.orchestration.constant.MessageType.TRANS_EARN_POINT_REQ;

public class GamiEarnTransactionProduce extends BaseEventConsume {
    private static final long serialVersionUID = -2632422612306271313L;

    private PayloadGami payloadGami;

    @Getter
    @Setter
    public static class EventGami {
        private final String event = EVENT_TRANS_GAMI;
        private final String messageType = String.valueOf(TRANS_EARN_POINT_REQ);
        private String id;
        @JsonProperty("customer_id")
        private String loyaltyCusId;
        private String transactionId;
        private String userId;
        private Time timeStamp;
        private @NotBlank(
                message = "Business code is missing"
        ) String businessCode;
        private @NotBlank(
                message = "Program code is missing"
        ) String programCode;
        @JsonProperty("payload")
        private PayloadGami payloadGami = new PayloadGami();

        public void setId() {
            this.id = UUID.randomUUID().toString();
        }
    }

    @Getter
    @Setter
    public static class PayloadGami {

        @JsonProperty("customer_identifier")
        private @Valid CustomerIdentify customerIdentifier;

        @JsonProperty("transaction_attributes")
        private List<TransactionAttributeReq> attributes;

        @JsonProperty("txn_datetime")
        private String transactionDatetime;
    }
}
