package com.onemount.orchestration.kafka.event.produce;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class CusInfo implements Serializable {
    private static final long serialVersionUID = -5659723479617800933L;

    private String loyaltyCusId;

    private String memberCode;

    private String status;

    private String registrationDate;

    private String dob;

    private String gender;

    private List<CusInfoAttribute> attributes;

    private String dsPartitionDate;
}