package com.onemount.orchestration.kafka.event.produce;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TierInfo implements Serializable {
    private static final long serialVersionUID = 3873781938472742328L;

    private String tierCode;

    private String startDate;

    private String expireDate;

    private String cstTierProgram;
}