package com.onemount.orchestration.kafka.event.produce;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.oneid.oneloyalty.common.model.CustomerIdentify;
import com.onemount.orchestration.kafka.event.consume.BaseEventConsume;
import com.onemount.orchestration.model.EventData;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import java.util.UUID;

import static com.onemount.orchestration.constant.EventName.EVENT_EARN_EVENT_GAMI;
import static com.onemount.orchestration.constant.MessageType.EVENT_EARN_POINT_REQ;

@Setter
@Getter
public class GamiEarnEventProduce extends BaseEventConsume {
    private static final long serialVersionUID = -2632422612306271313L;
    @JsonProperty("business_code")
    private final String businessCode = "TCB";
    @JsonProperty("program_code")
    private final String programCode = "TCBC";
    private final String event = EVENT_EARN_EVENT_GAMI;
    private final String messageType = "EVENT_EARN_POINT_REQ";
    private String id;
    @JsonProperty("customer_id")
    private String loyaltyCusId;
    @JsonProperty("user_id")
    private String userId;
    @JsonProperty("payload")
    private PayloadGami payloadGami = new PayloadGami();

    public void setId() {
        this.id = UUID.randomUUID().toString();
    }

    @Getter
    @Setter
    public static class PayloadGami {
        @JsonProperty("customer_identifier")
        private @Valid CustomerIdentify customerIdentifier;

        @JsonProperty("event")
        @Getter
        @Setter
        private EventData payloadEarnEventGami;

    }

}
