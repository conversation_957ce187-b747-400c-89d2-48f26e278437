package com.onemount.orchestration.kafka.producer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.onemount.orchestration.constant.CKafkaFactory;
import com.onemount.orchestration.kafka.event.produce.BaseEventProduce;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class KafkaExternalProducer {

    private final KafkaTemplate<String, String> kafkaTemplate;

    private final ObjectMapper objectMapper;

    public KafkaExternalProducer(
            @Qualifier(CKafkaFactory.KAFKA_EXTERNAL_PRODUCER) KafkaTemplate<String, String> kafkaTemplate,
            ObjectMapper objectMapper
    ) {
        this.kafkaTemplate = kafkaTemplate;
        this.objectMapper = objectMapper;
    }

    @Async("backgroundProcessThreadPool")
    public void send(BaseEventProduce data, String topic) {
        try {
            String body = objectMapper.writeValueAsString(data);
            send(body, topic);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public void send(String message, String topic) {
        kafkaTemplate.send(topic, message);
    }
}
