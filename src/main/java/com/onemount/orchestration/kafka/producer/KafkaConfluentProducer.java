package com.onemount.orchestration.kafka.producer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.onemount.orchestration.constant.CKafkaFactory;
import com.onemount.orchestration.kafka.config.KafkaConfluentConfigParam;
import com.onemount.orchestration.kafka.event.produce.BaseEventProduce;
import com.onemount.orchestration.support.utils.Log;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class KafkaConfluentProducer {

    private final KafkaTemplate<String, String> kafkaTemplate;

    private final ObjectMapper objectMapper;
    private final KafkaConfluentConfigParam configParam;

    public KafkaConfluentProducer(
            @Qualifier(CKafkaFactory.CONFLUENT_KAFKA_PRODUCER) KafkaTemplate<String, String> kafkaTemplate,
            ObjectMapper objectMapper,
            KafkaConfluentConfigParam configParam
    ) {
        this.kafkaTemplate = kafkaTemplate;
        this.objectMapper = objectMapper;
        this.configParam = configParam;
    }

    @Async("backgroundProcessThreadPool")
    public void send(BaseEventProduce data, String topic) {
        try {
            String body = objectMapper.writeValueAsString(data);
            send(body, topic);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public void send(String message, String topic) {
        if (configParam.produceEnabled) {
            kafkaTemplate.send(topic, message);
        }
    }

    public void send(String key, String message, String topic) {
        if (configParam.produceEnabled) {
            kafkaTemplate.send(topic, key, message);
        }
    }
}
