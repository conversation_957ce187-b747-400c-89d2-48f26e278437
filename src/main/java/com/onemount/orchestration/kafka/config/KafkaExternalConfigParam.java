package com.onemount.orchestration.kafka.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class KafkaExternalConfigParam {

    @Value("${kafka-external.bootstrap-servers}")
    public String kafkaBootstrapServers;

    @Value("${kafka-external.ssl.truststore.location}")
    public String truststoreLocation;

    @JsonIgnore
    @Value("${kafka-external.ssl.truststore.password}")
    public String truststorePassword;

    @JsonIgnore
    @Value("${kafka-external.ssl.key.password}")
    public String keyPassword;

    @JsonIgnore
    @Value("${kafka-external.ssl.keystore.password}")
    public String keystorePassword;

    @Value("${kafka-external.ssl.keystore.location}")
    public String keystoreLocation;

    @Value("${kafka-external.ssl.protocol}")
    public String sslProtocol;

    @Value("${kafka-external.properties.sasl.jaas.template}")
    public String jaasTemplate;

    @Value("${kafka-external.properties.sasl.jaas.username}")
    public String jaasUsername;

    @Value("${kafka-external.properties.sasl.jaas.password}")
    public String jaasPassword;

    @Value("${kafka-external.properties.sasl.jaas.mechanism}")
    public String saslMechanism;

    @Value("${kafka-external.properties.security.protocol}")
    public String securityProtocol;

    @Value("${kafka-external.properties.security.enable}")
    public boolean securityEnable;

    @Value("${kafka-external.ssl.enable}")
    public boolean sslEnable;

    @Value("${kafka-external.ssl.client.auth}")
    public String sslClientAuth;
}
