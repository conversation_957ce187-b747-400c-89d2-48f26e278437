package com.onemount.orchestration.kafka.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class KafkaConfluentConfigParam {

    @Value("${kafka.confluent.bootstrap-servers}")
    public String kafkaBootstrapServers;

    @Value("${kafka.confluent.truststore.location}")
    public String truststoreLocation;

    @JsonIgnore
    @Value("${kafka.confluent.truststore.password}")
    public String truststorePassword;

    @JsonIgnore
    @Value("${kafka.confluent.key.password}")
    public String keyPassword;

    @JsonIgnore
    @Value("${kafka.confluent.keystore.password}")
    public String keystorePassword;

    @Value("${kafka.confluent.keystore.location}")
    public String keystoreLocation;

    @Value("${kafka.confluent.ssl-enable}")
    public Boolean sslEnabled;

    @Value("${kafka.confluent.produce-enable:true}")
    public Boolean produceEnabled;
}
