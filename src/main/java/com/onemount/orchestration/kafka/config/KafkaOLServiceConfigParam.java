package com.onemount.orchestration.kafka.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class KafkaOLServiceConfigParam {

    @Value("${kafka.ol-service.bootstrap-servers}")
    public String kafkaBootstrapServers;

    @Value("${kafka.ol-service.truststore.location}")
    public String truststoreLocation;

    @JsonIgnore
    @Value("${kafka.ol-service.truststore.password}")
    public String truststorePassword;

    @JsonIgnore
    @Value("${kafka.ol-service.key.password}")
    public String keyPassword;

    @JsonIgnore
    @Value("${kafka.ol-service.keystore.password}")
    public String keystorePassword;

    @Value("${kafka.ol-service.keystore.location}")
    public String keystoreLocation;

    @Value("${kafka.ol-service.ssl-enable}")
    public Boolean sslEnabled;
}
