package com.onemount.orchestration.kafka.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.oneid.oneloyalty.common.util.Log;
import com.oneid.oneloyalty.common.util.LogData;
import com.onemount.orchestration.kafka.producer.KafkaConfluentProducer;
import com.onemount.orchestration.kafka.producer.KafkaExternalProducer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ProduceService {

    private final KafkaConfluentProducer producer;

    private final KafkaExternalProducer mskProducer;

    private final ObjectMapper objectMapper;

    public void produce(ProduceMessage produceMessage, String topic, String type) throws JsonProcessingException {
        String message = objectMapper.writeValueAsString(produceMessage.value);

        if ("EXTERNAL".equals(type)) {
            mskProducer.send(message, topic);
        } else {
            if (produceMessage.key != null) {
                producer.send(produceMessage.key.toString(), message, topic);
            } else {
                producer.send(message, topic);
            }
        }
    }

    @Async("backgroundProcessThreadPool")
    public void produceAsync(ProduceMessage message, String topic, String type) {
        try {
            produce(message, topic, type);
        } catch (Exception e) {
            Log.error(LogData.createLogData()
                    .append("msg", "Kafka produce message is error")
                    .append("payload", message)
                    .append("topic", topic)
                    .append("type", type)
            );
            e.printStackTrace();
        }
    }

    @Getter
    @Setter
    public static class ProduceMessage {
        private Object key;
        private Object value;
    }
}
