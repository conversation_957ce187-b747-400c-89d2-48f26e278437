# ps2-file-processing


# Tech stack:
- Java, Spring Boot
- Apache Camel, JSch

# Run local
Add environment variables:
```aidl
JDBC_PASSWORD=;
JDBC_URL=;
JDBC_USERNAME=;
SFTP_HOST=;
SFTP_PASSWORD=;
SFTP_PORT=;
SFTP_USERNAME=;
```
Connect to sFTP Server:
- https://vinid-team.atlassian.net/wiki/spaces/L2/pages/1171925953/C+c+lu+ng+x+l+file+qua+sFTP
- https://vinid-team.atlassian.net/wiki/spaces/L2/pages/733549407/SFTP+Server

Add files SFTP (Data files and Control file) as defined: https://1squad.atlassian.net/wiki/spaces/TCBLOYAL/pages/821657812/Earning+Flow+-+File+MFT


# Deployment

Dev: Trigger deploy from develop
qc: Create new tag from develop with prefix: v1.xx.yy-qc
uat: Create new tag from develop with prefix: v1.xx.yy-uat
Prod: Create new tag from develop with prefix: v1.xx.yy

- xx: Sprint number
- yy: Sequence number
