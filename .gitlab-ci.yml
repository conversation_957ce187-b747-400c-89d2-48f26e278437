include: # For Request Deploy To Production
  - project: 'devops/gitlab-ci-templates'
    ref: master
    file: "/common/libs-next-gen.yml"
image: asia.gcr.io/vinid-devops/k8s-deployer:latest

cache:
  key: "$CI_PROJECT_NAMESPACE:$CI_PROJECT_NAME"
  paths:
    - .m2/repository

variables:
  IMAGE_TAG: $CI_COMMIT_SHORT_SHA
  ENABLE_NEWRELIC: "false"
  SONAR_PROJECTKEY: 1loyalty_$CI_PROJECT_NAME
  MAVEN_CLI_OPTS: "-s maven/settings.xml --batch-mode"
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"

stages:
  - compile build
  - docker build
  - tag
  - deploy
  - request deploy to production

.before_script: &before_script |
  source ./k8s/before_script.sh

before_script:
  - *before_script

compile build:
  tags:
    - shared-k8s-runner
  only:
    - /feature\/*/
    - develop
    - /release\/*/
    - master
  stage: compile build
  image: maven:amazoncorretto

  script:
    - mvn $MAVEN_CLI_OPTS -U clean package -DskipTests
    - mkdir compiled/
    - cp target/ps2-file-processing-*.jar compiled/app.jar
  artifacts:
    expire_in: 6h
    paths:
      - compiled/

docker-build:
  tags:
    - shared-k8s-runner
  stage: docker build
  only:
    - develop
    - /feature\/*/
    - /release\/*/
    - /hotfix\/*/
    - /bugfix\/LP-*/
    - master
  script:
    - gcloud builds submit --config=k8s/cloudbuild.yaml --substitutions=_CI_PROJECT_NAME=${CI_PROJECT_NAME},_IMAGE_TAG=${IMAGE_TAG},_COMMIT_TAG=${CI_COMMIT_TAG},_JOB_ID=${CI_JOB_ID}

deploy-to-dev:
  stage: deploy
  allow_failure: false
  when: manual
  tags:
    - 1loyalty-nonprod
  only:
    - develop
  variables:
    ENABLE_NEWRELIC: "false"
    ENVIRONMENT: "dev"
    NAMESPACE: "oneloyalty-dev"
    SERVICE_PATH: "ps2-file-processing"
    HOST: "api-dev.int.vinid.dev"
  script: |
    cd k8s && bash deploy.sh

deploy-to-qc:
  stage: deploy
  allow_failure: false
  tags:
    - 1loyalty-nonprod
  only:
    - develop
    - /^v.*-qc/
  variables:
    ENABLE_NEWRELIC: "false"
    ENVIRONMENT: "qc"
    NAMESPACE: "oneloyalty-qc"
    SERVICE_PATH: "ps2-file-processing"
    HOST: "api-qc.int.vinid.dev"
  script: |
    cd k8s && bash deploy.sh

deploy-to-uat:
  stage: deploy
  allow_failure: false
  tags:
    - 1loyalty-nonprod
  only:
    - /^v.*-uat/
  variables:
    ENABLE_NEWRELIC: "false"
    ENVIRONMENT: "uat"
    NAMESPACE: "oneloyalty-uat"
    SERVICE_PATH: "ps2-file-processing"
    HOST: "api-uat.int.vinid.dev"
  script: |
    cd k8s && bash deploy.sh

deploy-to-pt:
  when: manual
  stage: deploy
  allow_failure: false
  tags:
    - 1loyalty-nonprod
  only:
    - /^v.*-uat/
    - /^v.*-pt/
  variables:
    ENABLE_NEWRELIC: "false"
    ENVIRONMENT: "pt"
    NAMESPACE: "oneloyalty-loadtest"
    SERVICE_PATH: "ps2-file-processing"
    HOST: "api-pt.int.vinid.dev"
    MIN_REPLICA: 1
    MAX_REPLICA: 20
    CPU_TARGET_PERCENT: 80
  script: |
    cd k8s && bash deploy.sh

deploy-to-stg:
  when: manual
  stage: deploy
  allow_failure: false
  tags:
    - 1loyalty-nonprod
  only:
    - /^v.*-stg/
  variables:
    ENABLE_NEWRELIC: "false"
    ENVIRONMENT: "stg"
    NAMESPACE: "oneloyalty-staging"
    SERVICE_PATH: "ps2-file-processing"
    HOST: "api-staging.int.vinid.dev"
    MIN_REPLICA: 1
    MAX_REPLICA: 20
    CPU_TARGET_PERCENT: 80
  script: |
    cd k8s && bash deploy.sh

tag:
  stage: tag
  only:
    - tags
  script:
    - gcloud container images add-tag asia.gcr.io/vinid-devops/${CI_PROJECT_NAME}:${IMAGE_TAG} asia.gcr.io/vinid-devops/${CI_PROJECT_NAME}:${CI_COMMIT_TAG} --quiet

request to prod:
  extends: .request_to_prod
  only:
    - /^v.*\d+$/
  when: manual