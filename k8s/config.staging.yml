apiVersion: v1
kind: ConfigMap
metadata:
  name: ${CI_PROJECT_NAME}
data:
  ENVIRONMENT: "${ENVIRONMENT}"
  APP_BUSINESS_CODE: "TCB"
  APP_PROGRAM_CODE: "TCBC"
  SFTP_HOST: "**************"
  SFTP_PORT: "2022"
  SFTP_BASE_FOLDER: "/reconcile/LoyaltyReward/staging/datalake"
  KNOWN_HOSTS_PATH: "./conf/known_hosts"
  #  SFTP_PRIVATE_KEY_PATH: "./keys/tcb_ssh_private_key_dev.pem"
  SFTP_PGP_ENABLE: "false"
  OL_SERVICE_BASE_URL: "http://oneloyalty-service/oneloyalty-service"
  OL_ESCROW_POINT_BASE_URL: "http://oneloyalty-escrow-point-service/oneloyalty-escrow-point-service"
  CHECK_FRAUD_BASE_URL: "https://api-payment-staging.int.vinid.dev/ps2/risk-fraud/internal"
  APP_ENABLE_EVENT_GAMI: "true"
  APP_ENABLE_EARNING_PROCESSING: "true"
  APP_ENABLE_SCALE_POD: "false"
  APP_POLL_INTERVAL_MS: "30000"
  APP_UP_SERVICE_NAME: "earn"
  APP_DOWN_SERVICE_NAME: "done"
  APP_WAIT_DURATION_MS: "1800000"
  APP_SCALE_TIME_OUT_MS: "2400000"

  KAFKA_CONFLUENT_BOOTSTRAP_ADDRESS: "confluent-kafka-tw-1.int.onemount.dev:9093,confluent-kafka-tw-2.int.onemount.dev:9093,confluent-kafka-tw-3.int.onemount.dev:9093,confluent-kafka-sg-4.int.onemount.dev:9093,confluent-kafka-sg-5.int.onemount.dev:9093,confluent-kafka-sg-6.int.onemount.dev:9093"
  KAFKA_CONFLUENT_TRUSTSTORE_LOCATION: "./kafka/confluent-truststore.jks"
  KAFKA_CONFLUENT_KEYSTORE_LOCATION: "./kafka/oneloyalty-common-clients.int.onemount.dev.jks"
  KAFKA_CONFLUENT_SSL_ENABLE: "true"
  KAFKA_CONFLUENT_ENABLED: "true"
  GROUP_ID: "ps2-loyalty-event-worker-staging"

  TOPIC_NAME_CUS_INFO_CONSUME: "ps2-event-customer-info-consume-staging"
  TOPIC_NAME_CUS_INFO_PRODUCE: "ps2-event-customer-info-produce-staging"
  TOPIC_NAME_TRANS_EARN_CONSUME: "ps2-event-transaction-earn-point-consume-staging"
  TOPIC_NAME_TRANS_EARN_PRODUCE: "ps2-event-transaction-earn-point-produce-staging"
  TOPIC_NAME_TRANS_GAMI_PRODUCE: "ps2-oneloyalty-event-transactions-raw-staging"
  TOPIC_NAME_EARN_EVENT_GAMI_PRODUCE: "ps2-oneloyalty-earn-event-raw-staging"

  KAFKA_EXTERNAL_BOOTSTRAP_ADDRESS: "3rd-party-streaming-uat-nlb-3-3440597c044c3802.elb.ap-southeast-1.amazonaws.com:9096,3rd-party-streaming-uat-nlb-1-7d12d9c148830fe0.elb.ap-southeast-1.amazonaws.com:9096,3rd-party-streaming-uat-nlb-2-08978705e3749571.elb.ap-southeast-1.amazonaws.com:9096"
  KAFKA_EXTERNAL_TRUSTSTORE_LOCATION: "./kafka/confluent-truststore.jks"
  KAFKA_EXTERNAL_KEYSTORE_LOCATION: "./kafka/oneloyalty-common-clients.int.onemount.dev.jks"
  EXTERNAL_TOPIC_NAME_CUS_INFO_CONSUME: "cloud-ps2-event-staging-loyalty-member-01"
  EXTERNAL_TOPIC_NAME_CUS_INFO_PRODUCE: "cloud-ps2-event-staging-loyalty-member-resp-01"
  EXTERNAL_TOPIC_NAME_UPDATE_CUSTOMER_CONSUME: "cloud-ps2-event-staging-customer-info-01"
  EXTERNAL_TOPIC_NAME_UPDATE_CUSTOMER_PRODUCE: "cloud-ps2-event-staging-customer-info-resp-01"
  EXTERNAL_GROUP_ID: "srv_omc_msk-group"
  KAFKA_EXTERNAL_SSL_ENABLE: "false"
  KAFKA_EXTERNAL_ENABLED: "false"
  KAFKA_EXTERNAL_SASL_JAAS_USERNAME: "srv_omc_msk"
  KAFKA_EXTERNAL_SASL_JAAS_TEMPLATE: "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"%s\" password=\"%s\";"
  KAFKA_EXTERNAL_SASL_MECHANISM: "SCRAM-SHA-512"
  KAFKA_EXTERNAL_SECURITY_PROTOCOL: "SASL_SSL"
  KAFKA_EXTERNAL_SSL_PROTOCOL: "TLSv1.2"
  KAFKA_EXTERNAL_MAX_CONCURRENCY: "64"
  KAFKA_EXTERNAL_MAX_POOL_RECORDS: "192"
  KAFKA_EXTERNAL_SSL_CLIENT_AUTH: "required"

  KAFKA_OL_SERVICE_BOOTSTRAP_ADDRESS: "confluent-kafka-tw-1.int.onemount.dev:9093,confluent-kafka-tw-2.int.onemount.dev:9093, confluent-kafka-tw-3.int.onemount.dev:9093,confluent-kafka-sg-4.int.onemount.dev:9093,confluent-kafka-sg-5.int.onemount.dev:9093,confluent-kafka-sg-6.int.onemount.dev:9093"
  KAFKA_OL_SERVICE_TRUSTSTORE_LOCATION: "./kafka/confluent-truststore.jks"
  KAFKA_OL_SERVICE_KEYSTORE_LOCATION: "./kafka/oneloyalty-common-clients.int.onemount.dev.jks"
  KAFKA_OL_SERVICE_CONSUMER_TOPIC: "oneloyalty-common-events-staging"
  KAFKA_OL_SERVICE_CONSUMER_GROUP: "oneloyalty-ps2-event-notification-worker-staging"
  KAFKA_OL_SERVICE_SSL_ENABLE: "true"
  KAFKA_OL_SERVICE_ENABLED: "true"
  KAFKA_TYPE: "INTERNAL"

  APP_PERFORMANCE_THREADS: "8"
  APP_PERFORMANCE_QUEUES: "2048"

  LISTENER_GROUP_ID: "ps2-event-transaction-listener-status-consume-group-staging"
  TOPIC_NAME_TRANSACTION_LISTENER_STATUS: "ps2-event-transaction-listener-status-staging"

  JAVA_OPT: |
    -javaagent:/monitor/opentelemetry-javaagent.jar -Dotel.service.name=$SERVICE_NAME -Dotel.metrics.exporter=none -Dotel.exporter.otlp.protocol=grpc -Dotel.resource.attributes=deployment.environment=${ENVIRONMENT} -Dotel.semconv-stability.opt-in=http -Dotel.instrumentation.micrometer.base-time-unit=s -Dotel.instrumentation.log4j-appender.experimental-log-attributes=true -Dotel.instrumentation.logback-appender.experimental-log-attributes=true -Dotel.exporter.otlp.endpoint=http://grafana-agent.monitoring.svc.cluster.local:4317
